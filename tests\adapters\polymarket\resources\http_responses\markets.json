{"data": [{"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 5, "minimum_tick_size": 0.001, "condition_id": "0x12a0cb60174abc437bf1178367c72d11f069e1a3add20b148fb0ab4279b772b2", "question_id": "0x71489e408cd07bfb310f01af681ed3503295ffefdb09a644c34fa3f9fe3f420c", "question": "[Single Market] Will <PERSON> win the U.S. 2024 Republican presidential nomination?", "description": "This market will resolve to “Yes” if <PERSON> wins the 2024 nomination of the Republican Party for U.S. president. Otherwise, this market will resolve to “No”.\n\nThe resolution source for this market will be a consensus of official GOP sources, including https://www.gop.com.\n\nAny replacement of the nominee before election day will not change the resolution of the market.", "market_slug": "will-ron-desantis-win-the-us-2024-republican-presidential-nomination", "end_date_iso": "2024-09-10T00:00:00Z", "game_start_time": null, "seconds_delay": 0, "fpmm": "0x84834141F76bDb7EE72A9E67Ca7Bd1e849288C3A", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/single-market-will-ron-desantis-win-the-us-2024-republican-presidential-nomination-d7f2b452-e8b6-4f5d-a82f-417c1eaa72e7.png", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/single-market-will-ron-desantis-win-the-us-2024-republican-presidential-nomination-d7f2b452-e8b6-4f5d-a82f-417c1eaa72e7.png", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "10715043962341292242163832255523703116280692082153281340642602592290195499485", "outcome": "Yes", "price": 0, "winner": false}, {"token_id": "80085489243215790077454313513728486445319747473476157602131800551595373270758", "outcome": "No", "price": 1, "winner": true}], "tags": ["Politics", "tim scott", "republican party", "v<PERSON><PERSON>", "republicans", "2024 presidential election", "u.s. 2024 elections", "<PERSON>", "u.s. 2024 republican presidential nomination", "Elections", "ron desantis", "u.s. presidential election", "ben sha<PERSON><PERSON>", "2024 election", "joe rogan", "kanye west", "u.s. politics", "presidential nomination", "donald trump jr.", "presidential election 2024", "glenn youngkin", "chris christie", "mike pence", "peter thiel", "u.s. presidency", "2024 republican presidential nomination", "All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 5, "minimum_tick_size": 0.001, "condition_id": "0x41190eb9336ae73949c04f4900f9865092e69a57cf9c942a6157abf6ae8d16c6", "question_id": "0xb8f7299902480dc5261333eacfcd304b5a6988a36693bed032b36f92676d2c91", "question": "[Single Market] Will <PERSON> win the U.S. 2024 Republican presidential nomination?", "description": "This market will resolve to “Yes” if <PERSON> wins the 2024 nomination of the Republican Party for U.S. president. Otherwise, this market will resolve to “No”.\n\nThe resolution source for this market will be a consensus of official GOP sources, including https://www.gop.com.\n\nAny replacement of the nominee before election day will not change the resolution of the market.", "market_slug": "will-donald-j-trump-win-the-us-2024-republican-presidential-nomination-1", "end_date_iso": "2024-09-10T00:00:00Z", "game_start_time": null, "seconds_delay": 0, "fpmm": "0xf810652Ca2F32CECF67c71adFB534b98B567F344", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/single-market-will-donald-j-trump-win-the-us-2024-republican-presidential-nomination-0be7f5d6-43f6-4d33-9ae5-fea3d780d6ea.png", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/single-market-will-donald-j-trump-win-the-us-2024-republican-presidential-nomination-0be7f5d6-43f6-4d33-9ae5-fea3d780d6ea.png", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "65818619657568813474341868652308942079804919287380422192892211131408793125422", "outcome": "Yes", "price": 1, "winner": true}, {"token_id": "7499310772839000939827460818108209122328490677343888452292252718053799772723", "outcome": "No", "price": 0, "winner": false}], "tags": ["Politics", "tim scott", "republican party", "v<PERSON><PERSON>", "republicans", "2024 presidential election", "u.s. 2024 elections", "<PERSON>", "u.s. 2024 republican presidential nomination", "Elections", "ron desantis", "u.s. presidential election", "ben sha<PERSON><PERSON>", "2024 election", "joe rogan", "kanye west", "u.s. politics", "presidential nomination", "donald trump jr.", "presidential election 2024", "glenn youngkin", "chris christie", "mike pence", "peter thiel", "u.s. presidency", "2024 republican presidential nomination", "All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 5, "minimum_tick_size": 0.001, "condition_id": "0x768603866bc5ce73836ccae47d72ee47cdd605d41320d3894ca41412f784d775", "question_id": "0x8bb29aaff85327cf495d8b525abf936f512d0756def375839f288e16f18f1f3b", "question": "[Single Market] Will <PERSON> win the U.S. 2024 Republican presidential nomination?", "description": "This market will resolve to “Yes” if <PERSON><PERSON><PERSON> wins the 2024 nomination of the Republican Party for U.S. president. Otherwise, this market will resolve to “No”.\n\nThe resolution source for this market will be a consensus of official GOP sources, including https://www.gop.com.\n\nAny replacement of the nominee before election day will not change the resolution of the market.", "market_slug": "will-nikki-haley-win-the-us-2024-republican-presidential-nomination", "end_date_iso": "2024-09-10T00:00:00Z", "game_start_time": null, "seconds_delay": 0, "fpmm": "0x639C81634fDFFDc05E95234248766044287841e7", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/single-market-will-nikki-haley-win-the-us-2024-republican-presidential-nomination-a119dc96-80a7-48c3-9ba1-6d92314e8ec2.png", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/single-market-will-nikki-haley-win-the-us-2024-republican-presidential-nomination-a119dc96-80a7-48c3-9ba1-6d92314e8ec2.png", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "35557859467763341635362789570856827321272210357501754618844815551576922983291", "outcome": "Yes", "price": 0, "winner": false}, {"token_id": "78118529524639173700167934778533202620053845007966912944800512231568696242280", "outcome": "No", "price": 1, "winner": true}], "tags": ["Politics", "tim scott", "republican party", "v<PERSON><PERSON>", "republicans", "2024 presidential election", "u.s. 2024 elections", "<PERSON>", "u.s. 2024 republican presidential nomination", "Elections", "ron desantis", "u.s. presidential election", "ben sha<PERSON><PERSON>", "2024 election", "joe rogan", "kanye west", "u.s. politics", "presidential nomination", "donald trump jr.", "presidential election 2024", "glenn youngkin", "chris christie", "mike pence", "peter thiel", "u.s. presidency", "2024 republican presidential nomination", "All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 5, "minimum_tick_size": 0.001, "condition_id": "0xf5875410202b3545491774ab5e712a6e05a0ffe780c52270cc8d70cc95164411", "question_id": "0x06a4bcc28c8b76961ed6caa7de6e103d3b4cc8a539ebb376fc970928199e5c18", "question": "[Single Market] Will <PERSON> win the U.S. 2024 Democratic presidential nomination?", "description": "This market will resolve to “Yes” if <PERSON> wins the 2024 nomination of the Democratic Party for U.S. president. Otherwise, this market will resolve to “No”.\n\nThe resolution source for this market will be a consensus of official Democratic Party sources, including https://democrats.org/.\n\nAny replacement of the nominee before election day will not change the resolution of the market.", "market_slug": "will-joe-biden-win-the-us-2024-democratic-presidential-nomination", "end_date_iso": "2024-08-10T00:00:00Z", "game_start_time": null, "seconds_delay": 0, "fpmm": "0x5Ce0c9cd0f79b711Bdaa8287B0e8540C02D824c5", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/will-joe-biden-win-the-2024-us-presidential-election-7eba08de-3d3a-41f1-95f6-fcf36bad1855.png", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/will-joe-biden-win-the-2024-us-presidential-election-7eba08de-3d3a-41f1-95f6-fcf36bad1855.png", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "72044801958932376213942522155627434496146777860490700435168293831379994699414", "outcome": "Yes", "price": 0, "winner": false}, {"token_id": "60777290337556307846082122611643867373415691927705756558171303096586770149710", "outcome": "No", "price": 1, "winner": true}], "tags": ["Politics", "<PERSON>", "2024 presidential election", "democratic party", "us elections", "gavin newsom", "u.s. presidential election", "2024 election", "u.s. politics", "presidential nomination", "hillary clinton", "kamala harris", "democrats", "u.s. 2024", "democratic presidential nomination", "dean phillips", "u.s. presidential election 2024", "robert f. kennedy jr.", "u.s. election", "pete buttigi<PERSON>", "elections 2024", "All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 5, "minimum_tick_size": 0.001, "condition_id": "0x3c1b76f77f93f19371d3df801047c0fba2e6ca4c4bd489dbabea990ea68f8941", "question_id": "0xb65e19af2c68cddff89116b6825949db83b20e80818020a84c43fc6f0be6b663", "question": "[Single Market] Will <PERSON><PERSON> win the U.S. 2024 Democratic presidential nomination?", "description": "This market will resolve to “Yes” if <PERSON><PERSON> wins the 2024 nomination of the Democratic Party for U.S. president. Otherwise, this market will resolve to “No”.\n\nThe resolution source for this market will be a consensus of official Democratic Party sources, including https://democrats.org/.\n\nAny replacement of the nominee before election day will not change the resolution of the market.", "market_slug": "will-kamala-harris-win-the-us-2024-democratic-presidential-nomination", "end_date_iso": "2024-08-10T00:00:00Z", "game_start_time": null, "seconds_delay": 0, "fpmm": "0xe553F23F6cE516787AC56d3542eA75Ce526416c2", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/single-market-will-kamala-harris-win-the-us-2024-democratic-presidential-nomination-a7ead023-6892-4278-893d-03e98b29ac02.png", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/single-market-will-kamala-harris-win-the-us-2024-democratic-presidential-nomination-a7ead023-6892-4278-893d-03e98b29ac02.png", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "55587534433102763859032443585846918727132208718352714378810158969375734322181", "outcome": "Yes", "price": 1, "winner": true}, {"token_id": "111837615529992437689409005124251998174169394377496834222265889069615714482973", "outcome": "No", "price": 0, "winner": false}], "tags": ["Politics", "2024 presidential election", "democratic party", "us elections", "gavin newsom", "u.s. presidential election", "2024 election", "u.s. politics", "presidential nomination", "hillary clinton", "kamala harris", "democrats", "u.s. 2024", "democratic presidential nomination", "dean phillips", "u.s. presidential election 2024", "robert f. kennedy jr.", "u.s. election", "pete buttigi<PERSON>", "elections 2024", "All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 5, "minimum_tick_size": 0.001, "condition_id": "0x84dfb8b5cac6356d4ac7bb1da55bb167d0ef65d06afc2546389630098cc467e9", "question_id": "0x73c7bcb266a34398200159da18d4bf95044e64e5d3c2e7a58db0f272aa46f396", "question": "[Single Market] Will <PERSON> win the U.S. 2024 Democratic presidential nomination?", "description": "This market will resolve to “Yes” if <PERSON> wins the 2024 nomination of the Democratic Party for U.S. president. Otherwise, this market will resolve to “No”.\n\nThe resolution source for this market will be a consensus of official Democratic Party sources, including https://democrats.org/.\n\nAny replacement of the nominee before election day will not change the resolution of the market.", "market_slug": "will-pete-buttigieg-win-the-us-2024-democratic-presidential-nomination", "end_date_iso": "2024-08-10T00:00:00Z", "game_start_time": null, "seconds_delay": 0, "fpmm": "0x49c5078E0BB883cAe3C2089Bf591eD189c7dCca1", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/single-market-will-pete-buttigieg-win-the-us-2024-democratic-presidential-nomination-75607a85-7b2f-4f57-8138-6b1556d8860d.png", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/single-market-will-pete-buttigieg-win-the-us-2024-democratic-presidential-nomination-75607a85-7b2f-4f57-8138-6b1556d8860d.png", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "113459643164060403919504712048530019379173768880231414404726414035517357828194", "outcome": "Yes", "price": 0, "winner": false}, {"token_id": "110606609354376812966157988257545578031088805684265956480094769102094200583475", "outcome": "No", "price": 1, "winner": true}], "tags": ["Politics", "2024 presidential election", "democratic party", "us elections", "gavin newsom", "u.s. presidential election", "2024 election", "u.s. politics", "presidential nomination", "hillary clinton", "kamala harris", "democrats", "u.s. 2024", "democratic presidential nomination", "dean phillips", "u.s. presidential election 2024", "robert f. kennedy jr.", "u.s. election", "pete buttigi<PERSON>", "elections 2024", "All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 5, "minimum_tick_size": 0.001, "condition_id": "0x08fbe3be22b176f9fd7a915ccd5b23470d6671747a05a4500346897f2834113e", "question_id": "0xf479d8b665eb526fa8d467fb07f3229a54d565026000ae1685fc975157bd3eb6", "question": "[Single Market] Will <PERSON> win the U.S. 2024 Democratic presidential nomination?", "description": "This market will resolve to “Yes” if <PERSON> wins the 2024 nomination of the Democratic Party for U.S. president. Otherwise, this market will resolve to “No”.\n\nThe resolution source for this market will be a consensus of official Democratic Party sources, including https://democrats.org/.\n\nAny replacement of the nominee before election day will not change the resolution of the market.", "market_slug": "will-hillary-clinton-win-the-us-2024-democratic-presidential-nomination", "end_date_iso": "2024-08-10T00:00:00Z", "game_start_time": null, "seconds_delay": 0, "fpmm": "0x233c912cA6b86B4D6ae3c9d514eFE2dD51745f04", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/single-market-will-hillary-clinton-win-the-us-2024-democratic-presidential-nomination-ef06a6db-e778-4ade-b5e4-f757d43b34eb.png", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/single-market-will-hillary-clinton-win-the-us-2024-democratic-presidential-nomination-ef06a6db-e778-4ade-b5e4-f757d43b34eb.png", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "50543573631414368777068097718195199982611650330220913106287841507687662300122", "outcome": "Yes", "price": 0, "winner": false}, {"token_id": "96571759762520201953297957414008200233885051464934470471783087076893409728238", "outcome": "No", "price": 1, "winner": true}], "tags": ["Politics", "2024 presidential election", "democratic party", "us elections", "gavin newsom", "u.s. presidential election", "2024 election", "u.s. politics", "presidential nomination", "hillary clinton", "kamala harris", "democrats", "u.s. 2024", "democratic presidential nomination", "dean phillips", "u.s. presidential election 2024", "robert f. kennedy jr.", "u.s. election", "pete buttigi<PERSON>", "elections 2024", "All"]}, {"enable_order_book": true, "active": true, "closed": false, "archived": false, "accepting_orders": true, "accepting_order_timestamp": null, "minimum_order_size": 5, "minimum_tick_size": 0.01, "condition_id": "0x26ee82bee2493a302d21283cb578f7e2fff2dd15743854f53034d12420863b55", "question_id": "0x22b180e61b0628d5a2c3fc05ad0e3bf19a499c492e4e620178519cdea1dacec2", "question": "Which party wins 2024 US Presidential Election?", "description": "The 2024 United States presidential election will be held on Tuesday, November 5, 2024.\n\nThis market will resolve to the political party whose candidate is elected the next President of the United States in the 2024 election.\n\nThe main resolution sources for this market will be  (https://www.archives.gov/electoral-college/results), (http://whitehouse.gov/) and (https://www.fec.gov/). Further official, judicial or other relevant announcements, reports or decisions may also be considered to resolve any ambiguity or uncertainty before the market is settled.\n\nIf another party wins other than Democratic or Republican, the market will resolve 50/50.", "market_slug": "which-party-will-win-the-2024-united-states-presidential-election", "end_date_iso": "2024-11-08T00:00:00Z", "game_start_time": null, "seconds_delay": 0, "fpmm": "0xcC3BAd8e848bFBbAbD598B48Bd060Cc7DBAEf7Ba", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/which-party-will-win-the-2024-united-states-presidential-election-IMmRIV7YSUE_.jpg", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/which-party-will-win-the-2024-united-states-presidential-election-IMmRIV7YSUE_.jpg", "rewards": {"rates": [{"asset_address": "0x2791Bca1f2de4661ED88A30C99A7a9449Aa84174", "rewards_daily_rate": 75}], "min_size": 200, "max_spread": 1.5}, "is_50_50_outcome": false, "tokens": [{"token_id": "11015470973684177829729219287262166995141465048508201953575582100565462316088", "outcome": "Democratic", "price": 0.545, "winner": false}, {"token_id": "65444287174436666395099524416802980027579283433860283898747701594488689243696", "outcome": "Republican", "price": 0.455, "winner": false}], "tags": ["Politics", "US Election", "2024 presidential election", "Elections", "united states", "All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 5, "minimum_tick_size": 0.001, "condition_id": "0xd93cb2d70e5e00e804ff2115f59aaed64cffe4af275264e9a629e1e4ad3dab80", "question_id": "0xfbc7f15b6ec0198a30907ba696a6a089ffb10ea6fd12a63932ed077227b2d3e7", "question": "[Single Market] Will <PERSON> win the U.S. 2024 Republican presidential nomination?", "description": "This market will resolve to “Yes” if <PERSON> wins the 2024 nomination of the Republican Party for U.S. president. Otherwise, this market will resolve to “No”.\n\nThe resolution source for this market will be a consensus of official GOP sources, including https://www.gop.com.\n\nAny replacement of the nominee before election day will not change the resolution of the market.", "market_slug": "will-tucker-carlson-win-the-us-2024-republican-presidential-nomination", "end_date_iso": "2024-09-10T00:00:00Z", "game_start_time": null, "seconds_delay": 0, "fpmm": "0xE56757FF1964C6B4C80b563b445a944D663c001C", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/single-market-will-tucker-car<PERSON>-win-the-us-2024-republican-presidential-nomination-21edc693-7091-4042-aecd-5aabaac4e22e.png", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/single-market-will-tucker-car<PERSON>-win-the-us-2024-republican-presidential-nomination-21edc693-7091-4042-aecd-5aabaac4e22e.png", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "26833757084348273199149103131128190314257933645647871320641517705324495047529", "outcome": "Yes", "price": 0, "winner": false}, {"token_id": "36354257386096345661488456060615987003496513249783340916004886692219239828930", "outcome": "No", "price": 1, "winner": true}], "tags": ["Politics", "tim scott", "republican party", "v<PERSON><PERSON>", "republicans", "2024 presidential election", "u.s. 2024 elections", "<PERSON>", "u.s. 2024 republican presidential nomination", "Elections", "ron desantis", "u.s. presidential election", "ben sha<PERSON><PERSON>", "2024 election", "joe rogan", "kanye west", "u.s. politics", "presidential nomination", "donald trump jr.", "presidential election 2024", "glenn youngkin", "chris christie", "mike pence", "peter thiel", "u.s. presidency", "2024 republican presidential nomination", "All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 5, "minimum_tick_size": 0.001, "condition_id": "0xb03b50581d3ed24f8d40c3bc3ca235fc82ba86eaafc2aed2a9fb85a47402a96c", "question_id": "0x893c4775cf54b9210dc3747fb63adf7c7390f60708accf3bc2258b0ee1a159ef", "question": "[Single Market] Will <PERSON> win the U.S. 2024 Republican presidential nomination?", "description": "This market will resolve to “Yes” if <PERSON> wins the 2024 nomination of the Republican Party for U.S. president. Otherwise, this market will resolve to “No”.\n\nThe resolution source for this market will be a consensus of official GOP sources, including https://www.gop.com.\n\nAny replacement of the nominee before election day will not change the resolution of the market.", "market_slug": "will-ben-sha<PERSON><PERSON>-win-the-us-2024-republican-presidential-nomination", "end_date_iso": "2024-09-10T00:00:00Z", "game_start_time": null, "seconds_delay": 0, "fpmm": "0x3C99DdD6da6Ed8b6878aaCa17b1F98a9cb7a995D", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/ben+shapiro.png", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/ben+shapiro.png", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "106442146417353994549341768954662846308837575744382260069699165928041680837679", "outcome": "Yes", "price": 0, "winner": false}, {"token_id": "39961381562395550166279034811191512070049464060698301636499886232973630461257", "outcome": "No", "price": 1, "winner": true}], "tags": ["Politics", "tim scott", "republican party", "v<PERSON><PERSON>", "republicans", "2024 presidential election", "u.s. 2024 elections", "<PERSON>", "u.s. 2024 republican presidential nomination", "Elections", "ron desantis", "u.s. presidential election", "ben sha<PERSON><PERSON>", "2024 election", "joe rogan", "kanye west", "u.s. politics", "presidential nomination", "donald trump jr.", "presidential election 2024", "glenn youngkin", "chris christie", "mike pence", "peter thiel", "u.s. presidency", "2024 republican presidential nomination", "All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 5, "minimum_tick_size": 0.001, "condition_id": "0x7e912d337aec54d321a4d415bd05e5a6676c3f04617f07def1281be7e0af3e91", "question_id": "0x4951ee1639f93bc65d014e154474fb79416624c37cfe52681a155eb6a8e2869e", "question": "[Single Market] Will <PERSON> win the U.S. 2024 Republican presidential nomination?", "description": "This market will resolve to “Yes” if <PERSON> wins the 2024 nomination of the Republican Party for U.S. president. Otherwise, this market will resolve to “No”.\n\nThe resolution source for this market will be a consensus of official GOP sources, including https://www.gop.com.\n\nAny replacement of the nominee before election day will not change the resolution of the market.", "market_slug": "will-joe-rogan-win-the-us-2024-republican-presidential-nomination", "end_date_iso": "2024-09-10T00:00:00Z", "game_start_time": null, "seconds_delay": 0, "fpmm": "0x5E9A3e7f7F8398D26293D7A36408E3339c50e698", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/single-market-will-joe-rogan-win-the-us-2024-republican-presidential-nomination-6705082a-6e61-4068-b86d-bf46f84df453.png", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/single-market-will-joe-rogan-win-the-us-2024-republican-presidential-nomination-6705082a-6e61-4068-b86d-bf46f84df453.png", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "26174674438429794193834912492739683499712017911436124046705415717174721343905", "outcome": "Yes", "price": 0, "winner": false}, {"token_id": "17626058909938167942937469946237823191635874917900993684453437908173602255356", "outcome": "No", "price": 1, "winner": true}], "tags": ["Politics", "tim scott", "republican party", "v<PERSON><PERSON>", "republicans", "2024 presidential election", "u.s. 2024 elections", "<PERSON>", "u.s. 2024 republican presidential nomination", "Elections", "ron desantis", "u.s. presidential election", "ben sha<PERSON><PERSON>", "2024 election", "joe rogan", "kanye west", "u.s. politics", "presidential nomination", "donald trump jr.", "presidential election 2024", "glenn youngkin", "chris christie", "mike pence", "peter thiel", "u.s. presidency", "2024 republican presidential nomination", "All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 5, "minimum_tick_size": 0.001, "condition_id": "0x49c2bb91bcb33296f486c92a553c2fca81cfe79f4e9ba00cca68b5ca274e914f", "question_id": "0x05451339c17d909ecf7cb744aaa240fa33bd51a363ed1f743face2e9848f1aaf", "question": "[Single Market] Will <PERSON><PERSON><PERSON> win the U.S. 2024 Republican presidential nomination?", "description": "This market will resolve to “Yes” if <PERSON><PERSON><PERSON>\nwins the 2024 nomination of the Republican Party for U.S. president. Otherwise, this market will resolve to “No”.\n\nThe resolution source for this market will be a consensus of official GOP sources, including https://www.gop.com.\n\nAny replacement of the nominee before election day will not change the resolution of the market.", "market_slug": "will-kanye-west-win-the-us-2024-republican-presidential-nomination", "end_date_iso": "2024-09-10T00:00:00Z", "game_start_time": null, "seconds_delay": 0, "fpmm": "0x3640fED9FA5f3EE361673041a03c83FA47911cDE", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/single-market-will-kanye-west-win-the-us-2024-republican-presidential-nomination-873a389c-2009-413c-aa66-0743dbdfe66e.png", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/single-market-will-kanye-west-win-the-us-2024-republican-presidential-nomination-873a389c-2009-413c-aa66-0743dbdfe66e.png", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "44412827116867851857888342652300469470699695641913209573689466108958588345876", "outcome": "Yes", "price": 0, "winner": false}, {"token_id": "87508300922072948504644627375052680275959171582701244894747032869704225334739", "outcome": "No", "price": 1, "winner": true}], "tags": ["Politics", "tim scott", "republican party", "v<PERSON><PERSON>", "republicans", "2024 presidential election", "u.s. 2024 elections", "<PERSON>", "u.s. 2024 republican presidential nomination", "Elections", "ron desantis", "u.s. presidential election", "ben sha<PERSON><PERSON>", "2024 election", "joe rogan", "kanye west", "u.s. politics", "presidential nomination", "donald trump jr.", "presidential election 2024", "glenn youngkin", "chris christie", "mike pence", "peter thiel", "u.s. presidency", "2024 republican presidential nomination", "All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 5, "minimum_tick_size": 0.001, "condition_id": "0x311198b7194c8eb1890df4d3a2e5171a313066bba76b0bdb431af757eeaf4e51", "question_id": "0xb08d8c23b3ce936fbfed0cfb05dc7b4c6a9ba011f6961ec5d1c796572271e59c", "question": "[Single Market] Will <PERSON> win the U.S. 2024 Republican presidential nomination?", "description": "This market will resolve to “Yes” if <PERSON> wins the 2024 nomination of the Republican Party for U.S. president. Otherwise, this market will resolve to “No”.\n\nThe resolution source for this market will be a consensus of official GOP sources, including https://www.gop.com.\n\nAny replacement of the nominee before election day will not change the resolution of the market.", "market_slug": "will-peter-thiel-win-the-us-2024-republican-presidential-nomination", "end_date_iso": "2024-09-10T00:00:00Z", "game_start_time": null, "seconds_delay": 0, "fpmm": "0xb5F9f5DBF1DB6A6FD506C653bba20D1EC0F0A546", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/peter+thiel.png", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/peter+thiel.png", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "111350394483029570823157092176785817077270431134711686441545759038263842946639", "outcome": "Yes", "price": 0, "winner": false}, {"token_id": "78030959962391034526268729499101857894241584078023018935559096152748585715940", "outcome": "No", "price": 1, "winner": true}], "tags": ["Politics", "tim scott", "republican party", "v<PERSON><PERSON>", "republicans", "2024 presidential election", "u.s. 2024 elections", "<PERSON>", "u.s. 2024 republican presidential nomination", "Elections", "ron desantis", "u.s. presidential election", "ben sha<PERSON><PERSON>", "2024 election", "joe rogan", "kanye west", "u.s. politics", "presidential nomination", "donald trump jr.", "presidential election 2024", "glenn youngkin", "chris christie", "mike pence", "peter thiel", "u.s. presidency", "2024 republican presidential nomination", "All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 5, "minimum_tick_size": 0.001, "condition_id": "0x6898b869f0bb1f31d309897956fab3437a7e82408cb91d4bfc888dbf52e259a8", "question_id": "0xd081f2072756c92a7d5c9a89a304b03a38e213715936ea5e289ee7a5f84898fe", "question": "[Single Market] Will <PERSON> win the U.S. 2024 Republican presidential nomination?", "description": "This market will resolve to “Yes” if <PERSON> wins the 2024 nomination of the Republican Party for U.S. president. Otherwise, this market will resolve to “No”.\n\nThe resolution source for this market will be a consensus of official GOP sources, including https://www.gop.com.\n\nAny replacement of the nominee before election day will not change the resolution of the market.", "market_slug": "will-donald-trump-jr-win-the-us-2024-republican-presidential-nomination", "end_date_iso": "2024-09-10T00:00:00Z", "game_start_time": null, "seconds_delay": 0, "fpmm": "0xe033f9121360B38e5d3170359532BB8Ce0CAE3f9", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/single-market-will-donald-trump-jr-win-the-us-2024-republican-presidential-nomination-939da30f-22c5-457a-abe2-a77cabcf5b51.png", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/single-market-will-donald-trump-jr-win-the-us-2024-republican-presidential-nomination-939da30f-22c5-457a-abe2-a77cabcf5b51.png", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "84434372545159691527242186470863982198611208256471183237663485790939159450270", "outcome": "Yes", "price": 0, "winner": false}, {"token_id": "13543391645147872104310669434084191645666247218622982123811573991258456327650", "outcome": "No", "price": 1, "winner": true}], "tags": ["Politics", "tim scott", "republican party", "v<PERSON><PERSON>", "republicans", "2024 presidential election", "u.s. 2024 elections", "<PERSON>", "u.s. 2024 republican presidential nomination", "Elections", "ron desantis", "u.s. presidential election", "ben sha<PERSON><PERSON>", "2024 election", "joe rogan", "kanye west", "u.s. politics", "presidential nomination", "donald trump jr.", "presidential election 2024", "glenn youngkin", "chris christie", "mike pence", "peter thiel", "u.s. presidency", "2024 republican presidential nomination", "All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0x98c01e1f5053e57fcb59add64b9c0f74ac784609b80ab1a8754e934878468310", "question_id": "0xd2e31a19272c7e9637783dd111e2b6bc3865d4a02be7fe8b28a56aa496ff56f6", "question": "Will the Ethereum Merge (EIP-3675) occur by September 1, 2022?", "description": "This is a market on if The Merge will occur on the Ethereum mainnet as described in EIP-3675 (or any successor to EIP-3675) by the resolution time, September 1 2022, 11:59:59 PM ET, transitioning the Ethereum blockchain to proof-of-stake.\n\nIf the first proof-of-stake block (defined in EIP-3675 as TRANSITION_BLOCK) is produced before the resolution time, this market will resolve to \"Yes\". \nOtherwise, the market will resolve \"No\".\n\nNote, that forks to the execution layer and the consensus layer implementing EIP-3675 will not have any impact on the market resolution. Only the actual occurrence of The Merge will be considered.", "market_slug": "will-the-ethereum-merge-eip-3675-occur-by-september-1-2022", "end_date_iso": "2022-09-01T00:00:00Z", "game_start_time": "2022-09-01T07:00:00Z", "seconds_delay": 0, "fpmm": "******************************************", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/will-the-ethereum-merge-eip-3675-occur-by-september-1-2022-901f2434-6331-4e30-9bb5-ef563e007813.png", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/will-the-ethereum-merge-eip-3675-occur-by-september-1-2022-901f2434-6331-4e30-9bb5-ef563e007813.png", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "58410023360445133337002742789209293559332988923893081558084399803488340569229", "outcome": "Yes", "price": 0, "winner": false}, {"token_id": "50020510440737250328008159708072591412893525445726218563056023860740951051234", "outcome": "No", "price": 1, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": false, "closed": false, "archived": true, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 0, "minimum_tick_size": 0.01, "condition_id": "0x5374183ab5a2925fda4e7ac2db4848499458224ce2d73c16d3f248951c80746e", "question_id": "0x9b01cdef998f33707fd23bf01531b5963b1a5a989e1fc3f20867e8bf4e279ce3", "question": "Will the clob strapi integration work?", "description": "This is a test market.", "market_slug": "will-the-clob-strapi-integration-work", "end_date_iso": "2022-02-19T00:00:00Z", "game_start_time": null, "seconds_delay": 0, "fpmm": "0x3F0A3eC9F4f87aFedAdd2095d5836234b4530f0D", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "", "image": "", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "", "outcome": "", "price": 0, "winner": false}, {"token_id": "", "outcome": "", "price": 0, "winner": false}], "tags": null}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0x0ea37da314e0fd0b6abb8068db294c49ffa81774904e7758e9f2c933d1d29090", "question_id": "0x4c8fe9dbe11a4bedd469ff589ecc85ca79b551aa8a2d8d5b37d9227767e11d4d", "question": "Who will win the $1M bet on LUNA's price being over $92.40: <PERSON> or <PERSON><PERSON>?", "description": "On March 14, 2022, <PERSON> (@stablekwon) and <PERSON><PERSON> (@AlgodTrading) made a bet on whether the price of Terra (LUNA) would be lower on March 14, 2023 than it was on March 14, 2022. \n\nEach party put up 1 million, to an escrow wallet owned by @cobie. Do Kwon bet LUNA would be $92.40 or higher on March 14, 2023, and Sensei Algod bet LUNA would be lower than $92.40.\n\nThis market will resolve to “Do Kwon” if <PERSON> wins the bet, or “Sensei Algod” if Sense<PERSON> Algod wins the bet.\n\nIf for whatever reason the winner of the bet isn’t decided by April 1, 2023, or is ambiguous (e.g. the result isn't agreed to by both parties and @cobie), LUNA’s 24h average price on March 14, 2023, ET will be checked. If it is 92.40$ or above, the market will resolve to “Do Kwon”, otherwise it will resolve to “Sensei Algod”.\n\nThe 24h price will be calculated from Coingecko (https://www.coingecko.com/en/coins/terra-luna), by averaging the close price of all forty-eight 30 minute candles for the timeframe March 14 12:00:00 AM ET to March 15 12:00:00 AM ET. If Coingecko’s relevant candles are unavailable, another credible source will be used.", "market_slug": "who-will-win-the-1m-bet-on-terras-luna-price-being-over-92pt40-do-kwon-or-algod", "end_date_iso": "2023-03-14T00:00:00Z", "game_start_time": null, "seconds_delay": 0, "fpmm": "0xa2BAcc20a7820d2ab35e3229a433f4cccb0CB3b3", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/who-will-win-the-1m-bet-on-lunas-price-being-over-9240-do-kwon-or-sensei-algod-ec8dc082-095c-43a0-8e7b-e335ecbdd9e8.png", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/who-will-win-the-1m-bet-on-lunas-price-being-over-9240-do-kwon-or-sensei-algod-ec8dc082-095c-43a0-8e7b-e335ecbdd9e8.png", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "10748995181593457604578084084863969524423821326200131005262948389275614744679", "outcome": "<PERSON>", "price": 0, "winner": false}, {"token_id": "61640712385864017864437038967794164168192471114650819064852913187991054367478", "outcome": "<PERSON><PERSON>", "price": 1, "winner": true}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0xf6024bc223ad8abf050aace07fd3a69328f1bba63c3ffc98062d2420a2030a1e", "question_id": "0x463a64bcd15d94d5df2d8e1a5a9abbea3f57ba986881cd734c7217581c8ddeb0", "question": "Will the Ethereum Merge (EIP-3675) occur by October 1, 2022?", "description": "This is a market on if The Merge will occur on the Ethereum mainnet as described in EIP-3675 (or any successor to EIP-3675) by the resolution time, October 1 2022, 11:59:59 PM ET, transitioning the Ethereum blockchain to proof-of-stake.\n\nIf the first proof-of-stake block (defined in EIP-3675 as TRANSITION_BLOCK) is produced before the resolution time, this market will resolve to \"Yes\". \nOtherwise, the market will resolve \"No\".\n\nNote, that forks to the execution layer and the consensus layer implementing EIP-3675 will not have any impact on the market resolution. Only the actual occurrence of The Merge will be considered.", "market_slug": "will-the-ethereum-merge-eip-3675-occur-by-october-1-2022", "end_date_iso": "2022-10-01T00:00:00Z", "game_start_time": "2022-10-01T07:00:00Z", "seconds_delay": 0, "fpmm": "******************************************", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "", "image": "", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "14407587149607649930148663675171068494265586931331224774282394443605115689380", "outcome": "Yes", "price": 1, "winner": false}, {"token_id": "106312965725639963065012626780222052162524895406036255682293803400206316447336", "outcome": "No", "price": 0, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0xc06f6a5aab8993800921cc56ce8cd6e2ab5cadd2393038b5ea2d9416eb9d69f8", "question_id": "0x6af445e08de0e8795366d4f00f35b2375902a246788717258dd79fff0ebf514a", "question": "Will <PERSON> be elected Speaker of the House in the 118th Congress?", "description": "The midterm US elections scheduled for November 8, 2022, are expected to be contentious, with a strong possibility of the Republicans taking the House and Senate. If the Republicans take the US House of Representatives, the present Speaker of the House, Democrat <PERSON>, may be replaced by the Republican who wins a roll call election held by House members.\n\nThe 2022 midterm federal election is scheduled for November 8, 2022. The 118th Congress is scheduled to be sworn in on January 3, 2023.\n\nThis market will resolve to \"Yes\" if the Member of the US House of Representatives from California <PERSON> becomes the first-elected Speaker of the House in the 118th United States Congress. Otherwise, this market will resolve to \"No\".\n\nAny individual elected to be, appointed to be, or serving as Speaker pro tempore in the 118th congress will have no bearing on the resolution of this market. Only the first-elected Speaker of the House will count toward the resolution of this market.\n\nIf the election for the first Speaker for the 118th Congress is not finalized by March 3, 2023, 11:59:59 PM ET, this market will resolve to 50-50.\n\nThe primary resolution source for this market will be official information from the government of the United States of America (ex: https://www.speaker.gov/, https://www.house.gov/), however credible reporting may be used.", "market_slug": "will-kevin-mccarthy-be-elected-speaker-of-the-house-in-the-118th-congress", "end_date_iso": "2023-01-04T00:00:00Z", "game_start_time": null, "seconds_delay": 0, "fpmm": "0xE96fd5DE84D1925B3d0dB82d194b96f049E731f5", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/kevin+mccarthy.png\n", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/kevin+mccarthy.png\n", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "19757598438830365661430080766752069548925267193232545779097343040090374037614", "outcome": "Yes", "price": 1, "winner": false}, {"token_id": "21592895070735420973019757199011663907650462290276867724996288633023114055240", "outcome": "No", "price": 0, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0xc27fc10fe7d24c1fb22607a15b90f26c116dd08a2e96ef7becdad894cfd9ee89", "question_id": "0xa25f4e825e9543893b7cfdc8fc0d0c9aef78f479ee6c1ac4878c802b902e87e7", "question": "Will @realDonaldTrump tweet in 2022?", "description": "In Response to Trader Inquiry: Retweets do not count for this market's resolution.\n\nTwitter permanently banned former president of the USA, <PERSON>, from the platform in January 2021 during the final days of his term. His handle @realDonaldTrump had over 88.9 million followers.\n\nThis market will resolve to \"Yes\" if <PERSON>'s verified Twitter account (@realDonaldTrump) tweets at least once after May 10, 2022, and by December 31, 2022, (11:59:59 PM ET). Otherwise, this market will resolve to \"No\".\n\nThe resolution source for this market will be <PERSON>'s verified Twitter account: https://twitter.com/realdonaldtrump.\n\nPlease note, only the @realDonaldTrump verified Twitter account counts for this market, regardless of the URL for this profile. If <PERSON> tweets from another account, it has no bearing on the resolution of this market.", "market_slug": "will-realdonaldtrump-tweet-in-2022", "end_date_iso": "2022-12-31T00:00:00Z", "game_start_time": null, "seconds_delay": 0, "fpmm": "0x386FEB7679Ef63dEAe75aC3ECCf35195136360C7", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/TrumpTT.jpg", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/TrumpTT.jpg", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "102894439065827948528517742821392984534879642748379345137452173673949276014977", "outcome": "Yes", "price": 0, "winner": false}, {"token_id": "109116338599204431446574747251192624868451978658636298128248845415338473031066", "outcome": "No", "price": 1, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0x9bea26c852102a50ed736d54504de4683481bc89e2285ee7a52c890ec2e38e07", "question_id": "0x84c4e753adfcaca5e4cc99ecab7b60bab7204bdca2d6af418d53aa687b37f6f2", "question": "2022 NBA Finals: Who will win Celtics vs. Warriors Game 1?", "description": "The 2022 NBA Finals is the championship series of the National Basketball Association (NBA)'s 2021–22 season and conclusion of the season's playoffs. The finals follow a tournament format in a best-of-seven series. This season the finals are to be played between the Eastern Conference champions, the Boston Celtics, and the Western Conference champions, the Golden State Warriors.\n\nThis is a market on who will win Game 1 of the 2022 NBA Finals, a matchup scheduled for June 2, 2022 (9 PM ET).\n\nThis market will resolve to “Celtics” if the Boston Celtics win Game 1, and “Warriors” if the Golden State Warriors win. \n\nIf for any reason the winner of this game is not decided by June 30, 2022 (ET), this market will resolve 50-50.", "market_slug": "2022-nba-finals-who-will-win-celtics-vs-warriors-game-1", "end_date_iso": "2022-06-02T00:00:00Z", "game_start_time": "2022-06-03T01:00:00Z", "seconds_delay": 3, "fpmm": "0x4FF12E23099Af52824F4276D82eD35E7DE965b45", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/Logo+NBA.png", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/Logo+NBA.png", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "12960275576607793729646661472620486834369148340338148471631429834254768141476", "outcome": "Celtics", "price": 1, "winner": false}, {"token_id": "92929362297942057398510811139618404861144036309900638052084330236671411404294", "outcome": "Warriors", "price": 0, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0x069e4a36a50972cfb19800b373ff6013eb311c5db70ac18ce9970a5f9ffd617f", "question_id": "0x14fd431f6d7acf68a5b9c532831af7a10be4c229611ddb9b28719ac4c20782f7", "question": "2022 NBA Finals: Who will win Celtics vs. Warriors Game 2?", "description": "The 2022 NBA Finals is the championship series of the National Basketball Association (NBA)'s 2021–22 season and conclusion of the season's playoffs. The finals follow a tournament format in a best-of-seven series. This season the finals are to be played between the Eastern Conference champions, the Boston Celtics, and the Western Conference champions, the Golden State Warriors.\n\nThis is a market on who will win Game 2 of the 2022 NBA Finals, a matchup scheduled for June 5, 2022 (8 PM ET).\n\nThis market will resolve to “Celtics” if the Boston Celtics win Game 2, and “Warriors” if the Golden State Warriors win. \n\nIf for any reason the winner of this game is not decided by June 30, 2022 (ET), this market will resolve 50-50.", "market_slug": "2022-nba-finals-who-will-win-celtics-vs-warriors-game-2", "end_date_iso": "2022-06-05T00:00:00Z", "game_start_time": "2022-06-06T00:00:00Z", "seconds_delay": 0, "fpmm": "0xF632Dff0281900A97e4B133BA1d9796D7034142f", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/Logo+NBA.png", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/Logo+NBA.png", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "30724350426664936219265414656870586951199488588995870716360921518394717460090", "outcome": "Celtics", "price": 0, "winner": false}, {"token_id": "112332551192271528241223627392597772853707342912573873206096352970517077284341", "outcome": "Warriors", "price": 1, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0x8416006eded5102881349c5ab1e15d81de7496527915db59cc76a8df7f271d69", "question_id": "0xa23c09be8366e9b7a935ce93875ee0bd2fdae36f94215728ab9020729d39c03d", "question": "2022 NBA Finals: Who will win Celtics vs. Warriors Game 3?", "description": "The 2022 NBA Finals is the championship series of the National Basketball Association (NBA)'s 2021–22 season and conclusion of the season's playoffs. The finals follow a tournament format in a best-of-seven series. This season the finals are to be played between the Eastern Conference champions, the Boston Celtics, and the Western Conference champions, the Golden State Warriors.\n\nThis is a market on who will win Game 3 of the 2022 NBA Finals, a matchup scheduled for June 8, 2022 (9 PM ET).\n\nThis market will resolve to “Celtics” if the Boston Celtics win Game 3, and “Warriors” if the Golden State Warriors win. \n\nIf for any reason the winner of this game is not decided by June 30, 2022 (ET), this market will resolve 50-50.", "market_slug": "2022-nba-finals-who-will-win-celtics-vs-warriors-game-3", "end_date_iso": "2022-06-08T00:00:00Z", "game_start_time": "2022-06-09T01:00:00Z", "seconds_delay": 0, "fpmm": "0x11da1e903b15caEc7b1e1DEac76bd2dce9D0de61", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/Logo+NBA.png", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/Logo+NBA.png", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "20297601973574352236858165315609558710267507989674725146519713359591393106624", "outcome": "Celtics", "price": 1, "winner": false}, {"token_id": "48222966179613492854296204126295100650952865928263922076101654637330688677512", "outcome": "Warriors", "price": 0, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0x0de4ed9c811667ff9485e0f7aa3788a8db7c2147050f33defef2e2a302665433", "question_id": "0x9f9aa97d3b818387161a93d2fbe59f1642c87529839f393ca503e368800e665f", "question": "2022 NBA Finals: Who will win Celtics vs. Warriors Game 4?", "description": "The 2022 NBA Finals is the championship series of the National Basketball Association (NBA)'s 2021–22 season and conclusion of the season's playoffs. The finals follow a tournament format in a best-of-seven series. This season the finals are to be played between the Eastern Conference champions, the Boston Celtics, and the Western Conference champions, the Golden State Warriors.\n\nThis is a market on who will win Game 4 of the 2022 NBA Finals, a matchup scheduled for June 10, 2022 (9 PM ET).\n\nThis market will resolve to “Celtics” if the Boston Celtics win Game 4, and “Warriors” if the Golden State Warriors win. \n\nIf for any reason the winner of this game is not decided by June 30, 2022 (ET), this market will resolve 50-50.", "market_slug": "2022-nba-finals-who-will-win-celtics-vs-warriors-game-4", "end_date_iso": "2022-06-10T00:00:00Z", "game_start_time": null, "seconds_delay": 0, "fpmm": "0x44140477Eebf99286cAC5968B4c3E2Bdb5d4CC34", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/Logo+NBA.png", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/Logo+NBA.png", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "45119618568427259556353873688243668201774394578145125280438548676993229690946", "outcome": "Celtics", "price": 0, "winner": false}, {"token_id": "93862367603666595364600979608925062192225587043933771308112737299403278286433", "outcome": "Warriors", "price": 1, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0xe17a0a6cf7ba8037e7ff4d21aa52dad1225ceb688e5d7634581a33d470231b8a", "question_id": "0xe7b272baf4f85eef31e9f11191ff58b948e082a9b687879f0c4face41576a845", "question": "2022 NBA Finals: Who will win Celtics vs. Warriors Game 5?", "description": "The 2022 NBA Finals is the championship series of the National Basketball Association (NBA)'s 2021–22 season and the conclusion of the season's playoffs. The finals follow a tournament format in a best-of-seven series. This season the finals are to be played between the Eastern Conference champions, the Boston Celtics, and the Western Conference champions, the Golden State Warriors.\n\nThis is a market on who will win Game 5 of the 2022 NBA Finals, a matchup scheduled for June 13, 2022 (9 PM ET).\n\nThis market will resolve to “Celtics” if the Boston Celtics win Game 5, and “Warriors” if the Golden State Warriors win. \n\nIf for any reason the winner of this game is not decided by June 30, 2022 (ET), this market will resolve 50-50.", "market_slug": "2022-nba-finals-who-will-win-celtics-vs-warriors-game-5", "end_date_iso": "2022-06-13T00:00:00Z", "game_start_time": "2022-06-14T01:00:00Z", "seconds_delay": 3, "fpmm": "0x743Bb09A2FEd4614FFEDC62dD9f5dEe3AFD79847", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/image_https%3A//polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/Logo%2BNBA.png", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/image_https%3A//polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/Logo%2BNBA.png", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "80199677557395360296324475529876249074210649088740142116557046384871133150134", "outcome": "Celtics", "price": 0, "winner": false}, {"token_id": "43903000987912964081215550993010797446226024080508396845834510831958145464362", "outcome": "Warriors", "price": 1, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0x0c74513ab61479c191f59762a22c878a13f2c98959bac858260ef708d133c649", "question_id": "0xad0ce769a25c18e0f3197b83b644268c207ed9aa99f38f085163ba983e8c2715", "question": "Will $ETH be above $1,700 on June 17?", "description": "This is a market on if the price of Ethereum (ETH) will be above $1,700 in USD on June 17, 2022 (12 PM ET).\n\nThis market will resolve to “Yes” if Ethereum (ETH) has a candlestick closing price of $1,700.01 or more, as per the resolution source, on June 17, 2022, 12 PM ET, and “No” otherwise.\n\nThe resolution source for this market will be prices listed on CoinGecko (https://www.coingecko.com/en/coins/ethereum).\n\nThis market will resolve according to the “C” (i.e. closing price) listed for the candle titled “Fri 17 June 2022, 12:00:00”, with the “Price” tab selected, in the Eastern Time Zone. Note, this 12:00:00 candle lists the opening price for 11:30:00 AM ET and the closing price for 12:00:00 PM ET.\n\nThe check will be after the relevant candle closes. If CoinGecko is unavailable, another credible source will be chosen.", "market_slug": "will-eth-be-above-1700-on-june-17", "end_date_iso": "2022-06-17T00:00:00Z", "game_start_time": null, "seconds_delay": 0, "fpmm": "******************************************", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/image_https%3A//polymarket-upload.s3.us-east-2.amazonaws.com/icon_https%253A//polymarket-upload.s3.us-east-2.amazonaws.com/image_https%25253A//polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/eth%25252Blogo%25252Bchart.jpg", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/image_https%3A//polymarket-upload.s3.us-east-2.amazonaws.com/icon_https%253A//polymarket-upload.s3.us-east-2.amazonaws.com/image_https%25253A//polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/eth%25252Blogo%25252Bchart.jpg", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "62622436664750208568888566798553718938782739615670770257415971023909564156192", "outcome": "Yes", "price": 0, "winner": false}, {"token_id": "41327066895588116095670867119607897135150908983005604879264810872384952851855", "outcome": "No", "price": 1, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0x77d10d87c7cbe2f4df4705f51880b7ed71fa2778792679290e3cda7a1d9ada07", "question_id": "0xb7f27c243f3c15a6cfa67158f6ceae18f81068cbeef47a52d44262d6565e92e9", "question": "2022 NBA Finals: Who will win Celtics vs. Warriors Game 6?", "description": "The 2022 NBA Finals is the championship series of the National Basketball Association (NBA)'s 2021–22 season and the conclusion of the season's playoffs. The finals follow a tournament format in a best-of-seven series. This season the finals are to be played between the Eastern Conference champions, the Boston Celtics, and the Western Conference champions, the Golden State Warriors.\n\nThis is a market on who will win Game 6 of the 2022 NBA Finals, a matchup scheduled for June 16, 2022 (9 PM ET).\n\nThis market will resolve to “Celtics” if the Boston Celtics win Game 6, and “Warriors” if the Golden State Warriors win. \n\nIf for any reason the winner of this game is not decided by June 30, 2022 (ET), this market will resolve 50-50.", "market_slug": "2022-nba-finals-who-will-win-celtics-vs-warriors-game-6", "end_date_iso": "2022-06-16T00:00:00Z", "game_start_time": "2022-06-17T01:00:00Z", "seconds_delay": 3, "fpmm": "0x0752DBf0911a81bd8256e6d3b750f07F0487D680", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/image_https%3A//polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/Logo%2BNBA.png", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/image_https%3A//polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/Logo%2BNBA.png", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "14275762593227889655468694635600162816858355230314596757706203772408409537200", "outcome": "Celtics", "price": 0.0003, "winner": false}, {"token_id": "93437883880915183013981481485289991594383731341480721344311809902765137890222", "outcome": "Warriors", "price": 0.9997, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0x031182ee7bb83283b23f575feb615845cc96026492c5732bbd053145f24237c8", "question_id": "0x141927e9cd1419e022300dfe161bf6f3b90294a9066a0f7f851d8ab1f3e9dfd8", "question": "Will $ETH be above $1,200 on July 1?", "description": "This is a market on if the price of Ethereum (ETH) will be above $1,200 in USD on July 1, 2022 (12 PM ET).\n\nThis market will resolve to “Yes” if Ethereum (ETH) has a candlestick closing price of $1,200.01 or more, as per the resolution source, on July 1, 2022, 12 PM ET, and “No” otherwise.\n\nThe resolution source for this market will be prices listed on CoinGecko (https://www.coingecko.com/en/coins/ethereum).\n\nThis market will resolve according to the “C” (i.e. closing price) listed for the candle titled “Fri 01 Jul 2022, 12:00:00”, with the “Price” tab selected, in the Eastern Time Zone. Note, this 12:00:00 candle lists the opening price for 11:30:00 AM ET and the closing price for 12:00:00 PM ET.\n\nThe check will be after the relevant candle closes. If CoinGecko is unavailable, another credible source will be chosen.", "market_slug": "will-eth-be-above-1200-on-july-1", "end_date_iso": "2022-07-01T00:00:00Z", "game_start_time": null, "seconds_delay": 0, "fpmm": "******************************************", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/image_https%3A//polymarket-upload.s3.us-east-2.amazonaws.com/image_https%253A//polymarket-upload.s3.us-east-2.amazonaws.com/icon_https%25253A//polymarket-upload.s3.us-east-2.amazonaws.com/image_https%2525253A//polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/eth%2525252Blogo%2525252Bchart.jpg", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/image_https%3A//polymarket-upload.s3.us-east-2.amazonaws.com/image_https%253A//polymarket-upload.s3.us-east-2.amazonaws.com/icon_https%25253A//polymarket-upload.s3.us-east-2.amazonaws.com/image_https%2525253A//polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/eth%2525252Blogo%2525252Bchart.jpg", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "23524343204745996710492795385834535675756497436523447151139590328737431282666", "outcome": "Yes", "price": 0, "winner": false}, {"token_id": "70617084946962644427501420627130176626494607953670535857480326296927694355914", "outcome": "No", "price": 1, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 14.96, "minimum_tick_size": 0.01, "condition_id": "0xd9bf55423aef6e2cc59bd14c5e30e69dfd33803b498b42aed2c434431029ee92", "question_id": "0x19a2c7ffd6dc2123caeb53b598f13f8058a49418dd77e2d835d81a2ff3868f88", "question": "2022 Wimbledon Championships: <PERSON><PERSON> vs <PERSON>", "description": "The Wimbledon Championships, commonly known as 'The Championships' or simply 'Wimbledon', is the oldest tennis tournament in the world and is widely regarded as the most prestigious. It has been held at the All England Club in Wimbledon, London since 1877 and is played on outdoor grass courts. There have been retractable roofs over the two main courts since 2019. The 135th edition is scheduled to take place between June 27, and July 10, 2022. \n\nThis is a market about the winner of the upcoming match between <PERSON><PERSON> and <PERSON>, scheduled to take place on June 30, 2022, as part of the 2022 Wimbledon Championships.\n\nThis market will resolve to \"<PERSON>ress<PERSON>\" if <PERSON><PERSON> beats <PERSON> in the pair's upcoming match scheduled for June 30, 2022, during the 2022 Wimbledon Championships. This market will resolve to \"Sock\" if <PERSON> beats <PERSON><PERSON> in the pair's upcoming match. If the winner of this match is not known by July 7, 2022, 12:00 PM ET, this market will resolve to 50-50.\n\nThe primary resolution source will be official information from Wimbledon (https://www.wimbledon.com/), however credible reporting may also be used.", "market_slug": "2022-wimbledon-championships-maxime-cressy-vs-jack-sock", "end_date_iso": "2022-06-30T00:00:00Z", "game_start_time": null, "seconds_delay": 0, "fpmm": "0x48AA60C91bDeaC01cBc99182284a069Bbb47241e", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/image_https%3A//polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/wimbledon.png", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/image_https%3A//polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/wimbledon.png", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "8229035264596140663221870410307220825030121002499640269251671320839639646773", "outcome": "<PERSON><PERSON><PERSON>", "price": 0, "winner": false}, {"token_id": "11047815880592163635920265233784027724703319290734133427712805522921172474766", "outcome": "Sock", "price": 1, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0xee36ec69318aee06a6cc5d1287c5bbbd8b533926ae4b284623114d5071fdd717", "question_id": "0xf875042c0b83fdd0293c5711d8355edb338f66207f93d7b17f1a01163500b75b", "question": "2022 Wimbledon Championships: Who will win <PERSON><PERSON><PERSON> vs. <PERSON><PERSON>?", "description": "The Wimbledon Championships, commonly known as 'The Championships' or simply 'Wimbledon', is the oldest tennis tournament in the world and is widely regarded as the most prestigious. It has been held at the All England Club in Wimbledon, London since 1877 and is played on outdoor grass courts. There have been retractable roofs over the two main courts since 2019. The 135th edition takes place between June 27, and July 10, 2022. \n\nThis is a market on who will advance after the Men’s Singles match between <PERSON> and <PERSON><PERSON>, scheduled for July 5, 8 AM ET.\n\nThe market will resolve to <PERSON><PERSON><PERSON><PERSON> if <PERSON> advances or to <PERSON><PERSON><PERSON> if <PERSON><PERSON> advances. Please note that the market will resolve to whoever advances, even if the result is a \"walkover\".\n\nIf for any reason the person advancing is not declared by organizers by July 12, 11:59:59 PM ET, the market will resolve 50-50.", "market_slug": "2022-wimbledon-championships-who-will-win-djo<PERSON>-vs-sinner", "end_date_iso": "2022-07-05T00:00:00Z", "game_start_time": null, "seconds_delay": 0, "fpmm": "0x26e93eaD69951b201726b16f684D60351B94F40B", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/image_https%3A//polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/wimbledon.png", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/image_https%3A//polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/wimbledon.png", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "102640916187022704133939093501107281254787317798470969227118600241082315279333", "outcome": "<PERSON><PERSON><PERSON>", "price": 1, "winner": false}, {"token_id": "84425526836702705992146966113769632778980748770164108520806997140875979379477", "outcome": "Sinner", "price": 0, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0x3f969c5f52a01224b1d7903952f90f3ca453d15b919ee16dbdb86eb297cb8d89", "question_id": "0xe6cf1bf1276c7084ee53e340059a2468fb9fac06a1e82680b0c62f550a34c3a1", "question": "2022 Wimbledon Championships: Who will win <PERSON> vs. <PERSON><PERSON>?", "description": "The Wimbledon Championships, commonly known as 'The Championships' or simply 'Wimbledon', is the oldest tennis tournament in the world and is widely regarded as the most prestigious. It has been held at the All England Club in Wimbledon, London since 1877 and is played on outdoor grass courts. There have been retractable roofs over the two main courts since 2019. The 135th edition takes place between June 27, and July 10, 2022. \n\nThis is a market on who will advance after the Men’s Singles match between <PERSON> and <PERSON>, scheduled for July 6, 8:30 AM ET.\n\nThe market will resolve to <PERSON><PERSON> if <PERSON> advances or to <PERSON><PERSON><PERSON> if <PERSON> advances. Please note that the market will resolve to whoever advances, even if the result is a \"walkover\".\n\nIf for any reason the person advancing is not declared by organizers by July 13, 11:59:59 PM ET, the market will resolve 50-50.", "market_slug": "2022-wimbledon-championships-who-will-win-fritz-vs-nadal", "end_date_iso": "2022-07-06T00:00:00Z", "game_start_time": null, "seconds_delay": 0, "fpmm": "0x1696d55ed8359f479cC2273bB65862cFB4664812", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/image_https%3A//polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/wimbledon.png", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/image_https%3A//polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/wimbledon.png", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "43617473640893650094000330805196154954357249323611881955755046373215631562348", "outcome": "<PERSON>", "price": 0, "winner": false}, {"token_id": "112416811235553052597853689212753807668937550373593424448757111436323740132334", "outcome": "Nadal", "price": 1, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0x6617fba8404d05fc408345d8831e6f9e5b006f103dc6f37d4774336106aa045e", "question_id": "0xa9234e5af82a115ccc53ee7a967caf35eebb93fb7952c6fbea8bc7d82730b554", "question": "2022 Wimbledon Championships: Who will win <PERSON><PERSON><PERSON> vs. <PERSON><PERSON>?", "description": "The Wimbledon Championships, commonly known as 'The Championships' or simply 'Wimbledon', is the oldest tennis tournament in the world and is widely regarded as the most prestigious. It has been held at the All England Club in Wimbledon, London since 1877 and is played on outdoor grass courts. There have been retractable roofs over the two main courts since 2019. The 135th edition takes place between June 27, and July 10, 2022. \n\nThis is a market on who will advance after the Men’s Singles match between <PERSON> and <PERSON>, scheduled for July 8, 8:30 AM ET.\n\nThe market will resolve to <PERSON><PERSON><PERSON><PERSON> if <PERSON> advances or to <PERSON><PERSON><PERSON> if <PERSON> advances. Please note that the market will resolve to whoever advances, even if the result is a \"walkover\".\n\nIf for any reason the person advancing is not declared by organizers by July 15, 11:59:59 PM ET, the market will resolve 50-50.", "market_slug": "2022-wimbledon-championships-who-will-win-djo<PERSON>-vs-norrie", "end_date_iso": "2022-07-08T00:00:00Z", "game_start_time": null, "seconds_delay": 0, "fpmm": "0x5e967668062dA99Af2ff3f4F0620FeA4907a3266", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/image_https%3A//polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/wimbledon.png", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/image_https%3A//polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/wimbledon.png", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "18693589081895873195910455096214475600640168817439978367203441518795732866210", "outcome": "<PERSON><PERSON><PERSON>", "price": 0, "winner": false}, {"token_id": "73928883127745055825765580811683237849786385626688881346196000607614520770563", "outcome": "<PERSON><PERSON>", "price": 0, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 5, "minimum_tick_size": 0.001, "condition_id": "0x951cc014c30d4d9f18f4738905e44c8613356379da583fb76e08b60855037a03", "question_id": "0xebe7f75ae519966258a5b59f30ccbd7d27f4c0c007597eae507b0a0655c2341e", "question": "[Single Market] Will <PERSON> win the U.S. 2024 Democratic presidential nomination?", "description": "This market will resolve to “Yes” if <PERSON> wins the 2024 nomination of the Democratic Party for U.S. president. Otherwise, this market will resolve to “No”.\n\nThe resolution source for this market will be a consensus of official Democratic Party sources, including https://democrats.org/.\n\nAny replacement of the nominee before election day will not change the resolution of the market.", "market_slug": "will-gavin-newsom-win-the-us-2024-democratic-presidential-nomination", "end_date_iso": "2024-09-10T00:00:00Z", "game_start_time": null, "seconds_delay": 0, "fpmm": "0x4D8e39E280452501294c677C4B2FD0F2e114aB16", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/Gavin.jpg", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/Gavin.jpg", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "7485789793420109554433747368362501384889372782243986779008355377936022316153", "outcome": "Yes", "price": 0, "winner": false}, {"token_id": "12975648701007310340874580615701896522917368669503544169941709453328586016082", "outcome": "No", "price": 1, "winner": true}], "tags": ["Politics", "2024 presidential election", "democratic party", "us elections", "gavin newsom", "u.s. presidential election", "2024 election", "u.s. politics", "presidential nomination", "hillary clinton", "kamala harris", "democrats", "u.s. 2024", "democratic presidential nomination", "dean phillips", "u.s. presidential election 2024", "robert f. kennedy jr.", "u.s. election", "pete buttigi<PERSON>", "elections 2024", "All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0x0efcd5dd54ba77c43d4017474c6f36873336e142d508cd65ef74c68267815aad", "question_id": "0xf9e25a6308bc6a4815a6907b7bcb793ab02cb5295bc0466538a316e19626a826", "question": "2022 Wimbledon Championships: Who will win <PERSON><PERSON><PERSON> vs. <PERSON><PERSON><PERSON><PERSON>?", "description": "The Wimbledon Championships, commonly known as 'The Championships' or simply 'Wimbledon', is the oldest tennis tournament in the world and is widely regarded as the most prestigious. It has been held at the All England Club in Wimbledon, London since 1877 and is played on outdoor grass courts. There have been retractable roofs over the two main courts since 2019. The 135th edition takes place between June 27, and July 10, 2022. \n\nThis is a market on who will win the Men’s Singles match between <PERSON> and <PERSON>, scheduled for July 10, 10:00 AM ET. \n\nThis market will resolve to <PERSON><PERSON><PERSON> if <PERSON> is declared the champion or to <PERSON><PERSON><PERSON><PERSON><PERSON> if <PERSON> is the declared the champion. Please note that the market will resolve to whoever is declared the champion, even if the result is a \"walkover\" or \"default\". If for any reason the champion is not declared by the organizers by July 15, 11:59:59 PM ET, the market will resolve 50-50.", "market_slug": "2022-wimbledon-championships-who-will-win-d<PERSON><PERSON>-vs-kyr<PERSON>s", "end_date_iso": "2022-07-10T00:00:00Z", "game_start_time": null, "seconds_delay": 0, "fpmm": "0x14aad96334c90356eF409F8C83CE1895AD466E23", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/image_https%3A//polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/wimbledon.png", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/image_https%3A//polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/wimbledon.png", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "32691275563493322989092697780482646077015951858463038171689116823264757208765", "outcome": "<PERSON><PERSON><PERSON>", "price": 0.81, "winner": false}, {"token_id": "13422393849104952917945640044590719178356521972940000374359370128790822486363", "outcome": "<PERSON><PERSON><PERSON><PERSON>", "price": 0.19, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0x386750dd12e6803962406465e845dd4ab2e910725a3a35e8fb74bb75032481ae", "question_id": "0xbe9a2ad685c7846e39c5ba46ba9d329c9e40f6b5c8469ea1900c8e918446a376", "question": "MLB: Who will win Boston Red Sox vs. Tampa Bay Rays, scheduled for July 11, 7:10 PM ET?", "description": "In the upcoming MLB game, scheduled for July 11, 7:10 PM ET:\n\nIf the Boston Red Sox win, the market will resolve to “Boston Red Sox”.\n\nIf the Tampa Bay Rays win, the market will resolve to “Tampa Bay Rays”.\n\nIf the game is not completed by July 18 (11:59:59 PM ET), the market will resolve 50-50.", "market_slug": "mlb-who-will-win-boston-red-sox-vs-tampa-bay-rays-scheduled-for-july-11-710-pm-et", "end_date_iso": "2022-07-11T00:00:00Z", "game_start_time": null, "seconds_delay": 0, "fpmm": "0x184bb0ccfd51F5206931Db5229203fB08bEd8125", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/MLB.jpg", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/MLB.jpg", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "26762056040273498834393797118087671500832196045655067026725010169120769446206", "outcome": "Boston Red Sox", "price": 0, "winner": false}, {"token_id": "56913499639481517460561257315180565201430754336278489181810395371152011728032", "outcome": "Tampa Bay Rays", "price": 1, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0x9ea0c01255e588cc416ae45e0f9a5693ff4841cadb9c497ff26a509b6229442b", "question_id": "0x6f27ce3c322ab2071f12fbedd32ac3e5078d47adc3dff93858828e1ecc1f9021", "question": "MLB: Who will win New York Mets vs. Atlanta Braves, scheduled for July 12, 7:20 PM ET?", "description": "In the upcoming MLB game, scheduled for July 12, 7:20 PM ET:\n\nIf the New York Mets win, the market will resolve to “Mets”.\n\nIf the Atlanta Braves win, the market will resolve to “Braves”.\n\nIf the game is not completed by July 19 (11:59:59 PM ET), the market will resolve 50-50.", "market_slug": "mlb-who-will-win-new-york-mets-vs-atlanta-braves-scheduled-for-july-12-720-pm-et", "end_date_iso": "2022-07-12T00:00:00Z", "game_start_time": null, "seconds_delay": 0, "fpmm": "0x2B6c09dDbDA8CF89A2989AEcb0A9508407E22d39", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/MLB.jpg", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/MLB.jpg", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "48328759414714880574318726121896003005916394784276988820613440547137427466236", "outcome": "Mets", "price": 0, "winner": false}, {"token_id": "92069769833298510258530696646244631066262428690341360661435106052722779817309", "outcome": "Braves", "price": 1, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 14.97, "minimum_tick_size": 0.01, "condition_id": "0x5e2d7783eb70c34b0de72882bb310fa1f07c52d6feabbd395af85ac43ceec6cd", "question_id": "0xdae5ced31b5d90b7206f8ea436be32b829a049c5f68e4016b2c83e208f7f2d89", "question": "MLB: Who will win New York Mets v. Chicago Cubs, scheduled for July 14, 8:05 PM ET?", "description": "In the upcoming MLB game scheduled for July 14, 8:05 PM ET:\n\nIf the New York Mets win, this market will resolve to “Mets”.\n\nIf the Chicago Cubs win, this market will resolve to “Cubs”.\n\nIf the game is not completed by July 21 (11:59:59 PM ET), this market will resolve 50-50.", "market_slug": "mlb-who-will-win-new-york-mets-v-chicago-cubs-scheduled-for-july-14-805-pm-et", "end_date_iso": "2022-07-14T00:00:00Z", "game_start_time": "2022-07-15T00:05:00.414Z", "seconds_delay": 3, "fpmm": "0x15221a220517f8FE4bB04D1503234B0B605cd9AF", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/MLB.jpg", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/MLB.jpg", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "66705055520486035594152917599417241803410370428042790528062198116650211354854", "outcome": "Mets", "price": 0, "winner": false}, {"token_id": "48234908733372643957258184255412467952928611791506167866950990015974456917341", "outcome": "Cubs", "price": 0, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0x15c0504f370a50cfb0524441563096393198b8959d760c0347949e689a655576", "question_id": "0xeb4872f9e12d1e34f6eb0075648cedcc6b06a8e849884c6a6f3df3be54db8323", "question": "MLB: Who will win Dodgers vs. Cardinals, scheduled for July 13, 7:45 PM ET?", "description": "In the upcoming MLB game, scheduled for July 13, 7:45 PM ET:\n\nIf the Los Angeles Dodgers win, this market will resolve to “Dodgers”.\n\nIf the St. Louis Cardinals win, this market will resolve to “Cardinals”.\n\nIf this game is not completed by July 20 (11:59:59 PM ET), this market will resolve 50-50.", "market_slug": "mlb-who-will-win-dodgers-vs-cardinals-scheduled-for-july-13-745-pm-et", "end_date_iso": "2022-07-13T00:00:00Z", "game_start_time": "2022-07-13T23:45:00Z", "seconds_delay": 3, "fpmm": "0x97ca4ef7aB79a4Bad32D99175B372680bd1AcC82", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/MLB.jpg", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/MLB.jpg", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "8324329575596800618842953068948484663467206098101353336934688506350052362726", "outcome": "Dodgers", "price": 0, "winner": false}, {"token_id": "34448824442889189169835950802419930188273622101954994021783873789349190433330", "outcome": "Cardinals", "price": 0, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0x6507e330b9094f9893423cab7a8d60dab4cf0e7802d5d887895daf4728b0ff66", "question_id": "0x984443c2ac48d815d89b55ff7c56b1a8e72275de09f07295107f5c9bb2e453da", "question": "MLB: Who will win San Francisco Giants v. Los Angeles Dodgers, scheduled for July 22, 10:10 PM ET?", "description": "In the upcoming MLB game scheduled for July 22, 10:10 PM ET:\n\nIf the San Francisco Giants win, this market will resolve to “Giants”.\n\nIf the Los Angeles Dodgers win, this market will resolve to “Dodgers”.\n\nIf the game is not completed by July 29 (11:59:59 PM ET), this market will resolve 50-50.", "market_slug": "mlb-who-will-win-san-francisco-giants-v-los-angeles-dodgers-scheduled-for-july-22-1010-pm-et", "end_date_iso": "2022-07-22T00:00:00Z", "game_start_time": "2022-07-23T02:10:00Z", "seconds_delay": 3, "fpmm": "0x1375588DED2931168DbD39B2510aAd792f3b30FF", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/MLB.jpg", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/MLB.jpg", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "111109709167867528746843674041369469053226623209220903940831478122494756466595", "outcome": "Giants", "price": 0, "winner": false}, {"token_id": "40539158247861993798348820182947183181912301274778438864445400243997588212918", "outcome": "Dodgers", "price": 0, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0xf0bba7de36096bc1aa410653b66ad6ff30ca0409454bebb055b09f3c3915a86c", "question_id": "0x97ddb2869bd4b6cb956d9740693a59ab4790d66e9b59e58a25a88ccafdc723e9", "question": "Will the price of a barrel of crude oil be $100 or more on July 22, 2022?", "description": "This is a market on whether the price of crude oil will be at $100.00 or higher as of market close on July 22, 2022. If the value of a barrel of oil is $100.00 or greater as of market close on the resolution date, this market will resolve to “Yes”. If the value of a barrel of oil is less than $100.00 as of market close on the resolution date, this market will resolve to “No”.  \n\nIf for any reason (e.g. market holiday, emergency market closure) there is no close price for this market's resolution date, the close price of the nearest previous date will be used.  \n\nThe resolution source for this market will be NASDAQ's reporting on the values offered by the New York Mercantile Exchange (NYMEX), found at (https://www.nasdaq.com/market-activity/commodities/cl%3Anmx/historical). The resolution will be based on the value listed in the column labelled “Close/Last” in the row titled with the resolution date.", "market_slug": "will-the-price-of-a-barrel-of-crude-oil-be-100-or-more-on-july-22-2022", "end_date_iso": "2022-07-22T00:00:00Z", "game_start_time": null, "seconds_delay": 0, "fpmm": "0xAb6c2740aF3857cfE4aE0e2a981B0442A47Bae18", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/Barrel.jpg", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/Barrel.jpg", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "36493711549671937246658436730998354303063613774443106674094485546314560542305", "outcome": "Yes", "price": 0, "winner": false}, {"token_id": "14151399106496984768508109779731837882199889145175535973711355640165189680237", "outcome": "No", "price": 1, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0xa8287c322d4cfe704b04c5367662cc884b1c75ee4651fbbf51e7f720ea53bbce", "question_id": "0x749d6cdf11b8e83bfd8dcafdb60faa8566e2c8d98ec85fec5e04d86df185852c", "question": "MLB: Who will win Atlanta Braves v. Philadelphia Phillies, scheduled for July 25, 7:05 PM ET?", "description": "In the upcoming MLB game scheduled for July 25, 7:05 PM ET:\n\nIf the Atlanta Braves win, this market will resolve to “Braves”.\n\nIf the Philadelphia Phillies win, this market will resolve to “Phillies”.\n\nIf the game is not completed by August 1 (11:59:59 PM ET), this market will resolve 50-50.", "market_slug": "mlb-who-will-win-atlanta-braves-v-philadelphia-phillies-scheduled-for-july-25-705-pm-et", "end_date_iso": "2022-07-25T00:00:00Z", "game_start_time": "2022-07-25T23:05:00Z", "seconds_delay": 3, "fpmm": "0x5e4040C0D9eF5E53fd37587BA2E46083b8691A79", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/MLB.jpg", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/MLB.jpg", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "86560778941650730943323417595588844382120095931435082637492728757263098895643", "outcome": "Braves", "price": 0.64, "winner": false}, {"token_id": "75170110050972615586616149953183008127650251760211047931707084911079097558313", "outcome": "Phillies", "price": 0.36, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0x9be4915ed263c3cefe52e999cdbf2ada9aa3f576bdd6d0f55369b59b43e9e381", "question_id": "0x0e1791ed8e483ee0a707396727e834245cd0ede229151a67945fe1536aec4b54", "question": "MLB: Who will win New York Yankees v. New York Mets, scheduled for July 26, 7:10 PM ET?", "description": "In the upcoming MLB game scheduled for July 26, 7:10 PM ET:\n\nIf the New York Yankees win, this market will resolve to “Yankees”.\n\nIf the New York Mets win, this market will resolve to “Mets”.\n\nIf the game is not completed by August 2 (11:59:59 PM ET), this market will resolve 50-50.", "market_slug": "mlb-who-will-win-new-york-yankees-v-new-york-mets-scheduled-for-july-26-710-pm-et", "end_date_iso": "2022-07-26T00:00:00Z", "game_start_time": "2022-07-26T23:10:00Z", "seconds_delay": 3, "fpmm": "0x8B0a074Fd8992861AFfdf872F2FF85f9CD3245d0", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/MLB.jpg", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/MLB.jpg", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "24734312942662095026495086940168456535490339328151697591402803254654762775362", "outcome": "Yankees", "price": 0, "winner": false}, {"token_id": "34604592444444078485277661869064840499227508837588932255734513226051461917801", "outcome": "Mets", "price": 0, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0xd2db839bff0e5451bd9e918be44d51e7ff0156925532536f3e72f583bf691365", "question_id": "0x97124fb11e3d26caec2073ef0ec237f6fc7a49afaaeb80cc5284ebc1666ffc2c", "question": "MLB: Who will win Chicago Cubs v. San Francisco Giants, scheduled for July 28, 9:45 PM ET?", "description": "In the upcoming MLB game scheduled for July 28, 9:45 PM ET:\n\nIf the Chicago Cubs win, this market will resolve to “Cubs”.\n\nIf the San Francisco Giants win, this market will resolve to “Giants”.\n\nIf the game is not completed by August 4 (11:59:59 PM ET), this market will resolve 50-50.", "market_slug": "mlb-who-will-win-chicago-cubs-v-san-francisco-giants-scheduled-for-july-28-945-pm-et", "end_date_iso": "2022-07-29T00:00:00Z", "game_start_time": "2022-07-29T01:45:00.063Z", "seconds_delay": 3, "fpmm": "0x146f9A9687474a0738eb749b2540AAfdeC8e3ef9", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/MLB.jpg", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/MLB.jpg", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "59296159884441737739435180130625919870851703985380506633887946837907970791512", "outcome": "Cubs", "price": 0, "winner": false}, {"token_id": "77625745672483242997383342333177920047941664700995969033756992324963702714286", "outcome": "Giants", "price": 0, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0x23b56720dacd8c59907beba9b3a5e6a1db04903a9d4168c25c6c7c200e99bcb5", "question_id": "0x64272ce505a6a63140e555c959591e4ffc51f4d9df352be1c2c236c9f1acf263", "question": "MLB: Who will win Cleveland Guardians v. Boston Red Sox, scheduled for July 27, 7:10 PM ET?", "description": "In the upcoming MLB game scheduled for July 27, 7:10 PM ET:\n\nIf the Cleveland Guardians win, this market will resolve to “Guardians”.\n\nIf the Boston Red Sox win, this market will resolve to “Red Sox”.\n\nIf the game is not completed by August 3 (11:59:59 PM ET), this market will resolve 50-50.", "market_slug": "mlb-who-will-win-cleveland-guardians-v-boston-red-sox-scheduled-for-july-27-710-pm-et", "end_date_iso": "2022-07-27T00:00:00Z", "game_start_time": "2022-07-27T23:10:00Z", "seconds_delay": 3, "fpmm": "0xBE67feB271E4dA9FfE561F1a6e8ad6731330F590", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/MLB.jpg", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/MLB.jpg", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "98089748666902318786131465316273495220333022783079327633266970324043360352837", "outcome": "Guardians", "price": 0, "winner": false}, {"token_id": "22796139858509827266884495714198467351770066978825766086877818904317040553415", "outcome": "Red Sox", "price": 0, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0xf931c37bc0d186095d0d06066f9a8372bccd82ccf12361809b631d9ddaeeee04", "question_id": "0x91bc597bf5a0fcdbfe67f4f9a3184efc642fda47315a8e1bf39582607c4604d4", "question": "MLB: Who will win Toronto Blue Jays v. Tampa Bay Rays, scheduled for August 2, 7:10 PM ET?", "description": "In the upcoming MLB game scheduled for August 2, 7:10 PM ET:\n\nIf the Toronto Blue Jays win, this market will resolve to “Blue Jays”.\n\nIf the Tampa Bay Rays win, this market will resolve to “Rays”.\n\nIf the game is not completed by August 9 (11:59:59 PM ET), this market will resolve 50-50.", "market_slug": "mlb-who-will-win-toronto-blue-jays-v-tampa-bay-rays-scheduled-for-august-2-710-pm-et", "end_date_iso": "2022-08-02T00:00:00Z", "game_start_time": "2022-08-02T23:10:00Z", "seconds_delay": 3, "fpmm": "0x9aE92Fe310FF8f753afaB297115f8a8DF7CE7558", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/MLB.jpg", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/MLB.jpg", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "38804251869393644332585026311743182459680476316307334805847420966747930871016", "outcome": "Blue Jays", "price": 0, "winner": false}, {"token_id": "95452300770340875154370270736915377883132753965118803717629754658045950424583", "outcome": "<PERSON><PERSON>", "price": 0, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0x1c6ed01028195fc0ffcbb0fb4a0f57132fedb65456376819c104f886cff9b167", "question_id": "0xc67be7280943451264cdd5df2d0e644c8c86fad732e92c72fe5bbea403dccfbc", "question": "NFL Preseason: Who will win Jacksonville Jaguars v. Las Vegas Raiders, scheduled for August 4, 8:00 PM ET?", "description": "In the upcoming NFL preseason game scheduled for August 4, 8:00 PM ET:\n\nIf the Jacksonville Jaguars win, this market will resolve to “Jaguars”.\n\nIf the Las Vegas Raiders win, this market will resolve to “Raiders”.\n\nIf the game ends in a tie, this market will resolve 50-50.\n\nIf the game is not completed by August 11 (11:59:59 PM ET), this market will resolve 50-50.", "market_slug": "nfl-preseason-who-will-win-jacksonville-jaguars-v-las-vegas-raiders-scheduled-for-august-4-800-pm-et", "end_date_iso": "2022-08-04T00:00:00Z", "game_start_time": "2022-08-05T00:00:00Z", "seconds_delay": 2, "fpmm": "0xFBf0cC97D4a9A4cF410C40dab9Fe2002cc9D2745", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/nfl.png", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/nfl.png", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "6901237497551481925956654462253353961122050621319347404786911219289972170261", "outcome": "Jaguars", "price": 0, "winner": false}, {"token_id": "32235446807858308568852813373064956057796134002241613317534148540835564369699", "outcome": "Raiders", "price": 0, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0x177cda40ffe00e6d89c1af8c2cc280581361a4350ac8766d16eaebf8c02167ad", "question_id": "0xc0ae31e906e1c2396f325ddca8b7b5399e0471104bade03c178b455300400852", "question": "MLB: Who will win Atlanta Braves v. Boston Red Sox, scheduled for August 9, 7:10 PM ET?", "description": "In the upcoming MLB game scheduled for August 9, 7:10 PM ET:\n\nIf the Atlanta Braves win, this market will resolve to “Braves”.\n\nIf the Boston Red Sox win, this market will resolve to “Red Sox”.\n\nIf the game is not completed by August 16 (11:59:59 PM ET), this market will resolve 50-50.", "market_slug": "mlb-who-will-win-atlanta-braves-v-boston-red-sox-scheduled-for-august-9-710-pm-et", "end_date_iso": "2022-08-09T00:00:00Z", "game_start_time": "2022-08-09T23:10:00Z", "seconds_delay": 3, "fpmm": "0x3C932EC3866348380ba01A38417420FB25c888F3", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/MLB.jpg", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/MLB.jpg", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "55438570117295462128623445714506670634001994137643275679764526422660520980974", "outcome": "Braves", "price": 0, "winner": false}, {"token_id": "108653314058497301870589189970340189736443843258890974837866663132029996092335", "outcome": "Red Sox", "price": 0, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0x5eb98990f95a673bd6a438c47d8f5bd835a255b87b42a88123fa92c653859c3b", "question_id": "0xa656e0dbdd236d16c31fd20b76662d170da347f7cd7d3e7e29cd0beebb60181e", "question": "MLB: Who will win Minnesota Twins v. Los Angeles Dodgers, scheduled for August 10, 10:10 PM ET?", "description": "In the upcoming MLB game scheduled for August 10, 10:10 PM ET:\n\nIf the Minnesota Twins win, this market will resolve to “Twins”.\n\nIf the Los Angeles Dodgers win, this market will resolve to “Dodgers”.\n\nIf the game is not completed by August 17 (11:59:59 PM ET), this market will resolve 50-50.", "market_slug": "mlb-who-will-win-minnesota-twins-v-los-angeles-dodgers-scheduled-for-august-10-1010-pm-et", "end_date_iso": "2022-08-10T00:00:00Z", "game_start_time": "2022-08-11T02:10:00Z", "seconds_delay": 3, "fpmm": "0x84d4dBDDdAA07aF188dBACa1c4F1e0cf22074e59", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/MLB.jpg", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/MLB.jpg", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "18085453502961937279853269962073107148504617320486877246539194898523412634575", "outcome": "Twins", "price": 0, "winner": false}, {"token_id": "14738706615013458833019139416180032439141817738367238992035195278124444899787", "outcome": "Dodgers", "price": 0, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0xdb6ec0db23cc48839cc7912f5253c4d7b57f861f11ae54aea8ff6ed1efb7e935", "question_id": "0x56c1a6f5622f2bb0fc7a15789d3413bd582686e140ea3fd4e07e93b7604ce099", "question": "MLB: Who will win New York Yankees v. Boston Red Sox, scheduled for August 14, 7:08 PM ET?", "description": "In the upcoming MLB game scheduled for August 14, 7:08 PM ET:\n\nIf the New York Yankees win, this market will resolve to “Yankees”.\n\nIf the Boston Red Sox win, this market will resolve to “Red Sox”.\n\nIf the game is not completed by August 21 (11:59:59 PM ET) this market will resolve 50-50.", "market_slug": "mlb-who-will-win-new-york-yankees-v-boston-red-sox-scheduled-for-august-14-708-pm-et", "end_date_iso": "2022-08-14T00:00:00Z", "game_start_time": "2022-08-14T23:08:00Z", "seconds_delay": 3, "fpmm": "0x504a036Bb0f5DEc20E0afa3f71E7700646314FE3", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/MLB.jpg", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/MLB.jpg", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "70276383073891790372982290479083607506460207562821394967086996435890136309777", "outcome": "Yankees", "price": 0, "winner": false}, {"token_id": "21033957937283593745063546740758397251924261271291760920846859862730276803303", "outcome": "Red Sox", "price": 0, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0x5afb48af961d4416471f591953e37c49eb10f65ed3252f8315430f8db0a2a7d7", "question_id": "0x5fd768550fb7a462ce0bb0ba494f4691794feabffba3bd984921a6bc66ff98f8", "question": "NFL Preseason: Who will win Minnesota Vikings v. Las Vegas Raiders, scheduled for August 14, 4:25 PM ET?", "description": "In the upcoming NFL preseason game scheduled for August 14, 4:25 PM ET:\n\nIf the Minnesota Vikings win, this market will resolve to “Vikings”.\n\nIf the Las Vegas Raiders win, this market will resolve to “Raiders”.\n\nIf the game ends in a tie, this market will resolve 50-50.\n\nIf the game is not completed by August 21 (11:59:59 PM ET), this market will resolve 50-50.", "market_slug": "nfl-preseason-who-will-win-minnesota-vikings-v-las-vegas-raiders-scheduled-for-august-14-425-pm-et", "end_date_iso": "2022-08-14T00:00:00Z", "game_start_time": "2022-08-14T20:25:00Z", "seconds_delay": 3, "fpmm": "0xD2E648703b6213e07aE2916A2C6bEa541b642b2d", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/nfl.png", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/nfl.png", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "75734596084576657310879559266167715895664011851389511913445080684188413655604", "outcome": "Vikings", "price": 0, "winner": false}, {"token_id": "9830121868268060657513821266147696833522272654797324293707473985879511473680", "outcome": "Raiders", "price": 0, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0x09776586b8dc3f65cee05adb6e5c1f647dba76992c314a77d80a191cab6bb8c0", "question_id": "0xe87f4eb99dec34aa5544dced6b543a5ec9f2c391c352d8a87664d79f9e3a296f", "question": "Premier League: Who will win the Crystal Palace vs. Liverpool game on August 15?", "description": "In the upcoming Premier League soccer game, scheduled for August 15:\n\nIf the Crystal Palace F.C. wins, the market will resolve to “Crystal Palace”. \n\nIf the Liverpool F.C. wins, the market will resolve to “Liverpool”.\n\nIf the game is not completed by August 22, 2022, or there is a tie, the market will resolve 50-50.", "market_slug": "premier-league-who-will-win-the-crystal-palace-vs-liverpool-game-on-august-15", "end_date_iso": "2022-08-15T00:00:00Z", "game_start_time": "2022-08-15T19:00:00.599Z", "seconds_delay": 3, "fpmm": "0x15BD05AbE7f4440f66235fC9d036D6bB1a89B8b2", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/premier+league.jpg", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/premier+league.jpg", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "26430229334445961376968322516392779151001660223387778747930666455579577999631", "outcome": "Crystal Palace", "price": 0, "winner": false}, {"token_id": "78560150967493632575566770688124045340054094663229498669036428951932571624356", "outcome": "Liverpool", "price": 0, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0xe6fac90ae4250a40225dacd6376c2771e653d3bc339e7ce9d9c8861ee2971659", "question_id": "0x5dccbdf27d3e073bcd633cdfe650baef091a9465a117ab5eab3fc35b51c601a3", "question": "MLB: Who will win San Diego Padres v. Miami Marlins, scheduled for August 15, 6:40 PM ET?", "description": "In the upcoming MLB game scheduled for August 15, 6:40 PM ET:\n\nIf the San Diego Padres win, this market will resolve to “Padres”.\n\nIf the Miami Marlins win, this market will resolve to “Marlins”.\n\nIf the game is not completed by August 22 (11:59:59 PM ET) this market will resolve 50-50.", "market_slug": "mlb-who-will-win-san-diego-padres-v-miami-marlins-scheduled-for-august-15-640-pm-et", "end_date_iso": "2022-08-15T00:00:00Z", "game_start_time": "2022-08-15T22:40:00.782Z", "seconds_delay": 3, "fpmm": "0x4C7709fdF63c667B1Ffb7Bf429c47a74d1378F4A", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/MLB.jpg", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/MLB.jpg", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "108494431042699709497211901192330239021361619773111489433580209866941794165283", "outcome": "Padres", "price": 0, "winner": false}, {"token_id": "47254758479229044066447698837922946175665010082329055062901576261062379649634", "outcome": "<PERSON><PERSON>", "price": 0, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0x37a9d3e59ab4bd3c622f74207d2179dc21a00a648a8bd98a64739a3a720c643b", "question_id": "0xca6578e4bca42444b8c5d151a737320b47ab9254e52180e0d425efe9e8a80487", "question": "MLB: Who will win New York Mets v. Atlanta Braves, scheduled for August 16, 7:20 PM ET?", "description": "In the upcoming MLB game scheduled for August 16, 7:20 PM ET:\n\nIf the New York Mets win, this market will resolve to “Mets”.\n\nIf the Atlanta Braves win, this market will resolve to “Braves”.\n\nIf the game is not completed by August 23 (11:59:59 PM ET) this market will resolve 50-50.", "market_slug": "mlb-who-will-win-new-york-mets-v-atlanta-braves-scheduled-for-august-16-720-pm-et", "end_date_iso": "2022-08-16T00:00:00Z", "game_start_time": "2022-08-16T23:20:00Z", "seconds_delay": 3, "fpmm": "0xdAB29A671fB18C4795Cc99EB0d78F9d7e561d078", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/MLB.jpg", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/MLB.jpg", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "68034208601303513670594201700259533828530054343282902539294658202372121720788", "outcome": "Mets", "price": 0, "winner": false}, {"token_id": "108605238103391841239339380005848223351598986954016948736708403612078792128816", "outcome": "Braves", "price": 0, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0x6b3751822822bf12e650c2920db73d7e2066792127d7ce69b7f684ed3e496aaf", "question_id": "0xde7f9db3a0ec7ca227e1bcb37c88eb9265528a4288a000cdb6b3fc2c31d9881e", "question": "MLB: Who will win Los Angeles Dodgers v. Milwaukee Brewers, scheduled for August 17, 8:10 PM ET?", "description": "In the upcoming MLB game scheduled for August 17, 8:10 PM ET:\n\nIf the Los Angeles Dodgers win, this market will resolve to “Dodgers”.\n\nIf the Milwaukee Brewers win, this market will resolve to “Brewers”.\n\nIf the game is not completed by August 24 (11:59:59 PM ET) this market will resolve 50-50.", "market_slug": "mlb-who-will-win-los-angeles-dodgers-v-milwaukee-brewers-scheduled-for-august-17-810-pm-et", "end_date_iso": "2022-08-17T00:00:00Z", "game_start_time": "2022-08-18T00:10:00Z", "seconds_delay": 3, "fpmm": "0x5F5EF97C25bab806De43Bdfb1DacddD3dd6d6A38", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/MLB.jpg", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/MLB.jpg", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "56609540568932168086366129960156742894313098733400417277083261062927714065926", "outcome": "Dodgers", "price": 0, "winner": false}, {"token_id": "107588478888330736146981926339855505167901447841714352041832981455607626723951", "outcome": "Brewers", "price": 0, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": false, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0x055176c9ebd8775c281ad540d5c16b3323e316507aef2d45c84f061cbc6bbcdc", "question_id": "0x5153ac4948bc2a54abd7828ad1d82e41024fd3eda35deda821f9f92d9e8c72a7", "question": "MLB: Who will win Boston Red Sox v. Baltimore Orioles, scheduled for August 21, 7:10 PM ET?", "description": "In the upcoming MLB game scheduled for August 21, 7:10 PM ET:\n\nIf the Boston Red Sox win, this market will resolve to “Red Sox”.\n\nIf the Baltimore Orioles win, this market will resolve to “Orioles”.\n\nIf the game is not completed by August 28 (11:59:59 PM ET) this market will resolve 50-50.", "market_slug": "mlb-who-will-win-boston-red-sox-v-baltimore-orioles-scheduled-for-august-21-710-pm-et", "end_date_iso": "2022-08-21T00:00:00Z", "game_start_time": "2022-08-21T23:10:00Z", "seconds_delay": 3, "fpmm": "0xeD3dc4c0893689f1407B182dB9AE8a9b71196c88", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/MLB.jpg", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/MLB.jpg", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "96608690015534549481981139148848533486469138671280945937552552577172448128011", "outcome": "Red Sox", "price": 0, "winner": false}, {"token_id": "9612890763764062692282935414227141810568206972440321500296202304471805951204", "outcome": "Orioles", "price": 1, "winner": true}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0x7bdbb78a104e7301cc9c6e0fef64bb5733c8fbe69ab8082cf26e227f91908ffa", "question_id": "0xdf11099924e2558303fd157be7e3ba02f47bcc0e0be0ea867f86e6e6bdb25493", "question": "MLB: Who will win Cleveland Guardians v. San Diego Padres, scheduled for August 23, 9:40 PM ET?", "description": "In the upcoming MLB game scheduled for August 23, 9:40 PM ET:\n\nIf the Cleveland Guardians win, this market will resolve to “Guardians”.\n\nIf the San Diego Padres win, this market will resolve to “Padres”.\n\nIf the game is not completed by August 30 (11:59:59 PM ET) this market will resolve 50-50.", "market_slug": "mlb-who-will-win-cleveland-guardians-v-san-diego-padres-scheduled-for-august-23-940-pm-et", "end_date_iso": "2022-08-24T00:00:00Z", "game_start_time": "2022-08-24T01:40:00Z", "seconds_delay": 3, "fpmm": "0x992aCa3eC814F50Fa35D54ab23b30a86E0ED26b4", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/MLB.jpg", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/MLB.jpg", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "66840793208895902134770756801876217714621927881254495944557040037466903434752", "outcome": "Guardians", "price": 0, "winner": false}, {"token_id": "93483829706271419755141541597999290384499264524196374818190642418621166604124", "outcome": "Padres", "price": 0, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0xb5858726a0b48b44465ee6c6d0d8fc913bde224ebb1045a537a687cfe1171222", "question_id": "0xadbef34334689c60ddae4d1eade297400bae5c750571c8bd75c5edda2c37e0bb", "question": "Will <PERSON> be indicted by March 31, 2023?", "description": "This market will resolve to “Yes” if any Federal or State jurisdiction of the United States unseals or otherwise officially announces a criminal indictment of former President <PERSON> before the resolution time March 31, 2023, 11:59:59 PM ET. \nOtherwise, it will resolve to “No”.\n\nPlease note, for purposes of this market, the District of Columbia and any county, municipality, or other subdivision of a State shall be included within the definition of a State.\nNote also, that an indictment that has been issued before the resolution time but remains sealed or otherwise secret at the resolution time will not be considered in this market.", "market_slug": "will-donald-j-trump-be-indicted-by-march-31-2023", "end_date_iso": "2023-03-31T00:00:00Z", "game_start_time": null, "seconds_delay": 0, "fpmm": "0x07E87487b31F7AabBFDc03AE385508c6eE98e775", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/will-donald-j-trump-be-indicted-by-march-31-2023-1c8c32f6-b439-44a8-a2e4-480f3ee28f38.png", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/will-donald-j-trump-be-indicted-by-march-31-2023-1c8c32f6-b439-44a8-a2e4-480f3ee28f38.png", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "3854028961966046605980069255334402052447887445769771059116269182001114272326", "outcome": "Yes", "price": 1, "winner": true}, {"token_id": "112102392865920769780182576716902680486413294799150614854158175980612636620456", "outcome": "No", "price": 0, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0x69d110a0dffd6cbb27a13394b4c213ff2559e6cd2b3e40f7618bd233afbab5d3", "question_id": "0x40553c4b2fd2b73635e081084f8405316560d4442ad1b2b9790faa63cfe5e332", "question": "Will <PERSON> play an NBA game in the 2022-23 regular season?", "description": "<PERSON> is a National Basketball Association (NBA) player with the Charlotte Hornets. In July, 2022 he was arraigned on \"one felony count of injuring a child’s parent and two felony counts of child abuse under circumstances or conditions likely to cause great bodily injury or death\", throwing his status as an active player into question.\n\nThis market will resolve to \"Yes\" if <PERSON> plays at least one game in the NBA 2022-23 regular season. Otherwise, this market will resolve to \"No\".\n\nThis market may only resolve to \"No\" once the NBA 2022-23 regular season has ended.\n\nThe resolution source for this market will be official information released by the National Basketball Association, e.g. <PERSON>' official player statistics, for the column titled GP in the regular season: https://www.nba.com/stats/player/1628970.", "market_slug": "will-miles-bridges-play-an-nba-game-in-the-2022-23-regular-season", "end_date_iso": "2023-04-09T00:00:00Z", "game_start_time": null, "seconds_delay": 0, "fpmm": "0x1b41bD509aB61EAd1398acbFC2eDCfe6fc489C99", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Miles+Bridges.png", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Miles+Bridges.png", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "37467393793870216551696405777887476755411553063234773578246172326723370667995", "outcome": "Yes", "price": 0, "winner": false}, {"token_id": "86571566370710053293239289823136842050386507871493091628045323167513168359729", "outcome": "No", "price": 1, "winner": true}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0x6a6ca5cb80b453e178af19c2dc887dd6f8ff4e4884daa61935c3711a6c09e83b", "question_id": "0xc7efddfd3cd7daef6d9445d1a5fc19dba94ed8afd56c5d4f61db686bf7445f76", "question": "Will <PERSON><PERSON><PERSON> break the Premier League goals record in the 22/23 season? (34 goals)", "description": "The Premier League's 2022/23 season started on August 6, 2022, and the final match round is scheduled for May 28, 2023. <PERSON><PERSON><PERSON> has already scored 15 goals, and there is significant buzz that he may break the Premier League's record of 34 goals scored in a season, currently shared by <PERSON> (Newcastle 1993-94) and <PERSON> (Blackburn 1994-95).\n\nThis market will resolve to \"Yes\" if <PERSON><PERSON><PERSON> scores 35 or more goals in the Premier League's 2022/23 season. Otherwise, this market will resolve to \"No\".\n\nThis market will resolve as soon as <PERSON><PERSON><PERSON> scores his 35th goal in the Premier League 2022/23 season, or otherwise plays his last scheduled match in the Premier League 2022/23 season.\n\nThe resolution source for this market will be official information from the premier league (e.g. https://www.premierleague.com/players/65970/Erling-<PERSON>d/stats?co=1&se=489).", "market_slug": "will-erling-haaland-break-the-premier-league-goals-record-in-the-2223-season-34-goals", "end_date_iso": "2023-05-28T00:00:00Z", "game_start_time": null, "seconds_delay": 0, "fpmm": "0x601d98e676649D9956AcADa01A9CaE2fA5c8e545", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/haaland.png", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/haaland.png", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "87478238843962372955942642067463731884279746362588126171058056558772239734230", "outcome": "Yes", "price": 1, "winner": true}, {"token_id": "17075117077105897381285187093694054525632088265660863705569379110251748585612", "outcome": "No", "price": 0, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 5, "minimum_tick_size": 0.001, "condition_id": "0xf3b7a1ffbb8cf0f8b5c392bc8615e5d7d28781bbc2a2d1630041ed5a4bb2aa73", "question_id": "0x348a2e2a59d9dd02bf71d0b3fbb7a50031b7bf814f9de92b3508a84f7f617047", "question": "[Single Market] Will <PERSON> win the U.S. 2024 Republican presidential nomination?", "description": "This market will resolve to “Yes” if former US vice president <PERSON> wins the 2024 nomination of the Republican Party for U.S. president. Otherwise, this market will resolve to “No”.\n\nThe resolution source for this market will be a consensus of official GOP sources, including https://www.gop.com.\n\nAny replacement of the nominee before election day will not change the resolution of the market.", "market_slug": "will-mike-pence-win-the-us-2024-republican-presidential-nomination", "end_date_iso": "2024-09-10T00:00:00Z", "game_start_time": null, "seconds_delay": 0, "fpmm": "0x8565335942aac6a0A7bB973F5DF88E41A731ab3d", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/mike+pence.png", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/mike+pence.png", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "98282331925505121785872687805280263968688819118250947355244296315799835397380", "outcome": "Yes", "price": 0, "winner": false}, {"token_id": "98832099334299678364240751912655312938723886874942994315244949116951319625279", "outcome": "No", "price": 1, "winner": true}], "tags": ["Politics", "tim scott", "republican party", "v<PERSON><PERSON>", "republicans", "2024 presidential election", "u.s. 2024 elections", "<PERSON>", "u.s. 2024 republican presidential nomination", "Elections", "ron desantis", "u.s. presidential election", "ben sha<PERSON><PERSON>", "2024 election", "joe rogan", "kanye west", "u.s. politics", "presidential nomination", "donald trump jr.", "presidential election 2024", "glenn youngkin", "chris christie", "mike pence", "peter thiel", "u.s. presidency", "2024 republican presidential nomination", "All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0x9287b5377b76529a5bcfeef2d82848e20340d70f818f5be438371c33ad036c92", "question_id": "0x76d8e8960f70dec7b903a22de6ab742827b303056ace01f4cda4ca6b714ce20b", "question": "Will 'Avatar: The Way of Water' gross more than $650 million domestically by January 31, 2023?", "description": "'Avatar: The Way of Water' (2022) is an upcoming American action adventure fantasy sci-fi film produced by 20th Century Studios. It is the second installment of the Avatar film series directed by <PERSON>. It is scheduled for theatrical release in the USA on December 16, 2022.\n\nThis is a market on how much 'Avatar: The Way of Water' will gross domestically by January 31, 2023 (inclusive). The “Domestic Daily” tab on https://www.boxofficemojo.com/release/rl3372254721/ will be used to resolve this market, specifically the \"To Date\" column.\n\nThis market will resolve to \"Yes\" if 'Avatar: The Way of Water' grosses more than $650,000,000 domestically by January 31, 2023. Otherwise, this market will resolve to \"No\".\n\nIf any finalized numbers in the \"To Date\" column (for dates January 31, 2023 and before) show a value of more than $650 million, this market will resolve immediately to \"Yes.\" \n\nThis market may only resolve to \"No\" once the January 31, 2023 \"To Date\" number is finalized, or if Jan 31, 2023 date is not available, the nearest previous date with available data.", "market_slug": "will-avatar-the-way-of-water-gross-more-than-650-million-domestically-by-january-31-2023", "end_date_iso": "2023-01-31T00:00:00Z", "game_start_time": null, "seconds_delay": 0, "fpmm": "0xF0F30337E9C98E28DcaB0b3af4A11d7Db202D4B2", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/avatar2.png", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/avatar2.png", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "107754309707789193901261105278967201345561077546606796858573409567513389818812", "outcome": "Yes", "price": 0, "winner": false}, {"token_id": "111418289100934687995154966444108081709096708376512104007820653421637200463766", "outcome": "No", "price": 1, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0xa9cf5313e10ce87be900934970fdc0a0c26f4ab12671eb1bbed12ba88120f71d", "question_id": "0x3f0db7a50451417dc3b4984e79eab0e0efc2ee2ada3eceb55c90513cbf8b1ff2", "question": "Will SBF be federally indicted by EOY?", "description": "This market will resolve to \"Yes\" if <PERSON>, co-founder and CEO of FTX, is indicted on federal charges or otherwise formally charged with any federal crime by the United States of America between November 10, 2022 and December 31, 2022, 11:59:59 PM ET. Otherwise, this market will resolve to \"No\".\n\nThe primary resolution source for this market will be official information from the federal government of the United States (from sources such as https://www.fbi.gov/news/pressrel, https://www.justice.gov/news), however a consensus of credible reporting may be used.", "market_slug": "will-sbf-be-federally-indicted-by-eoy", "end_date_iso": "2022-12-31T00:00:00Z", "game_start_time": null, "seconds_delay": 0, "fpmm": "0x4beBaf6612A0e56D5A13749173e78811fb492E0E", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/sbf1.jpeg", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/sbf1.jpeg", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "28138408415760624400519509867904324806200671314808027842532314150683687796821", "outcome": "Yes", "price": 0.9998, "winner": false}, {"token_id": "31568614448526992749124020390508562757794266757026035192670036014403613291504", "outcome": "No", "price": 0.0002, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0xa176093a98d710b81b9a4d0d57b151fd8c0053bf9796778358be5abea3085547", "question_id": "0x7eac308d9cb5fbfae236d3033505f879d4b236236f58ac3904b6dd6ad10d0db9", "question": "Will U.S. inflation be more than 0.1% from October to November 2022?", "description": "This is a market on a month-to-month inflation, as measured by the Bureau of Labor Statistics (CPI-U), from October to November 2022.\n\nInflation is measured by a change in Consumer Price Index for All Urban Consumers (CPI-U) on a seasonally adjusted basis, reported by the U.S. Bureau of Labor Statistics.\n\nThis market will resolve to “Yes” if the index increased by more than 0.1 percent (on a seasonally adjusted basis) from October to November 2022 and “No“ otherwise.\n\nThe resolution source for this market will be the BLS November 2022 Consumer Price Index report (https://www.bls.gov/bls/news-release/cpi.htm), currently scheduled to be released on December 13, 2022, at 8:30 AM ET. Resolution of this market will take place upon release of the aforementioned data. \n\n---\n\nThe Consumer Price Index (CPI) is a measure of the average change over time in the prices paid by urban consumers for a market basket of consumer goods and services.\n\nNote that the resolution source for this market will be the official monthly BLS CPI news release which reports seasonally-adjusted month-to-month inflation to only one decimal point (e.g. 0.7%, 0.6%, etc). Thus, this is the level of precision that will be used when resolving the market (e.g. if seasonally adjusted month-to-month CPI-U is 0.1%, the market will resolve to “No”, if it is 0.2%, this market will resolve to “Yes”).", "market_slug": "will-us-inflation-be-more-than-0pt1-from-october-to-november-2022", "end_date_iso": "2022-12-13T00:00:00Z", "game_start_time": null, "seconds_delay": 0, "fpmm": "0x2C7cd3B9DF7D9921C262005a00e940ab76D37180", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/Inflation.png", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/Inflation.png", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "10825690345209246807546160516925956760607544137852513202681515319215358012773", "outcome": "Yes", "price": 0.0003, "winner": false}, {"token_id": "446974584141201080150211659811583225722387638174911715605534238931246586102", "outcome": "No", "price": 0.9997, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0xe6837ecc9485f588af6858b0b307293ba1af60969fa386c59af2da8e37b1fd94", "question_id": "0xf0091728c819e4b2b158566aa907bddfb6fa6248cd832f6e8682788550a3b09d", "question": "Will U.S. inflation be more than 0.2% from October to November 2022?", "description": "This is a market on a month-to-month inflation, as measured by the Bureau of Labor Statistics (CPI-U), from October to November 2022.\n\nInflation is measured by a change in Consumer Price Index for All Urban Consumers (CPI-U) on a seasonally adjusted basis, reported by the U.S. Bureau of Labor Statistics.\n\nThis market will resolve to “Yes” if the index increased by more than 0.2 percent (on a seasonally adjusted basis) from October to November 2022 and “No“ otherwise.\n\nThe resolution source for this market will be the BLS November 2022 Consumer Price Index report (https://www.bls.gov/bls/news-release/cpi.htm), currently scheduled to be released on December 13, 2022, at 8:30 AM ET. Resolution of this market will take place upon release of the aforementioned data. \n\n---\n\nThe Consumer Price Index (CPI) is a measure of the average change over time in the prices paid by urban consumers for a market basket of consumer goods and services.\n\nNote that the resolution source for this market will be the official monthly BLS CPI news release which reports seasonally-adjusted month-to-month inflation to only one decimal point (e.g. 0.7%, 0.6%, etc). Thus, this is the level of precision that will be used when resolving the market (e.g. if seasonally adjusted month-to-month CPI-U is 0.2%, the market will resolve to “No”, if it is 0.3%, this market will resolve to “Yes”).", "market_slug": "will-us-inflation-be-more-than-0pt2-from-october-to-november-2022", "end_date_iso": "2022-12-13T00:00:00Z", "game_start_time": null, "seconds_delay": 0, "fpmm": "0x4BCB8E75c67263a208ED40895736ecEB046f73d7", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/Inflation.png", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/Inflation.png", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "34481482853889239557635054125294223660163902387435233350481658461894480561203", "outcome": "Yes", "price": 0.0004, "winner": false}, {"token_id": "73052352113931658337706214261789022663787289928052312677480533186253579062748", "outcome": "No", "price": 0.9996, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0x9e60c432648859520a987648fe23b662cd7537ae76c459cfe9288ef4ca8f883e", "question_id": "0x98e4941fe5a67cf207154d8c83c98aaec0c659d827e70af3c35f3cd1da59d5d5", "question": "Will U.S. inflation be more than 0.3% from October to November 2022?", "description": "This is a market on a month-to-month inflation, as measured by the Bureau of Labor Statistics (CPI-U), from October to November 2022.\n\nInflation is measured by a change in Consumer Price Index for All Urban Consumers (CPI-U) on a seasonally adjusted basis, reported by the U.S. Bureau of Labor Statistics.\n\nThis market will resolve to “Yes” if the index increased by more than 0.3 percent (on a seasonally adjusted basis) from October to November 2022 and “No“ otherwise.\n\nThe resolution source for this market will be the BLS November 2022 Consumer Price Index report (https://www.bls.gov/bls/news-release/cpi.htm), currently scheduled to be released on December 13, 2022, at 8:30 AM ET. Resolution of this market will take place upon release of the aforementioned data. \n\n---\n\nThe Consumer Price Index (CPI) is a measure of the average change over time in the prices paid by urban consumers for a market basket of consumer goods and services.\n\nNote that the resolution source for this market will be the official monthly BLS CPI news release which reports seasonally-adjusted month-to-month inflation to only one decimal point (e.g. 0.7%, 0.6%, etc). Thus, this is the level of precision that will be used when resolving the market (e.g. if seasonally adjusted month-to-month CPI-U is 0.3%, the market will resolve to “No”, if it is 0.4%, this market will resolve to “Yes”).", "market_slug": "will-us-inflation-be-more-than-0pt3-from-october-to-november-2022", "end_date_iso": "2022-12-13T00:00:00Z", "game_start_time": null, "seconds_delay": 0, "fpmm": "0xE4d94d53B3eA61a3701D1bc04d29feD34d5Cd8F1", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/Inflation.png", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/Inflation.png", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "28688245426751592288384385635086581787615257322692979021607852341251324337181", "outcome": "Yes", "price": 0.0001, "winner": false}, {"token_id": "13910834109807089972923962098572085269441074240833016479250551589801283707301", "outcome": "No", "price": 0.9999, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0xec4fbf081cf587efc4facf27fffbc85a9c588cf7108a8bf2689e238124444410", "question_id": "0x56047e835f29c4ffd7242d5ae2b8a8bcb7756cc858e14c5474ba47802ad74d7b", "question": "Will U.S. inflation be more than 0.4% from October to November 2022?", "description": "This is a market on a month-to-month inflation, as measured by the Bureau of Labor Statistics (CPI-U), from October to November 2022.\n\nInflation is measured by a change in Consumer Price Index for All Urban Consumers (CPI-U) on a seasonally adjusted basis, reported by the U.S. Bureau of Labor Statistics.\n\nThis market will resolve to “Yes” if the index increased by more than 0.4 percent (on a seasonally adjusted basis) from October to November 2022 and “No“ otherwise.\n\nThe resolution source for this market will be the BLS November 2022 Consumer Price Index report (https://www.bls.gov/bls/news-release/cpi.htm), currently scheduled to be released on December 13, 2022, at 8:30 AM ET. Resolution of this market will take place upon release of the aforementioned data. \n\n---\n\nThe Consumer Price Index (CPI) is a measure of the average change over time in the prices paid by urban consumers for a market basket of consumer goods and services.\n\nNote that the resolution source for this market will be the official monthly BLS CPI news release which reports seasonally-adjusted month-to-month inflation to only one decimal point (e.g. 0.7%, 0.6%, etc). Thus, this is the level of precision that will be used when resolving the market (e.g. if seasonally adjusted month-to-month CPI-U is 0.4%, the market will resolve to “No”, if it is 0.5%, this market will resolve to “Yes”).", "market_slug": "will-us-inflation-be-more-than-0pt4-from-october-to-november-2022", "end_date_iso": "2022-12-13T00:00:00Z", "game_start_time": null, "seconds_delay": 0, "fpmm": "0x99160845c75eA8a1CB73DcDfc755b6Fe3305C7b1", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/Inflation.png", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/Inflation.png", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "75763949814518394822523458980741561145981797066031821087395064496392435036396", "outcome": "Yes", "price": 0.0006, "winner": false}, {"token_id": "9438775503749848002697158884723994222715237347786979007816966168698074740939", "outcome": "No", "price": 0.9994, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0x3990f55503d1f1b9c94153327210b8b8eec101006c47a0c1f754af80d813f128", "question_id": "0x2f72e82fef71d22a3c66f2c84e52c7e32daede5d17893f40674914652c865884", "question": "Will Argentina win the 2022 World Cup?", "description": "In Response to Trader Inquiry: This market may immediately resolve to “No” if the team is eliminated or otherwise has no path to victory based on the rules of the 2022 World Cup.\n\n---------------------------------------------------------------------------------------------------------------\n\nThis market will resolve to \"Yes\" if Argentina is the Champion of FIFA World Cup Qatar 2022. Otherwise, this market will resolve to \"No\".\n\nIf no Champion of FIFA World Cup Qatar 2022 is declared by December 31, 2022, 11:59:59 PM ET, this market will resolve to 50-50.\n\nThe primary resolution source for this market will be official information from FIFA (e.g. https://www.fifa.com/fifaplus/en/tournaments/mens/worldcup/qatar2022/scores-fixtures), including official footage from the World Cup, however a consensus of credible reporting may also be used.", "market_slug": "will-argentina-win-the-2022-world-cup", "end_date_iso": "2022-12-18T00:00:00Z", "game_start_time": null, "seconds_delay": 0, "fpmm": "0x455bA72BA53773cBF347981D26386473B7e10a45", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/flag+argentina.png\n", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/flag+argentina.png\n", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "26123139076199609978522847027495726840275308007885498317455055187418797035383", "outcome": "Yes", "price": 0.9993, "winner": false}, {"token_id": "101816010996196886619009744418883592340857425486409910131450672434879405150269", "outcome": "No", "price": 0.0007, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0xd425eb1d83aef0500596c46254b199a4fa44247da2d61b0a359f81c6e8211e4f", "question_id": "0xaca672706eec97de90b20b3c3a109af927de859a54e60b3d5eec0c97539d0bbc", "question": "Will France win the 2022 World Cup?", "description": "In Response to Trader Inquiry: This market may immediately resolve to “No” if the team is eliminated or otherwise has no path to victory based on the rules of the 2022 World Cup.\n\n---------------------------------------------------------------------------------------------------------------\n\nThis market will resolve to \"Yes\" if France is the Champion of FIFA World Cup Qatar 2022. Otherwise, this market will resolve to \"No\".\n\nIf no Champion of FIFA World Cup Qatar 2022 is declared by December 31, 2022, 11:59:59 PM ET, this market will resolve to 50-50.\n\nThe primary resolution source for this market will be official information from FIFA (e.g. https://www.fifa.com/fifaplus/en/tournaments/mens/worldcup/qatar2022/scores-fixtures), including official footage from the World Cup, however a consensus of credible reporting may also be used.", "market_slug": "will-france-win-the-2022-world-cup", "end_date_iso": "2022-12-18T00:00:00Z", "game_start_time": null, "seconds_delay": 0, "fpmm": "0xFD2BBBf6FB2ADd9eF8cb1038Fe8BfBea6933ebA1", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/flag+france.png\n", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/flag+france.png\n", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "114371001821164164902646439685427759853981924302906124203753724550487055672889", "outcome": "Yes", "price": 0.0004, "winner": false}, {"token_id": "25285210070220154879776098149650327251027554102335965508704565054853282633381", "outcome": "No", "price": 0.9996, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0xe72ccc87d52382d893534f061a5a6adf1653ea8c17aa143702119a84a810be33", "question_id": "0xfe6ef163a06a95ad5cbf036b83192555f84523b456a978f076c9f6cda4bddf7f", "question": "Will Croatia win the 2022 World Cup?", "description": "In Response to Trader Inquiry: This market may immediately resolve to “No” if the team is eliminated or otherwise has no path to victory based on the rules of the 2022 World Cup.\n\n---------------------------------------------------------------------------------------------------------------\n\nThis market will resolve to \"Yes\" if Croatia is the Champion of FIFA World Cup Qatar 2022. Otherwise, this market will resolve to \"No\".\n\nIf no Champion of FIFA World Cup Qatar 2022 is declared by December 31, 2022, 11:59:59 PM ET, this market will resolve to 50-50.\n\nThe primary resolution source for this market will be official information from FIFA (e.g. https://www.fifa.com/fifaplus/en/tournaments/mens/worldcup/qatar2022/scores-fixtures), including official footage from the World Cup, however a consensus of credible reporting may also be used.", "market_slug": "will-croatia-win-the-2022-world-cup", "end_date_iso": "2022-12-18T00:00:00Z", "game_start_time": null, "seconds_delay": 0, "fpmm": "0x04F164CcD4f9E866C6afae0dBc88E53c7121a9a6", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/flag+croatia.png\n", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/flag+croatia.png\n", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "1045990436189152068423220432870986041184604466981267113710148075294820184459", "outcome": "Yes", "price": 0.0002, "winner": false}, {"token_id": "51135908343901278183693801383752935878686703491455393952891563502325813041701", "outcome": "No", "price": 0.9998, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0x9c4d05a5b1cec55d56626ede111048cb6c43ec8e8c777f794b605d33be0c82ec", "question_id": "0xcf1433e753034fdae627356b9dcf18e20709ca67dcf61fb9f01195ae704fae76", "question": "Will Morocco win the 2022 World Cup?", "description": "In Response to Trader Inquiry: This market may immediately resolve to “No” if the team is eliminated or otherwise has no path to victory based on the rules of the 2022 World Cup.\n\n---------------------------------------------------------------------------------------------------------------\n\nThis market will resolve to \"Yes\" if Morocco is the Champion of FIFA World Cup Qatar 2022. Otherwise, this market will resolve to \"No\".\n\nIf no Champion of FIFA World Cup Qatar 2022 is declared by December 31, 2022, 11:59:59 PM ET, this market will resolve to 50-50.\n\nThe primary resolution source for this market will be official information from FIFA (e.g. https://www.fifa.com/fifaplus/en/tournaments/mens/worldcup/qatar2022/scores-fixtures), including official footage from the World Cup, however a consensus of credible reporting may also be used.", "market_slug": "will-morocco-win-the-2022-world-cup", "end_date_iso": "2022-12-18T00:00:00Z", "game_start_time": null, "seconds_delay": 0, "fpmm": "0x25F0587C29B38dB5E8183029715990Eb197D60e2", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/flag+morocco.png\n", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/flag+morocco.png\n", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "105045584662682185685970556727114212674702864314804848740433640162087194646657", "outcome": "Yes", "price": 0.0003, "winner": false}, {"token_id": "68611577922343702457318296073818808816474842546502335416540125778940444670828", "outcome": "No", "price": 0.9997, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0x00b64a6a871db574b5ef8a9702437687ced76de894e086b1d3d5604feeaae586", "question_id": "0x79ecd766e87e97e4451b2c146efcbe3ad99775c54b670c780833b28ad2bafff5", "question": "Will 'Avatar: The Way of Water' gross more than $175 million domestically on its opening weekend?", "description": "'Avatar: The Way of Water' (2022) is an upcoming American action adventure fantasy sci-fi film produced by 20th Century Studios. It is the second installment of the Avatar film series directed by <PERSON>. It is scheduled for theatrical release in the USA on December 16, 2022.\n\nThis is a market on how much ‘Avatar: The Way of Water' will gross domestically on its opening weekend. The “Domestic Weekend” tab on https://www.boxofficemojo.com/title/tt1630029/ will be used to resolve this market once the values for the opening weekend (Dec 16-18) are final (i.e. not studio estimates).\n\nThis market will resolve to \"Yes\" if ‘Avatar: The Way of Water’ grosses more than $175,000,000 on its opening weekend. Otherwise, this market will resolve to \"No\".\n\nOpening weekend is defined as the first Friday, Saturday, and Sunday of the film's release. Please note, this market will resolve according to the Box Office Mojo number under Domestic Weekend for the 3-day weekend, regardless of whether domestic refers to only the USA, or to USA and Canada, etc.\n\nIf there is no final data available by December 26, 2022, 11:59:59 PM ET, another credible resolution source will be chosen.\n", "market_slug": "will-avatar-the-way-of-water-gross-more-than-175-million-domestically-on-its-opening-weekend", "end_date_iso": "2022-12-19T00:00:00Z", "game_start_time": null, "seconds_delay": 0, "fpmm": "******************************************", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/avatar+way+of+water.png", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/avatar+way+of+water.png", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "9002767896513307433373760038953934858769604991808707205317638291339294953989", "outcome": "Yes", "price": 0, "winner": false}, {"token_id": "40526823457244896112861646472576725802005102379004751361133752242752737276689", "outcome": "No", "price": 1, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0x591eec0563dcd596de64765ba46742ada461f8da91f039dcc83841265236a1f2", "question_id": "0x9adbf3313c9a3d9b9a51467cb03c4f53a21989a2d10f06325c2851edf1603379", "question": "Will SBF attend <PERSON>ine <PERSON>'s Congressional hearing?", "description": "<PERSON><PERSON> has invited <PERSON> (SBF) to appear before congress on December 13 to participate in a hearing. You can read her invitation via tweet here: https://twitter.com/RepMaxineWaters/status/1598693811252875264?s=20&t=XF4jCR9D3ws5bywZ3Fxbkg\n\nThis market will resolve to \"Yes\" if S<PERSON> physically appears at the congressional hearing he was invited to by <PERSON><PERSON>. Otherwise, this market will resolve to \"No\". \n\nIf the hearing is rescheduled to a date on or before December 31, 2022, this market will resolve once the hearing occurs. If the hearing is cancelled or postponed to a date after December 31, 2022, this market will resolve to \"No.\"\n\nFor the purposes of this market \"physically appears\" means that SBF appears in the United States for the congressional hearing in-person. Any telecommuting or streamed/prerecorded appearance will not be sufficient to resolve this market to \"Yes\".\n\nThis market's resolution source will be a consensus of credible reporting.", "market_slug": "will-sbf-attend-maxine-waters-congressional-hearing", "end_date_iso": "2022-12-13T00:00:00Z", "game_start_time": null, "seconds_delay": 0, "fpmm": "0xc82D2D38786d09fa5Dc91EBAd06E44099B525c11", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/sbfevil.png", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/sbfevil.png", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "87394345679057865149616762219211558177070150162008265576347492356262591885806", "outcome": "Yes", "price": 0, "winner": false}, {"token_id": "26796731057329890084186944794675642438538814995826745588832027716273887082446", "outcome": "No", "price": 1, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0xb51ccf704ac1fe87750db843ef3982e3dd37e4405c1dc545b49b416e0d64efd7", "question_id": "0xe5b78778e1b4d8669fa254322ed6a82bbba75db0b85b72f2e227d4ff51efa815", "question": "World Cup: Saturday - France vs. England", "description": "In the upcoming World Cup Quarter-finals soccer game, scheduled for December 10:  \n\nIf France wins, this market will resolve to \"France\".\nIf England wins, this market will resolve to \"England\". \n\nThis market will resolve based on the winner of the game, which includes regular time, extra time, and penalties.\n\nIf the game is not completed by December 18, 2022, this market will resolve 50-50.", "market_slug": "world-cup-saturday-france-vs-england", "end_date_iso": "2022-12-10T00:00:00Z", "game_start_time": "2022-12-10T19:00:00Z", "seconds_delay": 3, "fpmm": "0x9458ff6E2A5312c0C1B19436c4510959A7056C76", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/flag+france.png", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/flag+france.png", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "85616932232674288042346062547007149966268283433490516024559200198439648257677", "outcome": "France", "price": 0.9999, "winner": false}, {"token_id": "385167050514863041928081391056086691177616020327245197958833157777967264474", "outcome": "England", "price": 0.0001, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0x1444aa19610940c750a2fb4ac6c26bccbc0080a8b2f93e715b69d443e0cc7b18", "question_id": "0x00dc462d4fdb03e3f9f1a2b23f8a19ae5a5f3e98373930126d3f78b39301334f", "question": "World Cup: Saturday - Portugal vs. Morocco", "description": "In the upcoming World Cup Quarter-finals soccer game, scheduled for December 10:  \n\nIf Portugal wins, this market will resolve to \"Portugal\".\nIf Morocco wins, this market will resolve to \"Morocco\". \n\nThis market will resolve based on the winner of the game, which includes regular time, extra time, and penalties.\n\nIf the game is not completed by December 18, 2022, this market will resolve 50-50.", "market_slug": "world-cup-saturday-portugal-vs-morocco", "end_date_iso": "2022-12-10T00:00:00Z", "game_start_time": "2022-12-10T15:00:00Z", "seconds_delay": 3, "fpmm": "0xD4bD9bbB0cC7499b8CCfE6BcCEABD709Ecf653d9", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/flag+portugal.png", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/flag+portugal.png", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "57497473000369437350798096518722555583048163398825407567074259414631615679672", "outcome": "Portugal", "price": 0, "winner": false}, {"token_id": "46445877220496307025442939730529345616312175976266245436030960374828619760606", "outcome": "Morocco", "price": 1, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0x7a1fe597ac9644de6b97eeee238ba2b6439f091ff1dc172f9858c20904ff923f", "question_id": "0xae408845ab043b32decfbac0751c8087828c633f5a6cd8787ad30b3c22f21469", "question": "World Cup: Tuesday - Argentina vs. Croatia", "description": "In the upcoming World Cup Semifinals soccer game, scheduled for December 13:  \n\nIf Argentina wins, this market will resolve to \"Argentina\".\nIf Croatia wins, this market will resolve to \"Croatia\". \n\nThis market will resolve based on the winner of the game, which includes regular time, extra time, and penalties.\n\nIf the game is not completed by December 18, 2022, this market will resolve 50-50.", "market_slug": "world-cup-tuesday-argentina-vs-croatia", "end_date_iso": "2022-12-13T00:00:00Z", "game_start_time": "2022-12-13T19:00:00Z", "seconds_delay": 3, "fpmm": "0x89F9A2da2a32bd176C82c3f5Bba43DF97bD71A24", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/flag+argentina.png", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/flag+argentina.png", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "56331120378768085083807049568748918263528811106383757925339269209247241825845", "outcome": "Argentina", "price": 0.9999, "winner": false}, {"token_id": "86771234057093018828456100884278305817453445557880068058258911571182778590112", "outcome": "Croatia", "price": 0.0001, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0x0da3bb900632c5dfa0dcda6df806fc2b84862bb1820a141f9a22e0fcf27c8d55", "question_id": "0xd2cbeca36abcc2b0fe7465078cc1df91b02a63259e979dd36dc5d959838dbcda", "question": "World Cup: Wednesday - France vs. Morocco", "description": "In the upcoming World Cup Semifinals soccer game, scheduled for December 14:  \n\nIf France wins, this market will resolve to \"France\".\nIf Morocco wins, this market will resolve to \"Morocco\". \n\nThis market will resolve based on the winner of the game, which includes regular time, extra time, and penalties.\n\nIf the game is not completed by December 18, 2022, this market will resolve 50-50.", "market_slug": "world-cup-wednesday-france-vs-morocco", "end_date_iso": "2022-12-14T00:00:00Z", "game_start_time": "2022-12-14T19:00:00Z", "seconds_delay": 3, "fpmm": "0xd467112657537e5165E55D488419697835A336b3", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/flag+france.png", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/flag+france.png", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "35135409348165296533425769152169889177123033197141413836854440313087551126988", "outcome": "France", "price": 0.9997, "winner": false}, {"token_id": "80842897032421863068904294259184211620480611515975055424063236856134499439265", "outcome": "Morocco", "price": 0.0003, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0x4438202b145817f30fa3ee9ac5ab73a6160ec04ec5918bd843775f3b65b3cb47", "question_id": "0x13573d586baef638a24e681905141062b86bbd55773931c86be7952fd8f10e2c", "question": "Will <PERSON> be federally charged by March 31?", "description": "This market will resolve to \"Yes\" if <PERSON>, former CEO of Alameda Research, is indicted on federal charges or otherwise formally charged with any federal crime by the United States of America between December 13, 2022 and March 31, 2023, 11:59:59 PM ET. Otherwise, this market will resolve to \"No\".\n\nNote that reporting of sealed charges before the resolution date will suffice to resolve the market to \"Yes.\"\n\nThe primary resolution source for this market will be official information from the federal government of the United States (from sources such as https://www.fbi.gov/news/pressrel, https://www.justice.gov/news), however a consensus of credible reporting may be used.", "market_slug": "will-caroline-ellison-be-federally-charged-by-march-31", "end_date_iso": "2023-03-31T00:00:00Z", "game_start_time": null, "seconds_delay": 0, "fpmm": "0xd425Cea6FffE8B7a167602f41ba72a2a6aC03152", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/caroline.png", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/caroline.png", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "90421200627469287880519613061929208554581143025038571108604080630495097434908", "outcome": "Yes", "price": 1, "winner": false}, {"token_id": "63414574955591770473266166605246569121935971581627956000955579339328790256702", "outcome": "No", "price": 0, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0xb08c75808eeaa441b29685b0f30fba3874b4c4daff08747bbeb87fee6f49cf92", "question_id": "0xfed0f84033d3996da584cd61753e8e4865f97a23a634c4bd63b015b1941d75cf", "question": "Will <PERSON> be federally charged by March 31?", "description": "This market will resolve to \"Yes\" if <PERSON>, former co-CEO of Alameda Research, is indicted on federal charges or otherwise formally charged with any federal crime by the United States of America between December 13, 2022 and March 31, 2023, 11:59:59 PM ET. Otherwise, this market will resolve to \"No\".\n\nNote that reporting of sealed charges before the resolution date will suffice to resolve the market to \"Yes.\"\n\nThe primary resolution source for this market will be official information from the federal government of the United States (from sources such as https://www.fbi.gov/news/pressrel, https://www.justice.gov/news), however a consensus of credible reporting may be used.", "market_slug": "will-sam-trabucco-be-federally-charged-by-march-31", "end_date_iso": "2023-03-31T00:00:00Z", "game_start_time": null, "seconds_delay": 0, "fpmm": "0x191d05eDf3f0459C6a3d7494ea2EE606E60f9d98", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/sam+trabucco.jpeg", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/sam+trabucco.jpeg", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "20418249395528193802690805363653680152815570994555068696349565358020009181165", "outcome": "Yes", "price": 0, "winner": false}, {"token_id": "49515817667654132103401803284866398082676377522628909268495406309612104648479", "outcome": "No", "price": 1, "winner": true}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0xe023d544352833115f5c7a2bc89aba8216adf5bc91de4f59887f0fdc5a682784", "question_id": "0x8b75b40712fd3b75fe9b1248c0ef765fc091cd5fae6fb978e5df6b112f65cffb", "question": "Will U.S. inflation be greater than -0.3% from November to December 2022?", "description": "This is a market on a month-to-month inflation, as measured by the Bureau of Labor Statistics (CPI-U), from November to December 2022. \n\nInflation is measured by a change in Consumer Price Index for All Urban Consumers (CPI-U) on a seasonally adjusted basis, reported by the U.S. Bureau of Labor Statistics. \n\nThis market will resolve to “Yes” if the index increased by more than -0.3 percent (on a seasonally adjusted basis) from November to December 2022 and “No“ otherwise. \n\nThe resolution source for this market will be the BLS December 2022 Consumer Price Index report (https://www.bls.gov/bls/news-release/cpi.htm), currently scheduled to be released on January 12, 2023, at 8:30 AM ET. Resolution of this market will take place upon release of the aforementioned data. \n\n----\n\nThe Consumer Price Index (CPI) is a measure of the average change over time in the prices paid by urban consumers for a market basket of consumer goods and services. \n\nNote: the resolution source for this market will be the official monthly BLS CPI news release which reports seasonally-adjusted month-to-month inflation to only one decimal point (e.g. 0.7%, 0.6%, etc). Thus, this is the level of precision that will be used when resolving the market (e.g. if seasonally adjusted month-to-month CPI-U is -0.3%, the market will resolve to “No”, if it is -0.2%, this market will resolve to “Yes”).\n", "market_slug": "will-us-inflation-be-greater-than-0pt3-from-november-to-december-2022", "end_date_iso": "2023-01-12T00:00:00Z", "game_start_time": null, "seconds_delay": 0, "fpmm": "0xc017A82Ad65BA157C90ea0Db9f14c49e46B925dC", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/Inflation.png", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/Inflation.png", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "54183509765298175804953169181694278231007853239508428409398888321719986478377", "outcome": "Yes", "price": 1, "winner": false}, {"token_id": "68650275185894740399540419005061824687173119199530848635801508579328324648803", "outcome": "No", "price": 0, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0x4dc49ea1ddaf826269a5691aa1bb9d69509a0616a266d6c69448664e65169462", "question_id": "0x6340c360e41c8b548c0370166253a46c472e5d0a2a1292700009f3678ac9acd8", "question": "Will U.S. inflation be greater than -0.2% from November to December 2022?", "description": "This is a market on a month-to-month inflation, as measured by the Bureau of Labor Statistics (CPI-U), from November to December 2022. \n\nInflation is measured by a change in Consumer Price Index for All Urban Consumers (CPI-U) on a seasonally adjusted basis, reported by the U.S. Bureau of Labor Statistics. \n\nThis market will resolve to “Yes” if the index increased by more than -0.2 percent (on a seasonally adjusted basis) from November to December 2022 and “No“ otherwise. \n\nThe resolution source for this market will be the BLS December 2022 Consumer Price Index report (https://www.bls.gov/bls/news-release/cpi.htm), currently scheduled to be released on January 12, 2023, at 8:30 AM ET. Resolution of this market will take place upon release of the aforementioned data. \n\n----\n\nThe Consumer Price Index (CPI) is a measure of the average change over time in the prices paid by urban consumers for a market basket of consumer goods and services. \n\nNote: the resolution source for this market will be the official monthly BLS CPI news release which reports seasonally-adjusted month-to-month inflation to only one decimal point (e.g. 0.7%, 0.6%, etc). Thus, this is the level of precision that will be used when resolving the market (e.g. if seasonally adjusted month-to-month CPI-U is -0.2%, the market will resolve to “No”, if it is -0.1%, this market will resolve to “Yes”).\n", "market_slug": "will-us-inflation-be-greater-than-0pt2-from-november-to-december-2022", "end_date_iso": "2023-01-12T00:00:00Z", "game_start_time": null, "seconds_delay": 0, "fpmm": "0x16c244e0E35672b47579b6e0C5fc5787089a281E", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/Inflation.png", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/Inflation.png", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "103733425057729124746205890891948788956330343109988185796973904151250161115058", "outcome": "Yes", "price": 1, "winner": false}, {"token_id": "55501707684130134810893095134828672275930683163464570609158276445492087580931", "outcome": "No", "price": 0, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0x54bd39a293a524d4ecbb391ed056e5da80a95eb4d9ff5b35af1101cc48188751", "question_id": "0x707ff429e0f434c060948d79e555ff3824a9e9a51d65e43479427cb0e0b1d661", "question": "Will U.S. inflation be greater than -0.1% from November to December 2022?", "description": "This is a market on a month-to-month inflation, as measured by the Bureau of Labor Statistics (CPI-U), from November to December 2022. \n\nInflation is measured by a change in Consumer Price Index for All Urban Consumers (CPI-U) on a seasonally adjusted basis, reported by the U.S. Bureau of Labor Statistics. \n\nThis market will resolve to “Yes” if the index increased by more than -0.1 percent (on a seasonally adjusted basis) from November to December 2022 and “No“ otherwise. \n\nThe resolution source for this market will be the BLS December 2022 Consumer Price Index report (https://www.bls.gov/bls/news-release/cpi.htm), currently scheduled to be released on January 12, 2023, at 8:30 AM ET. Resolution of this market will take place upon release of the aforementioned data. \n\n----\n\nThe Consumer Price Index (CPI) is a measure of the average change over time in the prices paid by urban consumers for a market basket of consumer goods and services. \n\nNote: the resolution source for this market will be the official monthly BLS CPI news release which reports seasonally-adjusted month-to-month inflation to only one decimal point (e.g. 0.7%, 0.6%, etc). Thus, this is the level of precision that will be used when resolving the market (e.g. if seasonally adjusted month-to-month CPI-U is -0.1%, the market will resolve to “No”, if it is 0.0%, this market will resolve to “Yes”).\n", "market_slug": "will-us-inflation-be-greater-than-0pt1-from-november-to-december-2022", "end_date_iso": "2023-01-12T00:00:00Z", "game_start_time": null, "seconds_delay": 0, "fpmm": "0xD0514eA8C435C3dd14d0d19D4D749e5c7cb8033C", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/Inflation.png", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/Inflation.png", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "18305502723317709192604760313348150424931333917074943556297172461415884491615", "outcome": "Yes", "price": 0, "winner": false}, {"token_id": "115310325787037774283021873748952226199244695677021245870611447234220252356396", "outcome": "No", "price": 1, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0xcbe2211244525dd6245c1a7875132215e3c5a1e9e8a1013d996c2dae769b62b8", "question_id": "0x7465327362d210242b99bbbb236d6ac311cb2b1ffe8031c17c9cab223eb4fd69", "question": "Will U.S. inflation be greater than 0.0% from November to December 2022?", "description": "This is a market on a month-to-month inflation, as measured by the Bureau of Labor Statistics (CPI-U), from November to December 2022. \n\nInflation is measured by a change in Consumer Price Index for All Urban Consumers (CPI-U) on a seasonally adjusted basis, reported by the U.S. Bureau of Labor Statistics. \n\nThis market will resolve to “Yes” if the index increased by more than 0.0 percent (on a seasonally adjusted basis) from November to December 2022 and “No“ otherwise. \n\nThe resolution source for this market will be the BLS December 2022 Consumer Price Index report (https://www.bls.gov/bls/news-release/cpi.htm), currently scheduled to be released on January 12, 2023, at 8:30 AM ET. Resolution of this market will take place upon release of the aforementioned data. \n\n----\n\nThe Consumer Price Index (CPI) is a measure of the average change over time in the prices paid by urban consumers for a market basket of consumer goods and services. \n\nNote: the resolution source for this market will be the official monthly BLS CPI news release which reports seasonally-adjusted month-to-month inflation to only one decimal point (e.g. 0.7%, 0.6%, etc). Thus, this is the level of precision that will be used when resolving the market (e.g. if seasonally adjusted month-to-month CPI-U is 0.0%, the market will resolve to “No”, if it is 0.1%, this market will resolve to “Yes”).\n", "market_slug": "will-us-inflation-be-greater-than-0pt0-from-november-to-december-2022", "end_date_iso": "2023-01-12T00:00:00Z", "game_start_time": null, "seconds_delay": 0, "fpmm": "0x21143262A44C29086ccf5d6d0B94F98d2195EC47", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/Inflation.png", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/Inflation.png", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "106510692201476350018868230541630661022400888634955872483918697719512406430179", "outcome": "Yes", "price": 0, "winner": false}, {"token_id": "80160627816687864064395435425332409156776985951929833876693852205494069645116", "outcome": "No", "price": 1, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0x7e6488eadc8fc58186091fc8db7cd7946dbe15f70f3607f7f197f3e74901f908", "question_id": "0x32edf25c72b5fe472ca23069c300fed6dc9ceac884c4e4bd83df51d9c77e25a5", "question": "Will Binance.com become insolvent by Jan 31, 2023?", "description": "This market will resolve to \"Yes\" if official information released by Binance, official representatives of Binance (e.g. lawyers, spokespeople, etc.), or a consensus of credible reporting announces by January 31, 2023, 11:59:59 PM ET that Binance.com is insolvent or is filing/has filed for any variety of bankruptcy.\n\nThis market will also resolve to \"Yes\" if Binance.com withdrawals for BTC and/or ETH are suspended for a majority of users for at least 7 full days (168 hours) in a row by January 31, 2023, 11:59:59 PM ET. The primary resolution source for this settlement criterion will be official information released by Binance, official representatives of Binance (e.g. lawyers, spokespeople, etc.), and a consensus of credible reporting. \n\nIf neither of the above criteria are met, this market will resolve to \"No\". \n\nThis market only concerns Binance.com. Binance.us is not considered for this market. \n\nBinance.com withdrawals being suspended for a majority of users means that a majority of users are unable to withdraw their BTC and/or ETH from Binance (E.g. If a majority of users are unable to withdraw their ETH from Binance, it will satisfy the condition.)\n\nIf Binance.com suspends withdrawals on a day in the last week of January (e.g. They halt token withdrawals on January 30, 2023, 4 PM ET), this market will not resolve until either: 1) withdrawals are reenabled before 7 full days (168 hours) pass, resulting in a \"No\" resolution; 2) 7 full days pass (Jan 30 - Feb 6, 2023, 4 PM ET) where withdrawals are suspended, resulting in a \"Yes\" resolution.", "market_slug": "will-binancecom-become-insolvent-by-jan-31-2023", "end_date_iso": "2023-01-31T00:00:00Z", "game_start_time": null, "seconds_delay": 0, "fpmm": "******************************************", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/binance.png", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/binance.png", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "54663117294022528385601661991520131323669248856526706218963217967937304019396", "outcome": "Yes", "price": 0, "winner": false}, {"token_id": "82605074636298019650875866528882317585922531397163514824755769999593709272661", "outcome": "No", "price": 1, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0x8f6fa8af6176b37ebd99317b6a9caf28ee61f29a807dcbd56074a01eb4d975b8", "question_id": "0x5998863f58c9b2376c679e51d72c78765f3c80c3d4f5a0bf099cbcd9749baa18", "question": "Will SBF be extradited to the US by February 15, 2023?", "description": "This market asks whether <PERSON> (SBF) will set foot in the United States between December 13, 2022, and February 15, 2023, 11:59:59 PM ET. \n\nIf SBF sets foot in the United States between December 13, 2022 and February 15, 2023, 11:59:59 PM ET, this market will resolve to \"Yes\". Otherwise, this market will resolve to \"No\". \n\nFor the purpose of this market, \"setting foot in the United States\" is defined as SBF physically entering the terrestrial or maritime territory of the United States. Setting foot on US territories such as Guam and Puerto Rico will count for this market. If SBF sets foot in a United States embassy, it will not suffice to resolve this market to \"Yes.\"  Whether or not SBF enters United States airspace during the timeframe of this market will have no bearing on a positive resolution. \n\nThe primary resolution source for this information will be official information from government of the United States of America, however a consensus of credible reporting will also be used.", "market_slug": "will-sbf-be-extradited-to-the-us-by-february-15-2023", "end_date_iso": "2023-02-15T00:00:00Z", "game_start_time": null, "seconds_delay": 0, "fpmm": "******************************************", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/sbf+arrested.jpeg", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/sbf+arrested.jpeg", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "106522989057330725781727137109968308404342521445681659314101363937153135946454", "outcome": "Yes", "price": 1, "winner": false}, {"token_id": "100559920485897751472833403699186882923985677818404795840542061766510590545185", "outcome": "No", "price": 0, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0xe4d3e91c7fa9bf0391fd77114f17a2a00d2fb23c4a9567c29d3dfe4746685a94", "question_id": "0x12c27daa2731a410e90c06feb32d3f51446c56e39a297f8f0e535259eb0d3e71", "question": "World Cup 3rd Place: Croatia vs. Morocco", "description": "In the upcoming World Cup soccer game for 3rd Place, scheduled for December 17: \n\nIf Croatia wins, this market will resolve to \"Croatia\". \n\nIf Morocco wins, this market will resolve to \"Morocco\". \n\nThis market will resolve based on the winner of the game, which includes regular time, extra time, and penalties. If the game is not completed by December 24, 2022, this market will resolve 50-50.", "market_slug": "world-cup-3rd-place-croatia-vs-morocco", "end_date_iso": "2022-12-17T00:00:00Z", "game_start_time": "2022-12-17T15:00:00Z", "seconds_delay": 3, "fpmm": "0xD7c5a11707fBeAc8E2b6E27ECDF159ccf5ba54C8", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/world-cup-3rd-place-croatia-vs-morocco-80a938d3-357c-4385-9e71-bbef00fa0f76.png", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/world-cup-3rd-place-croatia-vs-morocco-80a938d3-357c-4385-9e71-bbef00fa0f76.png", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "34128948498964939853102398325306155688257269216438090996977511798069281111048", "outcome": "Croatia", "price": 1, "winner": false}, {"token_id": "60930293954076698452675765202055684980154138997530611162072208293416001004142", "outcome": "Morocco", "price": 0, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0x1e7db4f6ca3919aa41887f9701605568da64287e1e1662aa7558a749ec61146c", "question_id": "0x99432ad5ca805946d7fc6b653bb44a5d37cdcfb893b2ebba9d6897f8271d2757", "question": "World Cup Final: France vs. Argentina", "description": "In the upcoming World Cup Final, scheduled for December 18: \n\nIf France wins, this market will resolve to \"France\". \n\nIf Argentina wins, this market will resolve to \"Argentina\". \n\nThis market will resolve based on the winner of the game, which includes regular time, extra time, and penalties. If the game is not completed by December 25, 2022, this market will resolve 50-50.", "market_slug": "world-cup-final-france-vs-argentina", "end_date_iso": "2022-12-18T00:00:00Z", "game_start_time": "2022-12-18T15:00:00Z", "seconds_delay": 3, "fpmm": "0x35684edE9b6b739477d6b53C002b6Ccdb632ED3d", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/world-cup-final-france-vs-argentina-f6fc5cad-47b6-4247-a1e4-21ce9df0000e.png", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/world-cup-final-france-vs-argentina-f6fc5cad-47b6-4247-a1e4-21ce9df0000e.png", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "105392100504032111304134821100444646936144151941404393276849684670593970547907", "outcome": "France", "price": 0, "winner": false}, {"token_id": "6089433785251689770076375988640770693334877259777308137609453855410548953467", "outcome": "Argentina", "price": 1, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0x9412b855478d1dc1ba433a18de8bd082dc22b0c3a30c01f307016c2a07c11572", "question_id": "0x00fb4f6dea86c446e336a822afdec77a8b685538f1e28da95999a45b3c2b2564", "question": "Will the Fed increase interest rates by 0 bps after its February meeting?", "description": "The FED interest rates are defined in this market by the upper bound of the target federal funds range. The decisions on the target federal fund range are made by the Federal Open Market Committee (FOMC) meetings. \n\nThe resolution source for this market is the FOMC’s statement after its meeting scheduled for January 31 through February 1, 2023 according to the official calendar: https://www.federalreserve.gov/monetarypolicy/fomccalendars.htm. \n\nThis market will resolve to “Yes” if following the Federal Reserve's January/February 2023 meeting the upper bound of the target federal funds rate is exactly the same as the level it was prior to the meeting (namely it increased 0 bps). Otherwise, it will resolve to “No.” \n\nThe level and change of the target federal funds rate is also published at the official website of the Federal Reserve at https://www.federalreserve.gov/monetarypolicy/openmarket.htm. \n\nThis market may resolve as soon as the FOMC’s statement for their January/February meeting with relevant data is issued. If no statement is released by February 8, 2023, 11:59:59 PM ET, this market will resolve 50-50.\n", "market_slug": "will-the-fed-increase-interest-rates-by-0-bps-after-its-february-meeting", "end_date_iso": "2023-02-01T00:00:00Z", "game_start_time": null, "seconds_delay": 0, "fpmm": "0x6E95A260B978cFdf4909Ac708cf1F7527a32bB20", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/FED.jpg", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/FED.jpg", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "50840823040491579160844190313425221953810324677596615897772385992533441988174", "outcome": "Yes", "price": 0, "winner": false}, {"token_id": "16798240954217468982314816692525171780336564048408465031845768835317554113348", "outcome": "No", "price": 1, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0x5ef7d98e2b55953be12acc6aa5c13129bfef55a0e48b14ac0f479e08981f9bd1", "question_id": "0x22ef0a02659a76837363f0d9004b315ace028626da4f6e7a242817fba88e1131", "question": "Will the Fed increase interest rates by 25 bps after its February meeting?", "description": "The FED interest rates are defined in this market by the upper bound of the target federal funds range. The decisions on the target federal fund range are made by the Federal Open Market Committee (FOMC) meetings. \n\nThe resolution source for this market is the FOMC’s statement after its meeting scheduled for January 31 through February 1, 2023 according to the official calendar: https://www.federalreserve.gov/monetarypolicy/fomccalendars.htm. \n\nThis market will resolve to “Yes” if following the Federal Reserve's January/February 2023 meeting the upper bound of the target federal funds rate is increased exactly 25 basis points over the level it was prior to the meeting. Otherwise, it will resolve to “No.” \n\nThe level and change of the target federal funds rate is also published at the official website of the Federal Reserve at https://www.federalreserve.gov/monetarypolicy/openmarket.htm. \n\nThis market may resolve as soon as the FOMC’s statement for their January/February meeting with relevant data is issued. If no statement is released by February 8, 2023, 11:59:59 PM ET, this market will resolve 50-50.\n", "market_slug": "will-the-fed-increase-interest-rates-by-25-bps-after-its-february-meeting", "end_date_iso": "2023-02-01T00:00:00Z", "game_start_time": null, "seconds_delay": 0, "fpmm": "0xBf9b95A03641193E49314C590A3166a787712835", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/FED.jpg", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/FED.jpg", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "102122366921728883686007723122183182680201356871652918420191682492250640822215", "outcome": "Yes", "price": 1, "winner": false}, {"token_id": "1097955942803140332889254003124615337602080239229284846512443678670566232194", "outcome": "No", "price": 0, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0x6a521500cd2c0494540dc059b3eb2f7fa5d2c91dc6f3756ba2aa7a3398aec42d", "question_id": "0xa61eec0cd00ce3ebcb3a6f8f98fcac3356bad5ad9609b5ff67f064aa89c4d98d", "question": "Will the Fed increase interest rates by 50 bps after its February meeting?", "description": "The FED interest rates are defined in this market by the upper bound of the target federal funds range. The decisions on the target federal fund range are made by the Federal Open Market Committee (FOMC) meetings. \n\nThe resolution source for this market is the FOMC’s statement after its meeting scheduled for January 31 through February 1, 2023 according to the official calendar: https://www.federalreserve.gov/monetarypolicy/fomccalendars.htm. \n\nThis market will resolve to “Yes” if following the Federal Reserve's January/February 2023 meeting the upper bound of the target federal funds rate is increased exactly 50 basis points over the level it was prior to the meeting. Otherwise, it will resolve to “No.” \n\nThe level and change of the target federal funds rate is also published at the official website of the Federal Reserve at https://www.federalreserve.gov/monetarypolicy/openmarket.htm. \n\nThis market may resolve as soon as the FOMC’s statement for their January/February meeting with relevant data is issued. If no statement is released by February 8, 2023, 11:59:59 PM ET, this market will resolve 50-50.\n", "market_slug": "will-the-fed-increase-interest-rates-by-50-bps-after-its-february-meeting", "end_date_iso": "2023-02-01T00:00:00Z", "game_start_time": null, "seconds_delay": 0, "fpmm": "0x0cCDaa31Ae14c920334E511d7E8A14c1436F85FA", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/FED.jpg", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/FED.jpg", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "47964004584107225500321701776265303926067511500178575227907010687520299492312", "outcome": "Yes", "price": 0, "winner": false}, {"token_id": "62967343644001571223328834674800489901536335509964710054658772388672665324223", "outcome": "No", "price": 1, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0xc420b0080de735a6d244f694bd8c5331cd48e007aa774f25551f3afc8efb8383", "question_id": "0x1fd76817611264f5259d870c7cb03a19cbc26e9ed23b1ddd65fc3fc62a1e0741", "question": "Will the Fed increase interest rates by 75 bps after its February meeting?", "description": "The FED interest rates are defined in this market by the upper bound of the target federal funds range. The decisions on the target federal fund range are made by the Federal Open Market Committee (FOMC) meetings. \n\nThe resolution source for this market is the FOMC’s statement after its meeting scheduled for January 31 through February 1, 2023 according to the official calendar: https://www.federalreserve.gov/monetarypolicy/fomccalendars.htm. \n\nThis market will resolve to “Yes” if following the Federal Reserve's January/February 2023 meeting the upper bound of the target federal funds rate is increased exactly 75 basis points over the level it was prior to the meeting. Otherwise, it will resolve to “No.” \n\nThe level and change of the target federal funds rate is also published at the official website of the Federal Reserve at https://www.federalreserve.gov/monetarypolicy/openmarket.htm. \n\nThis market may resolve as soon as the FOMC’s statement for their January/February meeting with relevant data is issued. If no statement is released by February 8, 2023, 11:59:59 PM ET, this market will resolve 50-50.\n", "market_slug": "will-the-fed-increase-interest-rates-by-75-bps-after-its-february-meeting", "end_date_iso": "2023-02-01T00:00:00Z", "game_start_time": null, "seconds_delay": 0, "fpmm": "0xBe6eb27342611D9c1624Eaa23A4F87a54654e933", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/FED.jpg", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Repetitive-markets/FED.jpg", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "92102504882721078292711727586829784891874603520563569118796610254480436864224", "outcome": "Yes", "price": 0, "winner": false}, {"token_id": "22836153362828487281193732321408465359429588126437449158076945857786744021301", "outcome": "No", "price": 1, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0x4279448e4182d54860c52b2ccb3f908c23d8524cb946d8cb55a46678e6937075", "question_id": "0x658682f011e9eb7b53d971323af6b6be6bfc2279473c6bf712ddcbe87404b181", "question": "Will <PERSON><PERSON><PERSON><PERSON>'s \"Fee Switch\" Proposal pass?", "description": "This market will resolve to \"Yes\" if Uniswap's \"Fee Switch\" governance proposal passes before February 28, 2023, 11:59:59 PM ET. Otherwise, this market will resolve to \"No\". \n\nThis market refers to the result of the on-chain vote (Phase 3 detailed here: https://uniswap.org/governance). Note that if an on-chain vote commences and the vote results in \"against\" or otherwise does not pass, the market will immediately resolve to \"No.\"\n\nThe governance proposal discussion thread may be found here: https://gov.uniswap.org/t/fee-switch-pilot-update-vote/19514\n\n", "market_slug": "will-uniswaps-fee-switch-proposal-pass", "end_date_iso": "2023-02-28T00:00:00Z", "game_start_time": null, "seconds_delay": 0, "fpmm": "0xBA0A21c735c9B90918c6b70a116ee4593be6a9C0", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/uniswap.png", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/uniswap.png", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "18713421079181999612167315609922082035930280773422652131617717756524777433455", "outcome": "Yes", "price": 0, "winner": false}, {"token_id": "58529321329146082357359624909695341746391615648309737958025450142933601971165", "outcome": "No", "price": 1, "winner": true}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 5, "minimum_tick_size": 0.001, "condition_id": "0x3648ab7c146a9a85957e07c1d43a82272be71fde767822fd425e10ba0d6c0757", "question_id": "0x1fea5ae35e50abf6a074232210ac75c87429c2b78d2ef0d4ce205a5a590bcbbd", "question": "[Single Market] Will <PERSON> win the 2024 Republican presidential nomination?", "description": "This market will resolve to “Yes” if <PERSON> wins the 2024 nomination of the Republican Party for U.S. president. Otherwise, this market will resolve to “No”. \n\nThe resolution source for this market will be a consensus of official GOP sources, including https://www.gop.com. Any replacement of the nominee before election day will not change the resolution of the market.", "market_slug": "will-glenn-youngkin-win-the-2024-republican-presidential-nomination", "end_date_iso": "2024-09-10T00:00:00Z", "game_start_time": null, "seconds_delay": 0, "fpmm": "0x7b26b9c0b5BDE661C53eF619138C67094bDb43ae", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/glenn+youngkin.jpeg", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/glenn+youngkin.jpeg", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "2006684570364241896555682377056547936147873475006821796350135481625081540323", "outcome": "Yes", "price": 0, "winner": false}, {"token_id": "38580130314633104723975394735082095016667349014635620492800619699027053776775", "outcome": "No", "price": 1, "winner": true}], "tags": ["Politics", "tim scott", "republican party", "v<PERSON><PERSON>", "republicans", "2024 presidential election", "u.s. 2024 elections", "<PERSON>", "u.s. 2024 republican presidential nomination", "Elections", "ron desantis", "u.s. presidential election", "ben sha<PERSON><PERSON>", "2024 election", "joe rogan", "kanye west", "u.s. politics", "presidential nomination", "donald trump jr.", "presidential election 2024", "glenn youngkin", "chris christie", "mike pence", "peter thiel", "u.s. presidency", "2024 republican presidential nomination", "All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 5, "minimum_tick_size": 0.001, "condition_id": "0x65805e37d6c891808a44064013a0c80babf87010fe6e69204b17381fd0761fdd", "question_id": "0xaf5c20abbadecd87cafe64faed7ca1187a369034744adff2db48701a86b16aab", "question": "[Single Market] Will <PERSON> win the 2024 Republican presidential nomination?", "description": "This market will resolve to “Yes” if <PERSON> wins the 2024 nomination of the Republican Party for U.S. president. Otherwise, this market will resolve to “No”. \n\nThe resolution source for this market will be a consensus of official GOP sources, including https://www.gop.com. Any replacement of the nominee before election day will not change the resolution of the market.", "market_slug": "will-tim-scott-win-the-2024-republican-presidential-nomination", "end_date_iso": "2024-09-10T00:00:00Z", "game_start_time": null, "seconds_delay": 0, "fpmm": "0x719807a99a6b104C8e5DFD0Da6f5AF28ecF48868", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/tim+scott.png", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/tim+scott.png", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "15401993229934745297596131820442774063382865794132379780080338388221558196324", "outcome": "Yes", "price": 0, "winner": false}, {"token_id": "92824826487220401145378099492301263296405843389876712297661428775417612694972", "outcome": "No", "price": 1, "winner": true}], "tags": ["Politics", "tim scott", "republican party", "v<PERSON><PERSON>", "republicans", "2024 presidential election", "u.s. 2024 elections", "<PERSON>", "u.s. 2024 republican presidential nomination", "Elections", "ron desantis", "u.s. presidential election", "ben sha<PERSON><PERSON>", "2024 election", "joe rogan", "kanye west", "u.s. politics", "presidential nomination", "donald trump jr.", "presidential election 2024", "glenn youngkin", "chris christie", "mike pence", "peter thiel", "u.s. presidency", "2024 republican presidential nomination", "All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0xa7d59b976c95cd0254b93eda3ead34c3fd5cd5dca9242d3165f1bac29949c8c7", "question_id": "0xf82b34f8819307a1a54b6a1333d53d4512c27495e4e8bff6bcf3db503c7a0b30", "question": "NFL Thursday: 49ers vs. Seahawks", "description": "In the upcoming NFL game scheduled for December 15, 8:15 PM ET: \n\nIf the San Francisco 49ers win, this market will resolve to “49ers”. \n\nIf the Seattle Seahawks win, this market will resolve to “Seahawks”. \n\nIf the game ends in a tie, this market will resolve 50-50. If the game is not completed by December 22 (11:59:59 PM ET), this market will resolve 50-50.", "market_slug": "nfl-thursday-49ers-vs-seahawks", "end_date_iso": "2022-12-15T00:00:00Z", "game_start_time": "2022-12-16T01:15:00Z", "seconds_delay": 3, "fpmm": "0xd479611bfa655893d44E865CB81bAdEC893953a6", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/nfl.png", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/nfl.png", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "31664707571584204940593177344054211441028987718480967318908683018296524356976", "outcome": "49ers", "price": 1, "winner": false}, {"token_id": "50230520239169884837434218768705892293675196803960842560752832872918486506374", "outcome": "Seahawks", "price": 0, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0x88dd8f8829a7efdb458e8b551fa332d466898f01b3ed26b02727a070303f3a4b", "question_id": "0x322f63a0bd2766af1d65675a99ea49c90b1ffc01bffd1f6412a34873c887ceca", "question": "Will <PERSON><PERSON> play in the France v. Argentina World Cup Final?", "description": "<PERSON><PERSON>, forward for Real Madrid, sustained a thigh injury while training shortly before the World Cup, and has had to sit out all games he was scheduled to play for France to this point. He has refused to rule out a return to the field for the World Cup final, and excitement is growing on rumors that he may fly out to Qatar for France's upcoming match against Argentina.\n\nThis market will resolve to \"Yes\" if <PERSON><PERSON> engages in any playtime during the World Cup final match between Argentina and France, scheduled for December 18, 2022. Otherwise, this market will resolve to \"No\".\n\nThis market may only resolve once the game is complete. \n\nThe primary resolution source for this market will be official information from the French national football team, FIFA, <PERSON><PERSON>, and/or footage of the upcoming match between France and Argentina, however a consensus of credible reporting will also be used.", "market_slug": "will-karim-benzema-play-in-the-france-v-argentina-world-cup-final", "end_date_iso": "2022-12-18T00:00:00Z", "game_start_time": null, "seconds_delay": 0, "fpmm": "0xd9c4E66a120DaF896018b7eE196b65151dE6c8b4", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Ka<PERSON>+Benzema.png\n", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/Ka<PERSON>+Benzema.png\n", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "23297970555312850813091213675610498196691708563627225415425080042702356612772", "outcome": "Yes", "price": 0.0067, "winner": false}, {"token_id": "22915874961425713727313156987577198018583072296480438115193561230646321616536", "outcome": "No", "price": 0.9933, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0x7a5adffd2afdcf6be785b73d8154d04ba5add0bca4d6e1ebcbfd42102ef95677", "question_id": "0x0e5ab60aea7dac1789a156048c0b83a3dc9e1e91d98cbcac1f09c78263a9dbee", "question": "Will the US House Speaker be elected on the first ballot?", "description": "This market will resolve to \"Yes\" if the first Speaker of the House for the 118th United States Congress is elected on the first ballot. Otherwise, this market will resolve to \"No\".\n\nThe primary resolution source for this market is information from official US Government sources, however a consensus of credible reporting will also be used.\n\nAny individual elected to be, appointed to be, or serving as Speaker pro tempore in the 118th congress will have no bearing on the resolution of this market.", "market_slug": "will-the-us-house-speaker-be-elected-on-the-first-ballot", "end_date_iso": "2023-01-10T00:00:00Z", "game_start_time": null, "seconds_delay": 0, "fpmm": "0x41b0EaE31950995b29f4a699668C692457478356", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/us+house+of+representatives.png", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/us+house+of+representatives.png", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "77646341925561635619829291294569622428913213841694789786418877691584598358437", "outcome": "Yes", "price": 0, "winner": false}, {"token_id": "97369679633088835473195074283057408255920043312379820827577331539576275988996", "outcome": "No", "price": 1, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0x95d130a3b6f15fa3f90ce2576bb13e40bfc0abdf92ada4819c3cd53cce94e1b8", "question_id": "0x4b3b2d3c1a13347052f55ea36a4faaea3b6e4097b4756a309e58594a2d9661c0", "question": "NFL Monday: Rams v. Packers", "description": "In the upcoming NFL game scheduled for December 19, 8:15 PM ET: \n\nIf the Los Angeles Rams win, this market will resolve to “Rams”.\n\nIf the Green Bay Packers win, this market will resolve to “Packers”.\n\nIf this game ends in a tie, this market will resolve 50-50. If the game is not completed by December 26 (11:59:59 PM ET), this market will resolve 50-50.\n", "market_slug": "nfl-monday-rams-v-packers", "end_date_iso": "2022-12-19T00:00:00Z", "game_start_time": "2022-12-20T01:15:00Z", "seconds_delay": 3, "fpmm": "0x74976DDD674faf4C9F975a8b199c97acf902844F", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/nfl.png", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/nfl.png", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "44410223134538372855749297846022148180339814105925660101383408944923318545598", "outcome": "Rams", "price": 0.0004, "winner": false}, {"token_id": "98157034995445519609202349594950272258356854196493719992915859599537669329057", "outcome": "Packers", "price": 0.9996, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0x927b6c4df78f3a63eb467c10c84269683295d371ddcabec3a935f5b54eda9684", "question_id": "0x49ee89b3211b3f07b70e187bcc4d3f5e6f979febffa3b49958cc80b5274939e5", "question": "NBA Monday: Bucks vs. Pelicans", "description": "In the upcoming NBA game, scheduled for December 19: \n\nIf the Milwaukee Bucks win, the market will resolve to “Bucks.” \n\nIf the New Orleans Pelicans win, the market will resolve to “Pelicans.” \n\nIf the game is not completed by December 26, 2022 (11:59:59 PM ET), the market will resolve 50-50.\n\n\n", "market_slug": "nba-monday-bucks-vs-pelicans", "end_date_iso": "2022-12-19T00:00:00Z", "game_start_time": "2022-12-20T01:00:00Z", "seconds_delay": 3, "fpmm": "0xbD046179A3b84eeDbC237cc4B80D870a347333a0", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/nba.png", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/nba.png", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "56636389357506935242498095027320554655181992641635583785246942862617925408078", "outcome": "Bucks", "price": 1, "winner": false}, {"token_id": "35785866545530761083008005490561285012034555902359472480389872656057421480713", "outcome": "Pelicans", "price": 0, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0xc763341867a560c7a36dffb0d5d3680323910fdeecd14a71ae1474bb34c90c0e", "question_id": "0xbe3802fda26b1166b667b1d50582a45d1951e8cf0e3187b2aab73bebe41d14a6", "question": "NBA Tuesday: Jazz vs. <PERSON><PERSON><PERSON>", "description": "In the upcoming NBA game, scheduled for December 20: \n\nIf the Utah Jazz win, the market will resolve to “Jazz”.\n\nIf the Detroit Pistons win, the market will resolve to “Pistons”. \n\nIf this game is not completed by December 27, 2022 (11:59:59 PM ET), the market will resolve 50-50.", "market_slug": "nba-tuesday-jazz-vs-pistons", "end_date_iso": "2022-12-21T00:00:00Z", "game_start_time": "2022-12-21T00:00:00Z", "seconds_delay": 3, "fpmm": "0x48A8cf5652e21BB5F7632Aad706891f805248248", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/nba.png", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/nba.png", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "19487750836225643458019367764540893320162859688792345899789501026702130854210", "outcome": "Jazz", "price": 1, "winner": false}, {"token_id": "53132721520924439057040732871506464785716420858468005237908763078436936750744", "outcome": "<PERSON><PERSON><PERSON>", "price": 0, "winner": false}], "tags": ["All"]}, {"enable_order_book": false, "active": true, "closed": true, "archived": false, "accepting_orders": false, "accepting_order_timestamp": null, "minimum_order_size": 15, "minimum_tick_size": 0.01, "condition_id": "0xdece0a6eda3a1272aa88324e927f956903e28ea1b66f8dccbe9561e09356493a", "question_id": "0x49f9ebfa1c1f73449ec0ea30c261fce99ec8f1fd2c8a840d1550dae06e0815a6", "question": "Will the price of $ETH be above $1200 on December 30?", "description": "This market will resolve to “Yes” if the Binance 1 minute candle for ETH/USDT titled 2022/12/30 12:00 in the ET timezone (referring to 12:00 PM ET) has a final “Close” price of 1200.01 or higher, or to “No” otherwise. \n\nThe resolution source for this market is https://www.binance.com, specifically the ETH/USDT “Close” prices currently available at https://www.binance.com/en/trade/ETH_USDT?_from=markets&theme=dark&type=spot with “1m” and “Original” selected on the top bar. \n\nTo see the “Close” prices, mouse over particular candles and look at the value after “Close:” at the top of the chart. The API url https://api.binance.com/api/v3/klines?symbol=ETHUSDT&interval=1m with relevant startDate and endDate parameters may be used in the event that the chart is unavailable. \n\nPlease note that this market is about the price according to Binance ETH/USDT, not others sources or spot markets.", "market_slug": "will-the-price-of-eth-be-above-1200-on-december-30", "end_date_iso": "2022-12-30T00:00:00Z", "game_start_time": null, "seconds_delay": 0, "fpmm": "******************************************", "maker_base_fee": 0, "taker_base_fee": 0, "notifications_enabled": true, "neg_risk": false, "neg_risk_market_id": "", "neg_risk_request_id": "", "icon": "https://polymarket-upload.s3.us-east-2.amazonaws.com/eth+icon.png", "image": "https://polymarket-upload.s3.us-east-2.amazonaws.com/eth+icon.png", "rewards": {"rates": null, "min_size": 0, "max_spread": 0}, "is_50_50_outcome": false, "tokens": [{"token_id": "84711692666157930354893513911636014441723699818914499644583135188054036099098", "outcome": "Yes", "price": 0, "winner": false}, {"token_id": "37284478987169494923040397029201085027427289257902806965780305394982705766246", "outcome": "No", "price": 1, "winner": false}], "tags": ["All"]}], "next_cursor": "MTAw", "limit": 100, "count": 100}