{"retCode": 0, "retMsg": "OK", "result": {"list": [{"symbol": "BTCUSDT", "baseCoin": "BTC", "quoteCoin": "USDT", "innovation": "0", "status": "Trading", "marginTrading": "both", "lotSizeFilter": {"basePrecision": "0.000001", "quotePrecision": "0.00000001", "minOrderQty": "0.000048", "maxOrderQty": "399.9992", "minOrderAmt": "1", "maxOrderAmt": "2000000"}, "priceFilter": {"tickSize": "0.01"}}, {"symbol": "ETHUSDT", "baseCoin": "ETH", "quoteCoin": "USDT", "innovation": "0", "status": "Trading", "marginTrading": "both", "lotSizeFilter": {"basePrecision": "0.00001", "quotePrecision": "0.0000001", "minOrderQty": "0.00062", "maxOrderQty": "2430.7243559", "minOrderAmt": "1", "maxOrderAmt": "2000000"}, "priceFilter": {"tickSize": "0.01"}}]}, "time": 1694669199121}