# HyperLiquid API WebSocket高级功能

本文档详细介绍了HyperLiquid WebSocket API的高级功能，包括订阅、POST请求以及超时和心跳机制。这些功能对于构建实时交易应用程序至关重要。

## 1. 订阅功能

### 1.1 订阅消息格式

要订阅特定的数据流，需要发送订阅消息。订阅消息格式如下：

```json
{
  "method": "subscribe",
  "subscription": { ... }
}
```

订阅确认会提供时间序列数据（例如用户成交）的先前数据快照。这些快照消息标记为`isSnapshot: true`，如果之前的消息已经处理过，可以忽略。

### 1.2 可用的订阅类型

以下是可用的订阅类型及其格式：

1. **allMids**：所有中间价格
   ```json
   { "type": "allMids", "dex": "<dex>" }
   ```

2. **notification**：通知消息
   ```json
   { "type": "notification", "user": "<address>" }
   ```

3. **webData2**：用户聚合信息
   ```json
   { "type": "webData2", "user": "<address>" }
   ```

4. **candle**：K线数据
   ```json
   { "type": "candle", "coin": "<coin_symbol>", "interval": "<candle_interval>" }
   ```

5. **l2Book**：订单簿数据
   ```json
   { "type": "l2Book", "coin": "<coin_symbol>", "nSigFigs": 5, "mantissa": null }
   ```

6. **trades**：交易数据
   ```json
   { "type": "trades", "coin": "<coin_symbol>" }
   ```

7. **orderUpdates**：订单更新
   ```json
   { "type": "orderUpdates", "user": "<address>" }
   ```

8. **userEvents**：用户事件
   ```json
   { "type": "userEvents", "user": "<address>" }
   ```

9. **userFills**：用户成交
   ```json
   { "type": "userFills", "user": "<address>", "aggregateByTime": false }
   ```

10. **userFundings**：用户资金费用
    ```json
    { "type": "userFundings", "user": "<address>" }
    ```

11. **userNonFundingLedgerUpdates**：用户非资金费用账本更新
    ```json
    { "type": "userNonFundingLedgerUpdates", "user": "<address>" }
    ```

12. **activeAssetCtx**：活跃资产上下文
    ```json
    { "type": "activeAssetCtx", "coin": "<coin_symbol>" }
    ```

13. **activeAssetData**：活跃资产数据（仅支持永续合约）
    ```json
    { "type": "activeAssetData", "user": "<address>", "coin": "<coin_symbol>" }
    ```

14. **userTwapSliceFills**：用户TWAP切片成交
    ```json
    { "type": "userTwapSliceFills", "user": "<address>" }
    ```

15. **userTwapHistory**：用户TWAP历史
    ```json
    { "type": "userTwapHistory", "user": "<address>" }
    ```

16. **bbo**：最佳买卖价
    ```json
    { "type": "bbo", "coin": "<coin>" }
    ```

### 1.3 数据格式

服务器将对成功的订阅响应一条消息，其中`channel`属性设置为`"subscriptionResponse"`，以及提供原始订阅的`data`字段。然后，服务器将开始发送消息，其中`channel`属性设置为相应的订阅类型，例如`"allMids"`，而`data`字段提供订阅的数据。

### 1.4 取消订阅

要取消订阅特定的数据流，需要发送取消订阅消息，格式如下：

```json
{
  "method": "unsubscribe",
  "subscription": { ... }
}
```

`subscription`对象应与订阅时发送的原始订阅消息匹配。这允许服务器识别您想要取消订阅的特定数据流。

## 2. POST请求

WebSocket API支持通过POST请求发送通常通过HTTP API发送的请求。这些请求可以是info请求或签名操作。

### 2.1 请求格式

要通过WebSocket API发送这样的有效载荷，必须按以下方式包装：

```json
{
  "method": "post",
  "id": <number>,
  "request": {
    "type": "info" | "action",
    "payload": { ... }
  }
}
```

注意：
- `method`和`id`字段是必需的。
- 建议为每个发送的POST请求使用唯一的`id`，以便通过通道跟踪未完成的请求。
- WebSocket不支持`explorer`请求。

### 2.2 响应格式

服务器将以成功或错误响应POST请求。对于错误，返回一个`String`，反映如果通过HTTP发送请求将返回的HTTP状态代码和描述。

```json
{
  "channel": "post",
  "data": {
    "id": <number>,
    "response": {
      "type": "info" | "action" | "error",
      "payload": { ... }
    }
  }
}
```

### 2.3 示例

#### 发送L2Book信息请求：

```json
{
  "method": "post",
  "id": 123,
  "request": {
    "type": "info",
    "payload": {
      "type": "l2Book",
      "coin": "ETH",
      "nSigFigs": 5,
      "mantissa": null
    }
  }
}
```

#### 发送订单签名操作请求：

```json
{
  "method": "post",
  "id": 256,
  "request": {
    "type": "action",
    "payload": {
      "action": {
        "type": "order",
        "orders": [{
          "a": 4,
          "b": true,
          "p": "1100",
          "s": "0.2",
          "r": false,
          "t": {"limit": {"tif": "Gtc"}}
        }],
        "grouping": "na"
      },
      "nonce": 1713825891591,
      "signature": {
        "r": "...",
        "s": "...",
        "v": "..."
      },
      "vaultAddress": "0x12...3"
    }
  }
}
```

## 3. 超时和心跳机制

如果服务器在过去60秒内没有向连接发送消息，它将关闭任何连接。如果您订阅的通道不是每60秒接收一次消息，可以发送心跳消息以保持连接活跃。

### 3.1 心跳消息格式

```json
{ "method": "ping" }
```

服务器将响应：

```json
{ "channel": "pong" }
```

## 4. 数据类型定义

以下是WebSocket API中使用的一些主要数据类型的定义：

```typescript
// 交易数据
interface WsTrade {
  coin: string;
  side: string;
  px: string;
  sz: string;
  hash: string;
  time: number;
  tid: number;
  users: [string, string] // [买方, 卖方]
}

// 订单簿快照
interface WsBook {
  coin: string;
  levels: [Array<WsLevel>, Array<WsLevel>];
  time: number;
}

// 价格级别
interface WsLevel {
  px: string; // 价格
  sz: string; // 数量
  n: number; // 订单数量
}

// 用户成交
interface WsUserFills {
  isSnapshot?: boolean;
  user: string;
  fills: Array<WsFill>;
}

// 成交详情
interface WsFill {
  coin: string;
  px: string; // 价格
  sz: string; // 数量
  side: string;
  time: number;
  startPosition: string;
  dir: string; // 用于前端显示
  closedPnl: string;
  hash: string; // L1交易哈希
  oid: number; // 订单ID
  crossed: boolean; // 订单是否跨越点差（是否为主动方）
  fee: string; // 负数表示返利
  tid: number; // 唯一交易ID
  liquidation?: FillLiquidation;
  feeToken: string; // 支付费用的代币
  builderFee?: string; // 支付给构建者的金额，也包含在fee中
}
```

## 5. 最佳实践

1. **使用适当的订阅类型**：根据您的需求选择合适的订阅类型，避免订阅不必要的数据。

2. **处理断连**：实现重连逻辑，以处理可能的WebSocket连接断开。

3. **处理快照**：对于带有`isSnapshot: true`的消息，确保正确处理这些初始数据快照。

4. **限制订阅数量**：避免订阅过多的数据流，这可能会导致性能问题。

5. **实现心跳机制**：定期发送心跳消息，以保持连接活跃。

6. **使用唯一ID**：为每个POST请求使用唯一的ID，以便跟踪未完成的请求。

7. **错误处理**：实现适当的错误处理机制，以应对可能的错误响应。

## 6. 示例代码

以下是一个简单的Python示例，展示如何连接到WebSocket并实现心跳机制：

```python
import json
import time
import threading
import websocket

def on_message(ws, message):
    data = json.loads(message)
    print(f"Received: {data}")

def on_error(ws, error):
    print(f"Error: {error}")

def on_close(ws, close_status_code, close_msg):
    print("Connection closed")

def on_open(ws):
    print("Connection opened")
    
    # 订阅BTC交易数据
    subscription = {
        "method": "subscribe",
        "subscription": {
            "type": "trades",
            "coin": "BTC"
        }
    }
    ws.send(json.dumps(subscription))
    
    # 启动心跳线程
    def heartbeat():
        while True:
            time.sleep(30)  # 每30秒发送一次心跳
            ws.send(json.dumps({"method": "ping"}))
    
    threading.Thread(target=heartbeat, daemon=True).start()

if __name__ == "__main__":
    # 连接到主网WebSocket
    ws = websocket.WebSocketApp("wss://api.hyperliquid.xyz/ws",
                              on_open=on_open,
                              on_message=on_message,
                              on_error=on_error,
                              on_close=on_close)
    ws.run_forever()
```

## 7. 总结

HyperLiquid的WebSocket API提供了丰富的功能，包括订阅、POST请求以及超时和心跳机制，可用于构建高性能的交易应用程序。通过正确使用这些功能，您可以有效地管理数据流，并确保您的应用程序获取所需的实时信息。
