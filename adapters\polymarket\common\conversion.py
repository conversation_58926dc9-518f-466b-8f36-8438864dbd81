# -------------------------------------------------------------------------------------------------
#  Copyright (C) 2015-2025 Nautech Systems Pty Ltd. All rights reserved.
#  https://nautechsystems.io
#
#  Licensed under the GNU Lesser General Public License Version 3.0 (the "License");
#  You may not use this file except in compliance with the License.
#  You may obtain a copy of the License at https://www.gnu.org/licenses/lgpl-3.0.en.html
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
# -------------------------------------------------------------------------------------------------

from nautilus_trader.model.currencies import USDC_POS
from nautilus_trader.model.objects import HIGH_PRECISION
from nautilus_trader.model.objects import Money


def usdce_from_units(units: int) -> Money:
    """
    Return USDC.e money from the given units amount.

    Parameters
    ----------
    units : int
        The amount as an integer of fractional subunits.

    Returns
    -------
    Money

    """
    if HIGH_PRECISION:
        factor = 10_000_000_000
    else:
        factor = 1_000

    return Money.from_raw(int(units * factor), USDC_POS)
