# HyperLiquid API 错误响应

本文档详细介绍了HyperLiquid API可能返回的各种错误响应，以及如何处理这些错误。

## 批处理错误响应

订单和取消错误通常作为与批处理请求长度相同的向量返回。以下是可能的批处理错误响应列表：

| 错误来源 | 错误类型 | 错误字符串 |
|---------|---------|-----------|
| Order | Tick | Price must be divisible by tick size. |
| Order | MinTradeNtl | Order must have minimum value of $10 |
| Order | Margin | Insufficient margin to place order. |
| Order | ReduceOnly | Reduce only order would increase position. |
| Order | BadAloPx | Post only order would have immediately matched, bbo was {bbo}. |
| Order | IocCancel | Order could not immediately match against any resting orders. |
| Order | BadTriggerPx | Invalid TP/SL price. |
| Order | MarketOrderNoLiquidity | No liquidity available for market order. |
| Cancel | MissingOrder | Order was never placed, already canceled, or filled. |

## 预验证错误

**重要提示**：某些错误是有效载荷本身的确定性函数，这些错误会在预验证阶段提前返回。在这种情况下，整个有效载荷只返回一个错误，因为这些错误中的一些不适用于特定的订单或取消。

示例包括：
- 空订单批次
- 非只减仓的TP/SL订单
- 某些形式的价格精度验证

对于使用批处理的API用户，建议处理为多个订单的批次返回单个错误的情况。在这种情况下，响应可能会在发送到回调函数之前被复制`n`次，因为整个批次因同一原因被拒绝。

## 错误响应示例

### 订单错误

```json
{
  "status": "ok",
  "response": {
    "type": "order",
    "data": {
      "statuses": [
        {
          "error": "Order must have minimum value of $10."
        }
      ]
    }
  }
}
```

### 取消错误

```json
{
  "status": "ok",
  "response": {
    "type": "cancel",
    "data": {
      "statuses": [
        {
          "error": "Order was never placed, already canceled, or filled."
        }
      ]
    }
  }
}
```

### TWAP错误

```json
{
  "status": "ok",
  "response": {
    "type": "twapOrder",
    "data": {
      "status": {
        "error": "Invalid TWAP duration: 1 min(s)"
      }
    }
  }
}
```

## 签名错误

签名错误是最常见且最难调试的错误之一。不正确的签名会导致基于签名和有效载荷恢复不同的签名者，并导致以下错误之一：

```
"L1 error: User or API Wallet 0x0123... does not exist."
"Must deposit before performing actions. User: 0x123..."
```

其中返回的地址与您签名的钱包的公共地址不匹配。对于不同的输入，返回的地址也会变化。不正确的签名不会指示为什么不正确，这使得调试更具挑战性。

### 常见签名错误

1. **不了解有两种签名方案**：Python SDK中的方法是`sign_l1_action`与`sign_user_signed_action`。
2. **不了解msgpack的字段顺序很重要**。
3. **数字尾随零的问题**。
4. **地址字段中大写字符的问题**：建议在签名和发送之前将任何地址小写。有时字段被解析为字节，导致它在网络上自动小写。
5. **认为签名必须正确，因为本地调用recover signer会得到正确的地址**：recover signer的有效载荷是基于操作构建的，不一定匹配。

## 调试建议

1. **使用SDK**：强烈建议使用现有的SDK而不是手动生成签名。
2. **仔细阅读Python SDK**：确保您的实现与SDK完全匹配。
3. **添加日志**：如果SDK不起作用，添加日志以找出输出在哪里发生分歧。
4. **检查地址格式**：确保地址使用小写字母，并且格式正确。
5. **验证nonce**：确保nonce是唯一的，并且在适当的时间范围内。

## 错误处理最佳实践

1. **实现重试机制**：对于可重试的错误（如网络错误），实现指数退避重试机制。
2. **记录错误**：记录所有错误以便后续分析。
3. **处理批处理错误**：正确处理批处理请求中的部分成功和部分失败。
4. **验证输入**：在发送请求之前验证所有输入，以减少错误的可能性。
5. **监控错误率**：监控错误率，以便及早发现系统问题。

## 总结

理解并正确处理HyperLiquid API的错误响应对于构建可靠的交易应用程序至关重要。通过遵循本文档中的最佳实践，开发者可以减少错误的发生，并在错误发生时更有效地进行调试和恢复。
