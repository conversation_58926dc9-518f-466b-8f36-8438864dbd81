# -------------------------------------------------------------------------------------------------
#  Copyright (C) 2015-2023 Nautech Systems Pty Ltd. All rights reserved.
#  https://nautechsystems.io
#
#  Licensed under the GNU Lesser General Public License Version 3.0 (the "License");
#  You may not use this file except in compliance with the License.
#  You may obtain a copy of the License at https://www.gnu.org/licenses/lgpl-3.0.en.html
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
# -------------------------------------------------------------------------------------------------
"""
Provides Hyperliquid account HTTP API client.

This module implements account-specific operations for the Hyperliquid adapter,
combining both Info and Exchange endpoint functionality for account management.
"""

from typing import Any

from nautilus_trader.adapters.hyperliquid.http.client import HyperliquidHttpClient
from nautilus_trader.adapters.hyperliquid.http.exchange import HyperliquidExchangeHttpApi
from nautilus_trader.adapters.hyperliquid.http.market import HyperliquidMarketHttpApi


class HyperliquidAccountHttpApi:
    """
    Hyperliquid account HTTP API client.

    This class provides a unified interface for account-related operations,
    combining market data queries and trading operations.
    """

    def __init__(self, client: HyperliquidHttpClient) -> None:
        """
        Initialize the account API client.

        Parameters
        ----------
        client : HyperliquidHttpClient
            The HTTP client for making requests.
        """
        self._client = client
        self._market = HyperliquidMarketHttpApi(client)
        self._exchange = HyperliquidExchangeHttpApi(client)

    @property
    def market(self) -> HyperliquidMarketHttpApi:
        """
        Access to market data API.

        Returns
        -------
        HyperliquidMarketHttpApi
            The market data API client.
        """
        return self._market

    @property
    def exchange(self) -> HyperliquidExchangeHttpApi:
        """
        Access to exchange API.

        Returns
        -------
        HyperliquidExchangeHttpApi
            The exchange API client.
        """
        return self._exchange

    # Account Information Methods

    async def get_account_state(self, user: str, dex: str = "") -> dict[str, Any]:
        """
        Get comprehensive account state including positions and balances.

        Parameters
        ----------
        user : str
            User address in 42-character hexadecimal format.
        dex : str, default ""
            The DEX to query (empty string for default).

        Returns
        -------
        dict[str, Any]
            Complete account state information.
        """
        return await self._market.get_user_state(user, dex)

    async def get_spot_account_state(self, user: str) -> dict[str, Any]:
        """
        Get spot account state.

        Parameters
        ----------
        user : str
            User address in 42-character hexadecimal format.

        Returns
        -------
        dict[str, Any]
            Spot account state information.
        """
        return await self._market.get_spot_user_state(user)

    async def get_open_orders(self, user: str, dex: str = "") -> list[dict[str, Any]]:
        """
        Get all open orders for the account.

        Parameters
        ----------
        user : str
            User address in 42-character hexadecimal format.
        dex : str, default ""
            The DEX to query (empty string for default).

        Returns
        -------
        list[dict[str, Any]]
            List of open orders.
        """
        return await self._market.get_open_orders(user, dex)

    async def get_order_history(self, user: str) -> list[dict[str, Any]]:
        """
        Get order fill history for the account.

        Parameters
        ----------
        user : str
            User address in 42-character hexadecimal format.

        Returns
        -------
        list[dict[str, Any]]
            List of historical fills.
        """
        return await self._market.get_user_fills(user)

    async def get_portfolio_summary(self, user: str) -> list[Any]:
        """
        Get portfolio summary and performance metrics.

        Parameters
        ----------
        user : str
            User address in 42-character hexadecimal format.

        Returns
        -------
        list[Any]
            Portfolio summary data.
        """
        return await self._market.get_portfolio(user)

    async def get_sub_accounts(self, user: str) -> list[dict[str, Any]]:
        """
        Get all sub-accounts for the main account.

        Parameters
        ----------
        user : str
            User address in 42-character hexadecimal format.

        Returns
        -------
        list[dict[str, Any]]
            List of sub-accounts.
        """
        return await self._market.get_sub_accounts(user)

    # Trading Operations

    async def place_market_order(
        self,
        asset: int,
        is_buy: bool,
        size: str,
        slippage_tolerance: float = 0.05,
        reduce_only: bool = False,
        cloid: str | None = None,
    ) -> dict[str, Any]:
        """
        Place a market order with slippage protection.

        Parameters
        ----------
        asset : int
            Asset ID for the order.
        is_buy : bool
            True for buy orders, False for sell orders.
        size : str
            Order size as string.
        slippage_tolerance : float, default 0.05
            Maximum slippage tolerance (5% default).
        reduce_only : bool, default False
            Whether this is a reduce-only order.
        cloid : str | None
            Client order ID.

        Returns
        -------
        dict[str, Any]
            Order placement response.
        """
        # Get current mid price for slippage calculation
        mids = await self._market.get_all_mids()
        
        # This would need asset ID to symbol mapping
        # For now, use a placeholder price calculation
        # In a real implementation, you'd need to map asset ID to symbol
        # and get the appropriate mid price
        
        # Use IoC (Immediate or Cancel) for market orders
        order_type = {"limit": {"tif": "Ioc"}}
        
        # For demonstration, using a placeholder price
        # In practice, you'd calculate this based on mid price and slippage
        limit_px = "0"  # This needs proper implementation
        
        return await self._exchange.place_order(
            asset=asset,
            is_buy=is_buy,
            limit_px=limit_px,
            sz=size,
            reduce_only=reduce_only,
            order_type=order_type,
            cloid=cloid,
        )

    async def place_limit_order(
        self,
        asset: int,
        is_buy: bool,
        price: str,
        size: str,
        time_in_force: str = "Gtc",
        reduce_only: bool = False,
        cloid: str | None = None,
    ) -> dict[str, Any]:
        """
        Place a limit order.

        Parameters
        ----------
        asset : int
            Asset ID for the order.
        is_buy : bool
            True for buy orders, False for sell orders.
        price : str
            Limit price as string.
        size : str
            Order size as string.
        time_in_force : str, default "Gtc"
            Time in force ("Gtc", "Ioc", "Alo").
        reduce_only : bool, default False
            Whether this is a reduce-only order.
        cloid : str | None
            Client order ID.

        Returns
        -------
        dict[str, Any]
            Order placement response.
        """
        order_type = {"limit": {"tif": time_in_force}}
        
        return await self._exchange.place_order(
            asset=asset,
            is_buy=is_buy,
            limit_px=price,
            sz=size,
            reduce_only=reduce_only,
            order_type=order_type,
            cloid=cloid,
        )

    async def cancel_all_orders(self, asset: int | None = None) -> dict[str, Any]:
        """
        Cancel all open orders, optionally filtered by asset.

        Parameters
        ----------
        asset : int | None
            If specified, only cancel orders for this asset.

        Returns
        -------
        dict[str, Any]
            Cancellation response.
        """
        # Get current open orders
        user_address = self._client._vault_address or "0x0"  # This needs proper user address
        open_orders = await self.get_open_orders(user_address)
        
        if not open_orders:
            return {"status": "ok", "message": "No open orders to cancel"}
        
        # Filter by asset if specified
        if asset is not None:
            # This would need proper asset filtering based on the order structure
            # For now, cancel all orders
            pass
        
        # Build cancellation list
        cancels = []
        for order in open_orders:
            cancels.append({
                "a": order.get("asset", 0),  # This needs proper mapping
                "o": order["oid"],
            })
        
        if cancels:
            return await self._exchange.cancel_orders(cancels)
        else:
            return {"status": "ok", "message": "No matching orders to cancel"}
