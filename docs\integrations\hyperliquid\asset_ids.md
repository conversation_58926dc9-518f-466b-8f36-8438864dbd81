# HyperLiquid API 资产ID

在HyperLiquid API中，不同类型的资产（永续合约、现货和构建者部署的永续合约）使用不同的ID编码方式。本文档将详细说明这些ID的计算方法和使用场景。

## 资产ID类型

HyperLiquid平台上的资产ID分为三种主要类型：

1. **永续合约资产ID**
2. **现货资产ID**
3. **构建者部署的永续合约资产ID**

## 永续合约资产ID

永续合约端点需要一个整数作为`asset`参数，这个整数是从`meta`信息响应中找到的币种索引。

**计算方法**：直接使用币种在元数据中的索引值

**示例**：
- 在主网上，`BTC = 0`
- 其他币种按照在元数据中的顺序依次编号

## 现货资产ID

现货端点需要使用`10000 + spotInfo["index"]`作为资产ID，其中`spotInfo`是`spotMeta`中具有所需报价和基础代币的对应对象。

**计算方法**：`10000 + 现货在元数据中的索引值`

**示例**：
- 当提交`PURR/USDC`的订单时，应使用的资产ID是`10000`，因为它在现货信息中的资产索引是`0`

## 构建者部署的永续合约资产ID

构建者部署的永续合约使用更复杂的ID编码方式。

**计算方法**：`100000 + perp_dex_index * 10000 + index_in_meta`

其中：
- `perp_dex_index`：永续合约交易所索引
- `index_in_meta`：在元数据中的索引

**示例**：
- 在测试网上，`test:ABC`的`perp_dex_index = 1`，`index_in_meta = 0`，因此`asset = 110000`

**注意**：构建者部署的永续合约的名称始终采用`{dex}:{coin}`格式。

## 重要说明

1. **现货ID与代币ID不同**：需要注意的是，现货ID与代币ID是不同的概念。

2. **主网和测试网有不同的资产ID**：同一资产在主网和测试网上的ID可能不同。

## 示例对比

以HYPE为例：

| 网络 | 代币ID | 现货ID |
|------|--------|--------|
| 主网 | 150    | 107    |
| 测试网 | 1105   | 1035   |

## 最佳实践

1. **动态获取资产ID**：建议通过API动态获取资产ID，而不是硬编码，因为资产ID可能会随着时间变化。

2. **验证资产ID**：在发送交易前，验证资产ID是否正确，以避免意外交易。

3. **区分网络环境**：确保在不同的网络环境（主网、测试网）中使用正确的资产ID。

## 获取资产ID的方法

可以通过调用HyperLiquid的信息端点获取最新的资产列表和对应的ID：

```
GET https://api.hyperliquid.xyz/info
```

或测试网：

```
GET https://api.testnet.hyperliquid.xyz/info
```

响应中将包含所有可用资产及其索引，可用于计算正确的资产ID。
