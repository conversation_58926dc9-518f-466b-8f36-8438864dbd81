# -------------------------------------------------------------------------------------------------
#  Copyright (C) 2015-2023 Nautech Systems Pty Ltd. All rights reserved.
#  https://nautechsystems.io
#
#  Licensed under the GNU Lesser General Public License Version 3.0 (the "License");
#  You may not use this file except in compliance with the License.
#  You may obtain a copy of the License at https://www.gnu.org/licenses/lgpl-3.0.en.html
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
# -------------------------------------------------------------------------------------------------
"""
Provides Hyperliquid exchange HTTP API client.

This module implements the Exchange endpoint functionality for trading operations,
order management, and account operations on the Hyperliquid exchange.
"""

from typing import Any

from nautilus_trader.adapters.hyperliquid.http.client import HyperliquidHttpClient
from nautilus_trader.core.nautilus_pyo3 import HttpMethod


class HyperliquidExchangeHttpApi:
    """
    Hyperliquid exchange HTTP API client.

    This class provides methods for accessing the Hyperliquid Exchange endpoint,
    which handles trading operations and requires authentication.
    """

    def __init__(self, client: HyperliquidHttpClient) -> None:
        """
        Initialize the exchange API client.

        Parameters
        ----------
        client : HyperliquidHttpClient
            The HTTP client for making requests.
        """
        self._client = client

    # Order Management Methods

    async def place_order(
        self,
        asset: int,
        is_buy: bool,
        limit_px: str,
        sz: str,
        reduce_only: bool = False,
        order_type: dict[str, Any] | None = None,
        cloid: str | None = None,
        builder: dict[str, Any] | None = None,
    ) -> dict[str, Any]:
        """
        Place a single order.

        Parameters
        ----------
        asset : int
            Asset ID for the order.
        is_buy : bool
            True for buy orders, False for sell orders.
        limit_px : str
            Limit price as string.
        sz : str
            Size as string.
        reduce_only : bool, default False
            Whether this is a reduce-only order.
        order_type : dict[str, Any] | None
            Order type specification (e.g., {"limit": {"tif": "Gtc"}}).
        cloid : str | None
            Client order ID.
        builder : dict[str, Any] | None
            Builder information for fee sharing.

        Returns
        -------
        dict[str, Any]
            Order placement response.
        """
        order = {
            "a": asset,
            "b": is_buy,
            "p": limit_px,
            "s": sz,
            "r": reduce_only,
            "t": order_type or {"limit": {"tif": "Gtc"}},
        }
        if cloid:
            order["c"] = cloid

        action = {
            "type": "order",
            "orders": [order],
            "grouping": "na",
        }
        if builder:
            action["builder"] = builder

        return await self._client.post_l1_action(action)

    async def place_orders(
        self,
        orders: list[dict[str, Any]],
        grouping: str = "na",
        builder: dict[str, Any] | None = None,
    ) -> dict[str, Any]:
        """
        Place multiple orders in a batch.

        Parameters
        ----------
        orders : list[dict[str, Any]]
            List of order specifications.
        grouping : str, default "na"
            Order grouping type ("na", "normalTpsl", "positionTpsl").
        builder : dict[str, Any] | None
            Builder information for fee sharing.

        Returns
        -------
        dict[str, Any]
            Batch order placement response.
        """
        action = {
            "type": "order",
            "orders": orders,
            "grouping": grouping,
        }
        if builder:
            action["builder"] = builder

        return await self._client.post_l1_action(action)

    async def cancel_order(self, asset: int, oid: int) -> dict[str, Any]:
        """
        Cancel a single order.

        Parameters
        ----------
        asset : int
            Asset ID.
        oid : int
            Order ID to cancel.

        Returns
        -------
        dict[str, Any]
            Cancellation response.
        """
        action = {
            "type": "cancel",
            "cancels": [{"a": asset, "o": oid}],
        }
        return await self._client.post_l1_action(action)

    async def cancel_orders(self, cancels: list[dict[str, Any]]) -> dict[str, Any]:
        """
        Cancel multiple orders in a batch.

        Parameters
        ----------
        cancels : list[dict[str, Any]]
            List of cancellation specifications with "a" (asset) and "o" (oid).

        Returns
        -------
        dict[str, Any]
            Batch cancellation response.
        """
        action = {
            "type": "cancel",
            "cancels": cancels,
        }
        return await self._client.post_l1_action(action)

    async def cancel_order_by_cloid(self, asset: int, cloid: str) -> dict[str, Any]:
        """
        Cancel an order by client order ID.

        Parameters
        ----------
        asset : int
            Asset ID.
        cloid : str
            Client order ID to cancel.

        Returns
        -------
        dict[str, Any]
            Cancellation response.
        """
        action = {
            "type": "cancelByCloid",
            "cancels": [{"asset": asset, "cloid": cloid}],
        }
        return await self._client.post_l1_action(action)

    async def cancel_orders_by_cloid(self, cancels: list[dict[str, Any]]) -> dict[str, Any]:
        """
        Cancel multiple orders by client order ID.

        Parameters
        ----------
        cancels : list[dict[str, Any]]
            List of cancellation specifications with "asset" and "cloid".

        Returns
        -------
        dict[str, Any]
            Batch cancellation response.
        """
        action = {
            "type": "cancelByCloid",
            "cancels": cancels,
        }
        return await self._client.post_l1_action(action)

    async def modify_order(
        self,
        oid: int,
        order: dict[str, Any],
    ) -> dict[str, Any]:
        """
        Modify an existing order.

        Parameters
        ----------
        oid : int
            Order ID to modify.
        order : dict[str, Any]
            New order specification.

        Returns
        -------
        dict[str, Any]
            Modification response.
        """
        action = {
            "type": "modify",
            "oid": oid,
            "order": order,
        }
        return await self._client.post_l1_action(action)

    async def batch_modify_orders(self, modifies: list[dict[str, Any]]) -> dict[str, Any]:
        """
        Modify multiple orders in a batch.

        Parameters
        ----------
        modifies : list[dict[str, Any]]
            List of modification specifications with "oid" and "order".

        Returns
        -------
        dict[str, Any]
            Batch modification response.
        """
        action = {
            "type": "batchModify",
            "modifies": modifies,
        }
        return await self._client.post_l1_action(action)

    # Account Management Methods

    async def update_leverage(
        self,
        asset: int,
        is_cross: bool,
        leverage: int,
    ) -> dict[str, Any]:
        """
        Update leverage for an asset.

        Parameters
        ----------
        asset : int
            Asset ID.
        is_cross : bool
            True for cross margin, False for isolated margin.
        leverage : int
            New leverage value.

        Returns
        -------
        dict[str, Any]
            Leverage update response.
        """
        action = {
            "type": "updateLeverage",
            "asset": asset,
            "isCross": is_cross,
            "leverage": leverage,
        }
        return await self._client.post_l1_action(action)

    async def schedule_cancel(self, time: int | None = None) -> dict[str, Any]:
        """
        Schedule a time to cancel all open orders (dead man's switch).

        Parameters
        ----------
        time : int | None
            Time in UTC milliseconds to cancel orders. If None, unsets any scheduled cancel.

        Returns
        -------
        dict[str, Any]
            Schedule cancel response.
        """
        action = {"type": "scheduleCancel"}
        if time is not None:
            action["time"] = time

        return await self._client.post_l1_action(action)

    # Transfer Methods

    async def usd_transfer(
        self,
        destination: str,
        amount: str,
        hyperliquid_chain: str = "Mainnet",
        signature_chain_id: str = "0xa4b1",
    ) -> dict[str, Any]:
        """
        Transfer USD to another address.

        Parameters
        ----------
        destination : str
            Destination address.
        amount : str
            Amount to transfer.
        hyperliquid_chain : str, default "Mainnet"
            Chain to use ("Mainnet" or "Testnet").
        signature_chain_id : str, default "0xa4b1"
            Chain ID for signature.

        Returns
        -------
        dict[str, Any]
            Transfer response.
        """
        # Use the signer's specialized USD transfer method
        if not self._client._signer:
            msg = "Authentication required for USD transfers"
            raise ValueError(msg)

        nonce = self._client._clock.timestamp_ms()

        # Use the signer's sign_usd_transfer_action method
        signature = self._client._signer.sign_usd_transfer_action(
            destination=destination,
            amount=amount,
            nonce=nonce,
        )

        payload = {
            "action": {
                "type": "usdSend",
                "hyperliquidChain": hyperliquid_chain,
                "signatureChainId": signature_chain_id,
                "destination": destination,
                "amount": amount,
                "time": nonce,
            },
            "nonce": nonce,
            "signature": signature,
        }

        return await self._client.send_request(
            http_method=HttpMethod.POST,
            endpoint="/exchange",
            payload=payload,
            signed=False,  # Already signed
            sign_type="raw",
        )

    async def withdraw(
        self,
        destination: str,
        amount: str,
        hyperliquid_chain: str = "Mainnet",
        signature_chain_id: str = "0xa4b1",
    ) -> dict[str, Any]:
        """
        Initiate a withdrawal.

        Parameters
        ----------
        destination : str
            Destination address.
        amount : str
            Amount to withdraw.
        hyperliquid_chain : str, default "Mainnet"
            Chain to use ("Mainnet" or "Testnet").
        signature_chain_id : str, default "0xa4b1"
            Chain ID for signature.

        Returns
        -------
        dict[str, Any]
            Withdrawal response.
        """
        # Use the signer's specialized withdraw method
        if not self._client._signer:
            msg = "Authentication required for withdrawals"
            raise ValueError(msg)

        nonce = self._client._clock.timestamp_ms()

        # Use the signer's sign_withdraw_action method
        signature = self._client._signer.sign_withdraw_action(
            destination=destination,
            amount=amount,
            nonce=nonce,
        )

        payload = {
            "action": {
                "type": "withdraw3",
                "hyperliquidChain": hyperliquid_chain,
                "signatureChainId": signature_chain_id,
                "amount": amount,
                "time": nonce,
                "destination": destination,
            },
            "nonce": nonce,
            "signature": signature,
        }

        return await self._client.send_request(
            http_method=HttpMethod.POST,
            endpoint="/exchange",
            payload=payload,
            signed=False,  # Already signed
            sign_type="raw",
        )

    async def usd_class_transfer(
        self,
        amount: str,
        to_perp: bool,
    ) -> dict[str, Any]:
        """
        Transfer between spot and perpetual accounts.

        Parameters
        ----------
        amount : str
            Amount to transfer.
        to_perp : bool
            True to transfer to perpetual account, False to transfer to spot.

        Returns
        -------
        dict[str, Any]
            Transfer response.
        """
        action = {
            "type": "usdClassTransfer",
            "amount": amount,
            "toPerp": to_perp,
        }
        return await self._client.post_l1_action(action)

    # Sub-account Methods

    async def create_sub_account(self, name: str) -> dict[str, Any]:
        """
        Create a new sub-account.

        Parameters
        ----------
        name : str
            Name for the sub-account.

        Returns
        -------
        dict[str, Any]
            Sub-account creation response.
        """
        action = {
            "type": "createSubAccount",
            "name": name,
        }
        return await self._client.post_l1_action(action)

    async def sub_account_transfer(
        self,
        sub_account_user: str,
        is_deposit: bool,
        usd: int,
    ) -> dict[str, Any]:
        """
        Transfer funds to/from a sub-account.

        Parameters
        ----------
        sub_account_user : str
            Sub-account address.
        is_deposit : bool
            True for deposit to sub-account, False for withdrawal.
        usd : int
            Amount in USD (as integer).

        Returns
        -------
        dict[str, Any]
            Sub-account transfer response.
        """
        # Use the signer's specialized sub-account transfer method
        if not self._client._signer:
            msg = "Authentication required for sub-account transfers"
            raise ValueError(msg)

        nonce = self._client._clock.timestamp_ms()

        # Use the signer's sign_sub_account_transfer_action method
        signature = self._client._signer.sign_sub_account_transfer_action(
            sub_account_user=sub_account_user,
            is_deposit=is_deposit,
            usd=usd,
            nonce=nonce,
        )

        payload = {
            "action": {
                "type": "subAccountTransfer",
                "subAccountUser": sub_account_user,
                "isDeposit": is_deposit,
                "usd": usd,
            },
            "nonce": nonce,
            "signature": signature,
        }

        return await self._client.send_request(
            http_method=HttpMethod.POST,
            endpoint="/exchange",
            payload=payload,
            signed=False,  # Already signed
            sign_type="raw",
        )

    async def update_isolated_margin(
        self,
        asset: int,
        is_buy: bool,
        ntli: int,
    ) -> dict[str, Any]:
        """
        Update isolated margin for a position.

        Parameters
        ----------
        asset : int
            Asset ID.
        is_buy : bool
            Direction of margin adjustment.
        ntli : int
            Notional value in USD (as integer).

        Returns
        -------
        dict[str, Any]
            Margin update response.
        """
        action = {
            "type": "updateIsolatedMargin",
            "asset": asset,
            "isBuy": is_buy,
            "ntli": ntli,
        }
        return await self._client.post_l1_action(action)
