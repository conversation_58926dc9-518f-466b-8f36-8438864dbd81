# Interactive Brokers

```{eval-rst}
.. automodule:: nautilus_trader.adapters.interactive_brokers
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

## Common

```{eval-rst}
.. automodule:: nautilus_trader.adapters.interactive_brokers.common
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

## Config

```{eval-rst}
.. automodule:: nautilus_trader.adapters.interactive_brokers.config
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

## Data

```{eval-rst}
.. automodule:: nautilus_trader.adapters.interactive_brokers.data
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

## Execution

```{eval-rst}
.. automodule:: nautilus_trader.adapters.interactive_brokers.execution
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

## Factories

```{eval-rst}
.. automodule:: nautilus_trader.adapters.interactive_brokers.factories
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

## Providers

```{eval-rst}
.. automodule:: nautilus_trader.adapters.interactive_brokers.providers
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```
