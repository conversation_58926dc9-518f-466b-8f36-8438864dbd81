# -------------------------------------------------------------------------------------------------
#  Copyright (C) 2015-2025 Nautech Systems Pty Ltd. All rights reserved.
#  https://nautechsystems.io
#
#  Licensed under the GNU Lesser General Public License Version 3.0 (the "License");
#  You may not use this file except in compliance with the License.
#  You may obtain a copy of the License at https://www.gnu.org/licenses/lgpl-3.0.en.html
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
# -------------------------------------------------------------------------------------------------

import asyncio
import json
from collections import defaultdict
from collections.abc import Awaitable
from collections.abc import Callable

import msgspec

from nautilus_trader.adapters.hyperliquid.common.constants import (
    HYPERLIQUID_WS_URL_MAINNET,
    HYPERLIQUID_WS_URL_TESTNET,
)
from nautilus_trader.common.component import LiveClock
from nautilus_trader.common.component import Logger
from nautilus_trader.common.enums import LogColor
from nautilus_trader.core.nautilus_pyo3 import Quota
from nautilus_trader.core.nautilus_pyo3 import WebSocketClient
from nautilus_trader.core.nautilus_pyo3 import WebSocketClientError
from nautilus_trader.core.nautilus_pyo3 import WebSocketConfig


# Hyperliquid WebSocket subscription types
WS_SUBSCRIPTION_TYPES = {
    "allMids": "All mid prices",
    "notification": "Notification messages",
    "webData2": "User aggregated information",
    "candle": "Candlestick data",
    "l2Book": "Order book data",
    "trades": "Trade data",
    "orderUpdates": "Order updates",
    "userEvents": "User events",
    "userFills": "User fills",
    "userFundings": "User funding fees",
    "userNonFundingLedgerUpdates": "User non-funding ledger updates",
    "activeAssetCtx": "Active asset context",
    "activeAssetData": "Active asset data",
    "userTwapSliceFills": "User TWAP slice fills",
    "userTwapHistory": "User TWAP history",
    "bbo": "Best bid/offer",
}

# Mainnet and testnet timeout settings (milliseconds)
# WebSocket connection will be closed if no message is received within 60 seconds
HYPERLIQUID_WS_TIMEOUT_MS = 60_000


class HyperliquidWebSocketClient:
    """
    Provide a Hyperliquid streaming WebSocket client.

    Supports market data subscriptions, user data subscriptions, automatic reconnection,
    heartbeat mechanism, and sending POST requests through WebSocket.

    Parameters
    ----------
    clock : LiveClock
        The clock instance.
    handler : Callable[[bytes], None]
        The callback handler for message events.
    handler_reconnect : Callable[..., Awaitable[None]], optional
        The callback handler to be called on reconnect.
    testnet : bool, default False
        Whether to use testnet.
    base_url : str, optional
        The WebSocket base URL. If None, automatically selected based on testnet parameter.
    loop : asyncio.AbstractEventLoop, optional
        The event loop.
    subscription_rate_limit_per_second : int, default 5
        The maximum number of subscription requests per second.
    ping_interval : int, default 50
        The heartbeat interval in seconds.

    """

    def __init__(
        self,
        clock: LiveClock,
        handler: Callable[[bytes], None],
        handler_reconnect: Callable[..., Awaitable[None]] | None = None,
        testnet: bool = False,
        base_url: str | None = None,
        loop: asyncio.AbstractEventLoop | None = None,
        subscription_rate_limit_per_second: int = 5,
        ping_interval: int = 50,
    ) -> None:
        self._clock = clock
        self._log = Logger(type(self).__name__)
        self._handler = handler
        self._handler_reconnect = handler_reconnect
        self._loop = loop or asyncio.get_event_loop()
        self._subscription_rate_limit = subscription_rate_limit_per_second
        self._ping_interval = ping_interval

        # Set WebSocket URL
        if base_url:
            self._base_url = base_url
        else:
            self._base_url = HYPERLIQUID_WS_URL_TESTNET if testnet else HYPERLIQUID_WS_URL_MAINNET

        # WebSocket client
        self._client: WebSocketClient | None = None
        self._is_connected = False
        self._reconnecting = False
        self._reconnect_task: asyncio.Task | None = None
        self._heartbeat_task: asyncio.Task | None = None

        # Subscription management
        self._subscriptions: dict[str, set[str]] = defaultdict(set)  # type -> parameter sets
        self._pending_requests: dict[int, asyncio.Future] = {}  # request ID -> Future
        self._next_request_id = 1

        # Message decoder
        self._decoder = msgspec.json.Decoder()

    @property
    def is_connected(self) -> bool:
        """
        Return whether the client is connected.

        Returns
        -------
        bool

        """
        return self._is_connected and self._client is not None and self._client.is_active()

    @property
    def base_url(self) -> str:
        """
        Return the WebSocket base URL.

        Returns
        -------
        str

        """
        return self._base_url

    @property
    def subscriptions(self) -> dict[str, set[str]]:
        """
        Return the current active subscriptions.

        Returns
        -------
        dict[str, set[str]]

        """
        return self._subscriptions

    def has_subscription(self, subscription_type: str, params_str: str) -> bool:
        """
        Check if a specific subscription type and parameters are already subscribed.

        Parameters
        ----------
        subscription_type : str
            The subscription type.
        params_str : str
            The parameters JSON string.

        Returns
        -------
        bool

        """
        return params_str in self._subscriptions.get(subscription_type, set())

    async def connect(self) -> None:
        """
        Connect to the WebSocket server.
        """
        if self.is_connected:
            self._log.warning("Already connected to WebSocket server")
            return

        self._log.debug(f"Connecting to {self._base_url}")

        config = WebSocketConfig(
            url=self._base_url,
            handler=self._handle_message,
            heartbeat=self._ping_interval // 2,  # Heartbeat check interval is half of ping interval
            heartbeat_msg=msgspec.json.encode({"method": "ping"}).decode('utf-8'),
            headers=[],
        )

        try:
            connection_start = self._clock.timestamp_ms()
            self._log.debug(f"Connecting to WebSocket at {connection_start}ms")

            self._client = await WebSocketClient.connect(
                config=config,
                post_reconnection=self.reconnect,
                default_quota=Quota.rate_per_second(self._subscription_rate_limit),
            )

            connection_time = self._clock.timestamp_ms() - connection_start
            self._is_connected = True
            self._reconnecting = False
            self._log.info(f"Connected to {self._base_url} in {connection_time}ms", LogColor.GREEN)

            # Start heartbeat task
            self._start_heartbeat()

            # Resubscribe to all subscriptions
            await self._resubscribe_all()
        except Exception as e:
            self._log.error(f"WebSocket connection failed: {e}")
            self._is_connected = False
            self._schedule_reconnect()

    async def get_connection_status(self) -> dict[str, object]:
        """
        Get connection status information.

        Returns
        -------
        dict[str, object]
            Connection status information including connection state, base URL,
            active subscription counts, etc.

        """
        subscription_counts = {sub_type: len(params) for sub_type, params in self._subscriptions.items()}
        total_subscriptions = sum(len(params) for params in self._subscriptions.values())

        return {
            "connected": self.is_connected,
            "base_url": self._base_url,
            "subscription_types": list(self._subscriptions.keys()),
            "subscription_counts_by_type": subscription_counts,
            "total_subscriptions": total_subscriptions,
            "pending_requests": len(self._pending_requests),
        }

    async def disconnect(self) -> None:
        """
        Disconnect from the WebSocket server.
        """
        if not self.is_connected or self._client is None:
            self._log.warning("Not connected to WebSocket server, cannot disconnect")
            return

        # Stop heartbeat task
        if self._heartbeat_task is not None:
            self._heartbeat_task.cancel()
            self._heartbeat_task = None

        # Stop reconnect task
        if self._reconnect_task is not None:
            self._reconnect_task.cancel()
            self._reconnect_task = None

        try:
            await self._client.disconnect()
            self._is_connected = False
            self._client = None
            self._log.info(f"Disconnected from {self._base_url}", LogColor.GREEN)
        except WebSocketClientError as e:
            self._log.error(f"Failed to disconnect WebSocket: {e}")

    def reconnect(self) -> None:
        """
        Reconnect to the WebSocket server.
        """
        if self._reconnecting:
            return

        self._log.warning(f"Reconnecting to {self._base_url}", LogColor.YELLOW)
        self._reconnecting = True
        self._schedule_reconnect()

    def _schedule_reconnect(self) -> None:
        """
        Schedule a reconnection task.
        """
        if self._reconnect_task is None or self._reconnect_task.done():
            self._reconnect_task = self._loop.create_task(self._delayed_reconnect())

    async def _delayed_reconnect(self) -> None:
        """
        Delayed reconnection with exponential backoff strategy and jitter.
        Uses the Full Jitter algorithm to prevent reconnection storms.
        """
        import random

        retry_count = 0
        max_retries = 10
        base_delay = 1.0  # Base delay in seconds
        max_delay = 60.0  # Maximum delay in seconds

        while retry_count < max_retries and not self.is_connected:
            try:
                # Calculate exponential backoff delay with jitter
                exp_delay = min(base_delay * (2**retry_count), max_delay)
                # Add jitter: random value between 0 and exp_delay
                delay = random.uniform(0, exp_delay)

                self._log.info(f"Waiting {delay:.1f}s before reconnection attempt {retry_count + 1}/{max_retries}")
                await asyncio.sleep(delay)

                # Attempt to reconnect
                await self.connect()

                # If connection successful, call reconnect callback and resubscribe
                if self.is_connected:
                    # Resubscribe to all previous subscriptions
                    await self._resubscribe_all()

                    # Call reconnect handler if available
                    if self._handler_reconnect:
                        await self._handler_reconnect()

                    self._log.info(f"Successfully reconnected to {self._base_url}", LogColor.GREEN)
                    break

                retry_count += 1

            except Exception as e:
                self._log.error(f"Reconnection failed: {e}")
                retry_count += 1
                retry_count += 1

        if not self.is_connected:
            self._log.error(f"Reconnection failed after {max_retries} attempts, giving up")

        self._reconnecting = False

    def _start_heartbeat(self) -> None:
        """
        Start the heartbeat task.
        """
        if self._heartbeat_task is not None:
            self._heartbeat_task.cancel()

        self._heartbeat_task = self._loop.create_task(self._heartbeat_loop())

    async def _heartbeat_loop(self) -> None:
        """
        Heartbeat loop.
        """
        while self.is_connected:
            try:
                await asyncio.sleep(self._ping_interval)  # Use configured heartbeat interval
                await self._send({"method": "ping"})
            except asyncio.CancelledError:
                break
            except Exception as e:
                self._log.error(f"Failed to send heartbeat: {e}")

    def _handle_message(self, raw: bytes) -> None:
        """
        Handle WebSocket messages.

        Parameters
        ----------
        raw : bytes
            The raw WebSocket message.

        """
        try:
            message = msgspec.json.decode(raw)

            # Handle heartbeat response
            if message.get("channel") == "pong":
                self._log.debug("Received heartbeat response")
                return

            # Handle subscription response
            if message.get("channel") == "subscriptionResponse":
                self._log.debug(f"Subscription confirmed: {message}")
                return

            # Handle POST response
            if message.get("channel") == "post":
                self._handle_post_response(message)
                return

            # Handle other messages
            self._handler(raw)
        except Exception as e:
            self._log.error(f"Failed to handle WebSocket message: {e}")

    def _handle_post_response(self, message: dict[str, object]) -> None:
        """
        Handle POST responses.

        Parameters
        ----------
        message : dict[str, object]
            The POST response message.

        """
        data = message.get("data", {})
        request_id = data.get("id")

        if request_id is not None and request_id in self._pending_requests:
            future = self._pending_requests.pop(request_id)
            future.set_result(data.get("response"))

    async def _send(self, message: dict[str, object]) -> None:
        """
        Send a WebSocket message.

        Parameters
        ----------
        message : dict[str, object]
            The message to send.

        """
        if not self.is_connected or self._client is None:
            self._log.warning("Not connected to WebSocket server, cannot send message")
            return

        try:
            message_json = msgspec.json.encode(message)
            self._log.debug(f"Sending: {message_json}")
            await self._client.send(message_json)
        except WebSocketClientError as e:
            self._log.error(f"Failed to send message: {e}")
            self._schedule_reconnect()

    async def subscribe(self, subscription_type: str, **params) -> None:
        """
        Subscribe to a specific data stream type.

        Parameters
        ----------
        subscription_type : str
            The subscription type, e.g., "trades", "l2Book", "candle", etc.
        **params
            Subscription parameters, e.g., coin="BTC", interval="1m", etc.

        """
        # Build subscription message
        subscription = {
            "method": "subscribe",
            "subscription": {
                "type": subscription_type,
                **params,
            },
        }

        # Convert parameters to JSON string as unique identifier
        params_str = msgspec.json.encode(params, sort_keys=True).decode('utf-8')

        # Check if already subscribed
        if self.has_subscription(subscription_type, params_str):
            self._log.warning(f"Already subscribed to {subscription_type}: {params}")
            return

        # Record subscription
        self._subscriptions[subscription_type].add(params_str)

        # Send subscription request
        await self._send(subscription)
        self._log.info(f"Subscribed to {subscription_type}: {params}", LogColor.BLUE)

    async def unsubscribe(self, subscription_type: str, **params) -> None:
        """
        Unsubscribe from a specific data stream type.

        Parameters
        ----------
        subscription_type : str
            The subscription type, e.g., "trades", "l2Book", "candle", etc.
        **params
            Subscription parameters, e.g., coin="BTC", interval="1m", etc.

        """
        # Build unsubscription message
        unsubscription = {
            "method": "unsubscribe",
            "subscription": {
                "type": subscription_type,
                **params,
            },
        }

        # Convert parameters to JSON string as unique identifier
        params_str = msgspec.json.encode(params, sort_keys=True).decode('utf-8')

        # Check if subscribed
        if not self.has_subscription(subscription_type, params_str):
            self._log.warning(f"Not subscribed to {subscription_type}: {params}")
            return

        # Remove subscription record
        self._subscriptions[subscription_type].discard(params_str)
        if not self._subscriptions[subscription_type]:
            del self._subscriptions[subscription_type]

        # Send unsubscription request
        await self._send(unsubscription)
        self._log.info(f"Unsubscribed from {subscription_type}: {params}", LogColor.BLUE)

    async def _resubscribe_all(self) -> None:
        """
        Resubscribe to all subscriptions with rate limiting to avoid excessive requests.
        """
        if not self._subscriptions:
            return

        self._log.info("Resubscribing to all data streams...")

        # Calculate total subscriptions and estimated completion time
        total_subscriptions = sum(len(params) for params in self._subscriptions.values())
        delay_per_sub = 1.0 / self._subscription_rate_limit
        estimated_time = total_subscriptions * delay_per_sub

        if total_subscriptions > 10:
            self._log.info(f"Restoring {total_subscriptions} subscriptions, estimated time: {estimated_time:.1f}s")

        subscription_count = 0
        for subscription_type, params_set in self._subscriptions.items():
            for params_str in params_set:
                params = json.loads(params_str)
                await self.subscribe(subscription_type, **params)
                subscription_count += 1

                # Add appropriate delay based on rate limiting
                if subscription_count % self._subscription_rate_limit == 0:
                    await asyncio.sleep(1.0)  # Wait 1 second after every rate_limit requests
                else:
                    await asyncio.sleep(delay_per_sub)  # Otherwise add small delay per rate limit

        self._log.info(f"Successfully resubscribed to {subscription_count} data streams")

    async def bulk_subscribe(self, subscriptions: list[dict[str, object]]) -> None:
        """
        Bulk subscribe to multiple data streams with rate limiting.

        Parameters
        ----------
        subscriptions : list[dict[str, object]]
            List of subscriptions, each subscription is a dictionary containing type and other parameters.

        """
        if not subscriptions:
            return

        self._log.info(f"Bulk subscribing to {len(subscriptions)} data streams")

        for i, sub in enumerate(subscriptions):
            sub_type = sub.pop("type")
            await self.subscribe(sub_type, **sub)

            # Add appropriate delay
            if (i + 1) % self._subscription_rate_limit == 0:
                await asyncio.sleep(1.0)
            else:
                await asyncio.sleep(1.0 / self._subscription_rate_limit)

    async def post_request(self, request_type: str, payload: dict[str, object], timeout: float = 10.0) -> dict[str, object]:
        """
        Send a POST request through WebSocket.

        Parameters
        ----------
        request_type : str
            The request type, "info" or "action".
        payload : dict[str, object]
            The request payload.
        timeout : float, default 10.0
            The request timeout in seconds.

        Returns
        -------
        dict[str, object]
            The response data.

        Raises
        ------
        RuntimeError
            If not connected to WebSocket server.
        TimeoutError
            If request times out.
        ValueError
            If request type is invalid.

        """
        if not self.is_connected:
            msg = "Not connected to WebSocket server, cannot send POST request"
            raise RuntimeError(msg)

        # Validate request type
        if request_type not in ["info", "action"]:
            msg = f"Invalid request type: {request_type}, must be 'info' or 'action'"
            raise ValueError(msg)

        # Generate request ID
        request_id = self._next_request_id
        self._next_request_id += 1

        # Create Future
        future = self._loop.create_future()
        self._pending_requests[request_id] = future

        # Build request message
        message = {
            "method": "post",
            "id": request_id,
            "request": {"type": request_type, "payload": payload},
        }

        # Send request
        await self._send(message)

        try:
            # Wait for response
            response = await asyncio.wait_for(future, timeout)
            return response
        except asyncio.TimeoutError:
            self._pending_requests.pop(request_id, None)
            raise TimeoutError(f"POST request timeout: {request_type}") from None

    async def post_info_request(self, info_type: str, **params) -> dict[str, object]:
        """
        Send an information request.

        Parameters
        ----------
        info_type : str
            The information type, e.g., "l2Book", "trades", "candle", etc.
        **params
            Request parameters.

        Returns
        -------
        dict[str, object]
            The response data.

        """
        payload = {"type": info_type, **params}
        return await self.post_request("info", payload)

    async def post_action_request(
        self,
        action: dict[str, object],
        nonce: int,
        signature: dict[str, str],
        vault_address: str,
    ) -> dict[str, object]:
        """
        Send an action request (requires signature).

        Parameters
        ----------
        action : dict[str, object]
            The action object containing trading instructions, etc.
        nonce : int
            The nonce value.
        signature : dict[str, str]
            The signature object containing r, s, v.
        vault_address : str
            The vault address.

        Returns
        -------
        dict[str, object]
            The response data.

        """
        payload = {
            "action": action,
            "nonce": nonce,
            "signature": signature,
            "vaultAddress": vault_address,
        }
        return await self.post_request("action", payload)

    # Convenience subscription methods

    async def subscribe_trades(self, coin: str) -> None:
        """
        Subscribe to trade data.

        Parameters
        ----------
        coin : str
            The coin symbol.

        """
        await self.subscribe("trades", coin=coin)

    async def unsubscribe_trades(self, coin: str) -> None:
        """
        Unsubscribe from trade data.

        Parameters
        ----------
        coin : str
            The coin symbol.

        """
        await self.unsubscribe("trades", coin=coin)

    async def subscribe_order_book(self, coin: str, n_sig_figs: int = 5) -> None:
        """
        Subscribe to order book data.

        Parameters
        ----------
        coin : str
            The coin symbol.
        n_sig_figs : int, default 5
            The number of significant figures.

        """
        await self.subscribe("l2Book", coin=coin, nSigFigs=n_sig_figs, mantissa=None)

    async def unsubscribe_order_book(self, coin: str) -> None:
        """
        Unsubscribe from order book data.

        Parameters
        ----------
        coin : str
            The coin symbol.

        """
        await self.unsubscribe("l2Book", coin=coin)

    async def subscribe_candles(self, coin: str, interval: str) -> None:
        """
        Subscribe to candlestick data.

        Parameters
        ----------
        coin : str
            The coin symbol.
        interval : str
            The candlestick interval, e.g., "1m", "5m", "1h", etc.

        """
        await self.subscribe("candle", coin=coin, interval=interval)

    async def unsubscribe_candles(self, coin: str, interval: str) -> None:
        """
        Unsubscribe from candlestick data.

        Parameters
        ----------
        coin : str
            The coin symbol.
        interval : str
            The candlestick interval, e.g., "1m", "5m", "1h", etc.

        """
        await self.unsubscribe("candle", coin=coin, interval=interval)

    async def subscribe_bbo(self, coin: str) -> None:
        """
        Subscribe to best bid/offer data.

        Parameters
        ----------
        coin : str
            The coin symbol.

        """
        await self.subscribe("bbo", coin=coin)

    async def unsubscribe_bbo(self, coin: str) -> None:
        """
        Unsubscribe from best bid/offer data.

        Parameters
        ----------
        coin : str
            The coin symbol.

        """
        await self.unsubscribe("bbo", coin=coin)

    async def subscribe_all_mids(self, dex: str | None = None) -> None:
        """
        Subscribe to all mid prices.

        Parameters
        ----------
        dex : str, optional
            The exchange name.

        """
        params = {}
        if dex is not None:
            params["dex"] = dex
        await self.subscribe("allMids", **params)

    async def unsubscribe_all_mids(self, dex: str | None = None) -> None:
        """
        Unsubscribe from all mid prices.

        Parameters
        ----------
        dex : str, optional
            The exchange name.

        """
        params = {}
        if dex is not None:
            params["dex"] = dex
        await self.unsubscribe("allMids", **params)

    async def subscribe_user_events(self, user: str) -> None:
        """
        Subscribe to user events.

        Parameters
        ----------
        user : str
            The user address.

        """
        await self.subscribe("userEvents", user=user)

    async def unsubscribe_user_events(self, user: str) -> None:
        """
        Unsubscribe from user events.

        Parameters
        ----------
        user : str
            The user address.

        """
        await self.unsubscribe("userEvents", user=user)

    async def subscribe_order_updates(self, user: str) -> None:
        """
        Subscribe to order updates.

        Parameters
        ----------
        user : str
            The user address.

        """
        await self.subscribe("orderUpdates", user=user)

    async def unsubscribe_order_updates(self, user: str) -> None:
        """
        Unsubscribe from order updates.

        Parameters
        ----------
        user : str
            The user address.

        """
        await self.unsubscribe("orderUpdates", user=user)

    async def subscribe_user_fills(self, user: str, aggregate_by_time: bool = False) -> None:
        """
        Subscribe to user fills.

        Parameters
        ----------
        user : str
            The user address.
        aggregate_by_time : bool, default False
            Whether to aggregate by time.

        """
        await self.subscribe("userFills", user=user, aggregateByTime=aggregate_by_time)

    async def unsubscribe_user_fills(self, user: str) -> None:
        """
        Unsubscribe from user fills.

        Parameters
        ----------
        user : str
            The user address.

        """
        await self.unsubscribe("userFills", user=user)

    async def subscribe_user_fundings(self, user: str) -> None:
        """
        Subscribe to user funding fees.

        Parameters
        ----------
        user : str
            The user address.

        """
        await self.subscribe("userFundings", user=user)

    async def unsubscribe_user_fundings(self, user: str) -> None:
        """
        Unsubscribe from user funding fees.

        Parameters
        ----------
        user : str
            The user address.

        """
        await self.unsubscribe("userFundings", user=user)

    async def subscribe_user_non_funding_ledger_updates(self, user: str) -> None:
        """
        Subscribe to user non-funding ledger updates.

        Parameters
        ----------
        user : str
            The user address.

        """
        await self.subscribe("userNonFundingLedgerUpdates", user=user)

    async def unsubscribe_user_non_funding_ledger_updates(self, user: str) -> None:
        """
        Unsubscribe from user non-funding ledger updates.

        Parameters
        ----------
        user : str
            The user address.

        """
        await self.unsubscribe("userNonFundingLedgerUpdates", user=user)

    async def subscribe_notification(self, user: str) -> None:
        """
        Subscribe to notification messages.

        Parameters
        ----------
        user : str
            The user address.

        """
        await self.subscribe("notification", user=user)

    async def unsubscribe_notification(self, user: str) -> None:
        """
        Unsubscribe from notification messages.

        Parameters
        ----------
        user : str
            The user address.

        """
        await self.unsubscribe("notification", user=user)

    async def subscribe_web_data2(self, user: str) -> None:
        """
        Subscribe to user aggregated information.

        Parameters
        ----------
        user : str
            The user address.

        """
        await self.subscribe("webData2", user=user)

    async def unsubscribe_web_data2(self, user: str) -> None:
        """
        Unsubscribe from user aggregated information.

        Parameters
        ----------
        user : str
            The user address.

        """
        await self.unsubscribe("webData2", user=user)

    async def subscribe_active_asset_ctx(self, coin: str) -> None:
        """
        Subscribe to active asset context.

        Parameters
        ----------
        coin : str
            The coin symbol.

        """
        await self.subscribe("activeAssetCtx", coin=coin)

    async def unsubscribe_active_asset_ctx(self, coin: str) -> None:
        """
        Unsubscribe from active asset context.

        Parameters
        ----------
        coin : str
            The coin symbol.

        """
        await self.unsubscribe("activeAssetCtx", coin=coin)

    async def subscribe_active_asset_data(self, user: str, coin: str) -> None:
        """
        Subscribe to active asset data (perpetuals only).

        Parameters
        ----------
        user : str
            The user address.
        coin : str
            The coin symbol.

        """
        await self.subscribe("activeAssetData", user=user, coin=coin)

    async def unsubscribe_active_asset_data(self, user: str, coin: str) -> None:
        """
        Unsubscribe from active asset data.

        Parameters
        ----------
        user : str
            The user address.
        coin : str
            The coin symbol.

        """
        await self.unsubscribe("activeAssetData", user=user, coin=coin)

    async def subscribe_user_twap_slice_fills(self, user: str) -> None:
        """
        Subscribe to user TWAP slice fills.

        Parameters
        ----------
        user : str
            The user address.

        """
        await self.subscribe("userTwapSliceFills", user=user)

    async def unsubscribe_user_twap_slice_fills(self, user: str) -> None:
        """
        Unsubscribe from user TWAP slice fills.

        Parameters
        ----------
        user : str
            The user address.

        """
        await self.unsubscribe("userTwapSliceFills", user=user)

    async def subscribe_user_twap_history(self, user: str) -> None:
        """
        Subscribe to user TWAP history.

        Parameters
        ----------
        user : str
            The user address.

        """
        await self.subscribe("userTwapHistory", user=user)

    async def unsubscribe_user_twap_history(self, user: str) -> None:
        """
        Unsubscribe from user TWAP history.

        Parameters
        ----------
        user : str
            The user address.

        """
        await self.unsubscribe("userTwapHistory", user=user)

    # Convenience WebSocket operation methods

    async def get_l2_orderbook(self, coin: str, n_sig_figs: int = 5) -> dict[str, object]:
        """
        Get L2 order book snapshot (via WebSocket POST request).

        Parameters
        ----------
        coin : str
            The coin symbol.
        n_sig_figs : int, default 5
            The number of significant figures.

        Returns
        -------
        dict[str, object]
            The order book data.

        """
        payload = {
            "type": "l2Book",
            "coin": coin,
            "nSigFigs": n_sig_figs,
            "mantissa": None,
        }
        return await self.post_info_request(**payload)

    async def get_recent_trades(self, coin: str) -> dict[str, object]:
        """
        Get recent trades (via WebSocket POST request).

        Parameters
        ----------
        coin : str
            The coin symbol.

        Returns
        -------
        dict[str, object]
            The trade data.

        """
        payload = {"type": "trades", "coin": coin}
        return await self.post_info_request(**payload)

    async def get_candle_data(self, coin: str, interval: str) -> dict[str, object]:
        """
        Get candlestick data (via WebSocket POST request).

        Parameters
        ----------
        coin : str
            The coin symbol.
        interval : str
            The candlestick interval, e.g., "1m", "5m", "1h", etc.

        Returns
        -------
        dict[str, object]
            The candlestick data.

        """
        payload = {"type": "candle", "coin": coin, "interval": interval}
        return await self.post_info_request(**payload)

    async def subscribe_to_all_markets(
        self,
        instruments: list[str],
        data_types: list[str] | None = None,
    ) -> None:
        """
        Subscribe to specified data types for all markets.

        Parameters
        ----------
        instruments : list[str]
            List of coin symbols.
        data_types : list[str], optional
            List of data types to subscribe to, e.g., ["trades", "l2Book", "bbo"].
            If None, defaults to subscribing to trades and l2Book.

        """
        if data_types is None:
            data_types = ["trades", "l2Book"]

        subscriptions = []
        for instrument in instruments:
            for data_type in data_types:
                if data_type == "trades":
                    subscriptions.append({"type": "trades", "coin": instrument})
                elif data_type == "l2Book":
                    subscriptions.append({
                        "type": "l2Book",
                        "coin": instrument,
                        "nSigFigs": 5,
                        "mantissa": None,
                    })
                elif data_type == "bbo":
                    subscriptions.append({"type": "bbo", "coin": instrument})
                elif data_type == "candle":
                    # Default to 1-minute candlesticks
                    subscriptions.append({
                        "type": "candle",
                        "coin": instrument,
                        "interval": "1m",
                    })
                elif data_type == "activeAssetCtx":
                    subscriptions.append({"type": "activeAssetCtx", "coin": instrument})

        await self.bulk_subscribe(subscriptions)

    async def place_order_via_ws(
        self,
        action: dict[str, object],
        nonce: int,
        signature: dict[str, str],
        vault_address: str,
    ) -> dict[str, object]:
        """
        Place an order via WebSocket.

        Parameters
        ----------
        action : dict[str, object]
            The order action object with type "order".
        nonce : int
            The nonce value.
        signature : dict[str, str]
            The signature object.
        vault_address : str
            The vault address.

        Returns
        -------
        dict[str, object]
            The response data.

        """
        return await self.post_action_request(action, nonce, signature, vault_address)

    async def cancel_order_via_ws(
        self,
        action: dict[str, object],
        nonce: int,
        signature: dict[str, str],
        vault_address: str,
    ) -> dict[str, object]:
        """
        Cancel an order via WebSocket.

        Parameters
        ----------
        action : dict[str, object]
            The cancel action object with type "cancel".
        nonce : int
            The nonce value.
        signature : dict[str, str]
            The signature object.
        vault_address : str
            The vault address.

        Returns
        -------
        dict[str, object]
            The response data.

        """
        return await self.post_action_request(action, nonce, signature, vault_address)

    async def cancel_order_by_cloid_via_ws(
        self,
        action: dict[str, object],
        nonce: int,
        signature: dict[str, str],
        vault_address: str,
    ) -> dict[str, object]:
        """
        Cancel an order by client order ID via WebSocket.

        Parameters
        ----------
        action : dict[str, object]
            The cancel action object with type "cancelByCloid".
        nonce : int
            The nonce value.
        signature : dict[str, str]
            The signature object.
        vault_address : str
            The vault address.

        Returns
        -------
        dict[str, object]
            The response data.

        """
        return await self.post_action_request(action, nonce, signature, vault_address)

    async def modify_order_via_ws(
        self,
        action: dict[str, object],
        nonce: int,
        signature: dict[str, str],
        vault_address: str,
    ) -> dict[str, object]:
        """
        Modify an order via WebSocket.

        Parameters
        ----------
        action : dict[str, object]
            The modify action object with type "modify".
        nonce : int
            The nonce value.
        signature : dict[str, str]
            The signature object.
        vault_address : str
            The vault address.

        Returns
        -------
        dict[str, object]
            The response data.

        """
        return await self.post_action_request(action, nonce, signature, vault_address)

    async def batch_modify_orders_via_ws(
        self,
        action: dict[str, object],
        nonce: int,
        signature: dict[str, str],
        vault_address: str,
    ) -> dict[str, object]:
        """
        Batch modify orders via WebSocket.

        Parameters
        ----------
        action : dict[str, object]
            The batch modify action object with type "batchModify".
        nonce : int
            The nonce value.
        signature : dict[str, str]
            The signature object.
        vault_address : str
            The vault address.

        Returns
        -------
        dict[str, object]
            The response data.

        """
        return await self.post_action_request(action, nonce, signature, vault_address)

    async def schedule_cancel_via_ws(
        self,
        action: dict[str, object],
        nonce: int,
        signature: dict[str, str],
        vault_address: str,
    ) -> dict[str, object]:
        """
        Schedule cancel (dead man's switch) via WebSocket.

        Parameters
        ----------
        action : dict[str, object]
            The schedule cancel action object with type "scheduleCancel".
        nonce : int
            The nonce value.
        signature : dict[str, str]
            The signature object.
        vault_address : str
            The vault address.

        Returns
        -------
        dict[str, object]
            The response data.

        """
        return await self.post_action_request(action, nonce, signature, vault_address)

    async def update_leverage_via_ws(
        self,
        action: dict[str, object],
        nonce: int,
        signature: dict[str, str],
        vault_address: str,
    ) -> dict[str, object]:
        """
        Update leverage via WebSocket.

        Parameters
        ----------
        action : dict[str, object]
            The update leverage action object with type "updateLeverage".
        nonce : int
            The nonce value.
        signature : dict[str, str]
            The signature object.
        vault_address : str
            The vault address.

        Returns
        -------
        dict[str, object]
            The response data.

        """
        return await self.post_action_request(action, nonce, signature, vault_address)
