#!/usr/bin/env python3
# -------------------------------------------------------------------------------------------------
#  Copyright (C) 2015-2025 Nautech Systems Pty Ltd. All rights reserved.
#  https://nautechsystems.io
#
#  Licensed under the GNU Lesser General Public License Version 3.0 (the "License");
#  You may not use this file except in compliance with the License.
#  You may obtain a copy of the License at https://www.gnu.org/licenses/lgpl-3.0.en.html
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
# -------------------------------------------------------------------------------------------------

"""
Example usage of the Hyperliquid WebSocket client.

This example demonstrates how to use the new Hyperliquid WebSocket client
following Nautilus trader patterns.
"""

import asyncio
import json

from nautilus_trader.adapters.hyperliquid.websocket.client_new import HyperliquidWebSocketClient
from nautilus_trader.common.component import LiveClock


def handle_message(raw: bytes) -> None:
    """
    Handle incoming WebSocket messages.

    Parameters
    ----------
    raw : bytes
        The raw WebSocket message.
    """
    try:
        message = json.loads(raw.decode("utf-8"))
        print(f"Received message: {json.dumps(message, indent=2)}")
    except Exception as e:
        print(f"Error handling message: {e}")


async def handle_reconnect() -> None:
    """
    Handle reconnection events.
    """
    print("WebSocket reconnected successfully!")


async def main() -> None:
    """
    Main example function demonstrating WebSocket client usage.
    """
    # Create clock instance
    clock = LiveClock()

    # Create WebSocket client
    client = HyperliquidWebSocketClient(
        clock=clock,
        handler=handle_message,
        handler_reconnect=handle_reconnect,
        testnet=True,  # Use testnet for example
        subscription_rate_limit_per_second=5,
        ping_interval=50,
    )

    try:
        # Connect to WebSocket
        print("Connecting to Hyperliquid WebSocket...")
        await client.connect()

        # Subscribe to some market data
        print("Subscribing to BTC trades...")
        await client.subscribe_trades("BTC")

        print("Subscribing to ETH order book...")
        await client.subscribe_order_book("ETH", n_sig_figs=5)

        print("Subscribing to SOL 1-minute candles...")
        await client.subscribe_candles("SOL", "1m")

        print("Subscribing to BTC best bid/offer...")
        await client.subscribe_bbo("BTC")

        # Get connection status
        status = await client.get_connection_status()
        print(f"Connection status: {json.dumps(status, indent=2)}")

        # Example of bulk subscription
        bulk_subscriptions = [
            {"type": "trades", "coin": "DOGE"},
            {"type": "l2Book", "coin": "DOGE", "nSigFigs": 5, "mantissa": None},
            {"type": "bbo", "coin": "DOGE"},
        ]
        print("Bulk subscribing to DOGE data...")
        await client.bulk_subscribe(bulk_subscriptions)

        # Example of WebSocket POST request
        try:
            print("Getting BTC order book snapshot via POST...")
            orderbook = await client.get_l2_orderbook("BTC", n_sig_figs=5)
            print(f"Order book snapshot: {json.dumps(orderbook, indent=2)}")
        except Exception as e:
            print(f"Error getting order book: {e}")

        # Example of order management via WebSocket (requires signing)
        # Note: These are examples of the method signatures - actual usage requires proper signing
        print("\nOrder management methods available:")
        print("- place_order_via_ws: Place orders")
        print("- cancel_order_via_ws: Cancel orders by order ID")
        print("- cancel_order_by_cloid_via_ws: Cancel orders by client order ID")
        print("- modify_order_via_ws: Modify existing orders")
        print("- batch_modify_orders_via_ws: Batch modify multiple orders")
        print("- schedule_cancel_via_ws: Schedule cancel (dead man's switch)")
        print("- update_leverage_via_ws: Update leverage settings")
        print("\nThese methods require proper action objects, nonces, signatures, and vault addresses.")

        # Example of subscribing to all markets
        instruments = ["BTC", "ETH", "SOL"]
        data_types = ["trades", "bbo"]
        print(f"Subscribing to {data_types} for {instruments}...")
        await client.subscribe_to_all_markets(instruments, data_types)

        # Keep running for a while to receive messages
        print("Listening for messages for 30 seconds...")
        await asyncio.sleep(30)

        # Unsubscribe from some data
        print("Unsubscribing from BTC trades...")
        await client.unsubscribe_trades("BTC")

        print("Unsubscribing from ETH order book...")
        await client.unsubscribe_order_book("ETH")

        # Get updated connection status
        status = await client.get_connection_status()
        print(f"Updated connection status: {json.dumps(status, indent=2)}")

        # Keep running for a bit more
        await asyncio.sleep(10)

    except KeyboardInterrupt:
        print("Interrupted by user")
    except Exception as e:
        print(f"Error: {e}")
    finally:
        # Disconnect
        print("Disconnecting...")
        await client.disconnect()
        print("Disconnected successfully")


if __name__ == "__main__":
    # Run the example
    asyncio.run(main())
