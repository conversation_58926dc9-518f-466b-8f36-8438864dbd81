# -------------------------------------------------------------------------------------------------
#  Copyright (C) 2015-2023 Nautech Systems Pty Ltd. All rights reserved.
#  https://nautechsystems.io
#
#  Licensed under the GNU Lesser General Public License Version 3.0 (the "License");
#  You may not use this file except in compliance with the License.
#  You may obtain a copy of the License at https://www.gnu.org/licenses/lgpl-3.0.en.html
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
# -------------------------------------------------------------------------------------------------

import json
import time
from typing import Any, Dict, List, Optional, Union

import msgspec

import nautilus_trader
from nautilus_trader.adapters.hyperliquid.common.constants import (
    HYPERLIQUID_HTTP_URL_MAINNET,
    HYPERLIQUID_HTTP_URL_TESTNET,
)
from nautilus_trader.adapters.hyperliquid.util.signing import HyperliquidSigner, NonceManager
from nautilus_trader.common.component import LiveClock
from nautilus_trader.common.component import Logger
from nautilus_trader.common.enums import LogColor
from nautilus_trader.core.nautilus_pyo3 import HttpClient
from nautilus_trader.core.nautilus_pyo3 import HttpMethod
from nautilus_trader.core.nautilus_pyo3 import HttpResponse
from nautilus_trader.core.nautilus_pyo3 import Quota


DEFAULT_REQUEST_TIMEOUT_SECONDS = 10


class HyperliquidHttpClient:
    """
    HyperLiquid HTTP client for making API requests.

    This client handles authentication, signing, and rate limiting for HyperLiquid API requests.
    """

    def __init__(
        self,
        clock: LiveClock,
        api_key: Optional[str] = None,
        api_secret: Optional[str] = None,
        testnet: bool = False,
        base_url: Optional[str] = None,
        ratelimiter_quotas: Optional[List[tuple[str, Quota]]] = None,
        ratelimiter_default_quota: Optional[Quota] = None,
    ) -> None:
        """
        Initialize a new HyperLiquid HTTP client.

        Parameters
        ----------
        clock : LiveClock
            The clock for the component.
        api_key : Optional[str]
            The API key for authentication (vault address if using vault).
        api_secret : Optional[str]
            The API secret (private key) for authentication.
        testnet : bool, default False
            If True, use testnet API endpoints.
        base_url : Optional[str]
            Optional override for the base URL.
        ratelimiter_quotas : Optional[List[tuple[str, Quota]]]
            Optional rate limiter quotas for specific endpoints.
        ratelimiter_default_quota : Optional[Quota]
            Optional default rate limiter quota.
        """
        self._clock = clock
        self._log = Logger(type(self).__name__)
        self._api_key = api_key
        self._api_secret = api_secret
        self._testnet = testnet
        self._is_mainnet = not testnet

        # Set up base URL
        if base_url:
            self._base_url = base_url
        else:
            self._base_url = HYPERLIQUID_HTTP_URL_TESTNET if testnet else HYPERLIQUID_HTTP_URL_MAINNET

        # Set up headers
        self._headers = {
            "Content-Type": "application/json",
            "User-Agent": nautilus_trader.NAUTILUS_USER_AGENT,
        }

        # Create HTTP client
        self._client = HttpClient(
            keyed_quotas=ratelimiter_quotas or [],
            default_quota=ratelimiter_default_quota,
        )

        # Set up signer and nonce manager if credentials provided
        self._signer = None
        if api_secret:
            self._signer = HyperliquidSigner(private_key_hex=api_secret, clock=self._clock)

        self._nonce_manager = NonceManager(clock=self._clock)

    @property
    def base_url(self) -> str:
        """
        Return the base URL being used by the client.

        Returns
        -------
        str
        """
        return self._base_url

    @property
    def is_testnet(self) -> bool:
        """
        Return whether the client is using testnet.

        Returns
        -------
        bool
        """
        return self._testnet

    @property
    def is_authenticated(self) -> bool:
        """
        Return whether the client is authenticated.

        Returns
        -------
        bool
        """
        return self._signer is not None

    async def send_request(
        self,
        http_method: HttpMethod,
        endpoint: str,
        params: Optional[Dict[str, Any]] = None,
        data: Optional[Dict[str, Any]] = None,
        signed: bool = False,
        timeout_secs: int = DEFAULT_REQUEST_TIMEOUT_SECONDS,
        ratelimiter_keys: Optional[List[str]] = None,
    ) -> Dict[str, Any]:
        """
        Send an HTTP request to the HyperLiquid API.

        Parameters
        ----------
        http_method : HttpMethod
            The HTTP method to use.
        endpoint : str
            The API endpoint to request.
        params : Optional[Dict[str, Any]]
            Optional query parameters for GET requests.
        data : Optional[Dict[str, Any]]
            Optional data for POST requests.
        signed : bool, default False
            Whether the request requires authentication.
        timeout_secs : int, default DEFAULT_REQUEST_TIMEOUT_SECONDS
            Request timeout in seconds.
        ratelimiter_keys : Optional[List[str]]
            Optional rate limiter keys.

        Returns
        -------
        Dict[str, Any]
            The parsed JSON response.

        Raises
        ------
        ValueError
            If authentication is required but not configured.
        """
        url = f"{self._base_url}{endpoint}"

        # Handle authentication
        if signed:
            if not self._signer:
                raise ValueError("Authentication required but no API secret provided")

            # For GET requests with params, add them to the URL
            if http_method == HttpMethod.GET and params:
                # TODO: Implement signed GET requests if needed
                pass

            # For POST requests, sign the data
            elif http_method == HttpMethod.POST:
                if data is None:
                    data = {}

                # Get next nonce
                nonce = self._nonce_manager.get_next_nonce()

                # Sign the request
                signed_data = self._signer.sign_l1_action(
                    action=data,
                    vault_address=self._api_key,
                    nonce=nonce,
                    is_mainnet=self._is_mainnet,
                )

                # Replace data with signed data
                data = signed_data

        # Prepare request body
        body = None
        if http_method == HttpMethod.POST and data:
            body = msgspec.json.encode(data)

        # Add query parameters to URL for GET requests
        if http_method == HttpMethod.GET and params:
            import urllib.parse
            query_string = urllib.parse.urlencode(params)
            url = f"{url}?{query_string}"

        # Log request details
        self._log.debug(f"Request: {http_method} {url}", LogColor.MAGENTA)
        if data:
            self._log.debug(f"Request data: {data}", LogColor.MAGENTA)

        # Send request
        response: HttpResponse = await self._client.request(
            method=http_method,
            url=url,
            headers=self._headers,
            body=body,
            keys=ratelimiter_keys,
            timeout_secs=timeout_secs,
        )

        # Parse response
        response_json = json.loads(response.body)

        # Log response
        self._log.debug(f"Response: {response_json}", LogColor.MAGENTA)

        return response_json

    async def get(
        self,
        endpoint: str,
        params: Optional[Dict[str, Any]] = None,
        signed: bool = False,
        timeout_secs: int = DEFAULT_REQUEST_TIMEOUT_SECONDS,
        ratelimiter_keys: Optional[List[str]] = None,
    ) -> Dict[str, Any]:
        """
        Send a GET request to the HyperLiquid API.

        Parameters
        ----------
        endpoint : str
            The API endpoint to request.
        params : Optional[Dict[str, Any]]
            Optional query parameters.
        signed : bool, default False
            Whether the request requires authentication.
        timeout_secs : int, default DEFAULT_REQUEST_TIMEOUT_SECONDS
            Request timeout in seconds.
        ratelimiter_keys : Optional[List[str]]
            Optional rate limiter keys.

        Returns
        -------
        Dict[str, Any]
            The parsed JSON response.
        """
        return await self.send_request(
            http_method=HttpMethod.GET,
            endpoint=endpoint,
            params=params,
            signed=signed,
            timeout_secs=timeout_secs,
            ratelimiter_keys=ratelimiter_keys,
        )

    async def post(
        self,
        endpoint: str,
        data: Optional[Dict[str, Any]] = None,
        signed: bool = False,
        timeout_secs: int = DEFAULT_REQUEST_TIMEOUT_SECONDS,
        ratelimiter_keys: Optional[List[str]] = None,
    ) -> Dict[str, Any]:
        """
        Send a POST request to the HyperLiquid API.

        Parameters
        ----------
        endpoint : str
            The API endpoint to request.
        data : Optional[Dict[str, Any]]
            Optional data to send.
        signed : bool, default False
            Whether the request requires authentication.
        timeout_secs : int, default DEFAULT_REQUEST_TIMEOUT_SECONDS
            Request timeout in seconds.
        ratelimiter_keys : Optional[List[str]]
            Optional rate limiter keys.

        Returns
        -------
        Dict[str, Any]
            The parsed JSON response.
        """
        return await self.send_request(
            http_method=HttpMethod.POST,
            endpoint=endpoint,
            data=data,
            signed=signed,
            timeout_secs=timeout_secs,
            ratelimiter_keys=ratelimiter_keys,
        )