# -------------------------------------------------------------------------------------------------
#  Copyright (C) 2015-2023 Nautech Systems Pty Ltd. All rights reserved.
#  https://nautechsystems.io
#
#  Licensed under the GNU Lesser General Public License Version 3.0 (the "License");
#  You may not use this file except in compliance with the License.
#  You may obtain a copy of the License at https://www.gnu.org/licenses/lgpl-3.0.en.html
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
# -------------------------------------------------------------------------------------------------
"""
Provides a Hyperliquid HTTP client for API requests.

This module implements the core HTTP client functionality for the Hyperliquid adapter,
following nautilus_trader patterns and providing authentication, signing, and error handling.
"""

import json
from typing import Any
from urllib.parse import urlencode

import msgspec

import nautilus_trader
from nautilus_trader.adapters.hyperliquid.common.constants import (
    HYPERLIQUID_HTTP_URL_MAINNET,
    HYPERLIQUID_HTTP_URL_TESTNET,
)
from nautilus_trader.adapters.hyperliquid.http.error import HyperliquidError
from nautilus_trader.adapters.hyperliquid.util.signing import HyperliquidSigner
from nautilus_trader.common.component import LiveClock
from nautilus_trader.common.component import Logger
from nautilus_trader.common.enums import LogColor
from nautilus_trader.core.nautilus_pyo3 import HttpClient
from nautilus_trader.core.nautilus_pyo3 import HttpMethod
from nautilus_trader.core.nautilus_pyo3 import HttpResponse
from nautilus_trader.core.nautilus_pyo3 import Quota


DEFAULT_REQUEST_TIMEOUT_SECONDS = 10


class HyperliquidHttpClient:
    """
    Hyperliquid HTTP client for making API requests.

    This client handles authentication, signing, and rate limiting for Hyperliquid API requests.
    Follows nautilus_trader patterns for HTTP client implementation.
    """

    def __init__(
        self,
        clock: LiveClock,
        private_key: str | None = None,
        vault_address: str | None = None,
        testnet: bool = False,
        base_url: str | None = None,
        ratelimiter_quotas: list[tuple[str, Quota]] | None = None,
        ratelimiter_default_quota: Quota | None = None,
    ) -> None:
        """
        Initialize a new Hyperliquid HTTP client.

        Parameters
        ----------
        clock : LiveClock
            The clock for the component.
        private_key : str | None
            The private key for signing requests (hex format starting with 0x).
        vault_address : str | None
            The vault address for sub-account trading.
        testnet : bool, default False
            If True, use testnet API endpoints.
        base_url : str | None
            Optional override for the base URL.
        ratelimiter_quotas : list[tuple[str, Quota]] | None
            Optional rate limiter quotas for specific endpoints.
        ratelimiter_default_quota : Quota | None
            Optional default rate limiter quota.
        """
        self._clock = clock
        self._log = Logger(type(self).__name__)
        self._private_key = private_key
        self._vault_address = vault_address
        self._testnet = testnet
        self._is_mainnet = not testnet

        # Set up base URL
        if base_url:
            self._base_url = base_url
        else:
            self._base_url = HYPERLIQUID_HTTP_URL_TESTNET if testnet else HYPERLIQUID_HTTP_URL_MAINNET

        # Set up headers
        self._headers = {
            "Content-Type": "application/json",
            "User-Agent": nautilus_trader.NAUTILUS_USER_AGENT,
        }

        # Create HTTP client
        self._client = HttpClient(
            keyed_quotas=ratelimiter_quotas or [],
            default_quota=ratelimiter_default_quota,
        )

        # Set up signer if credentials provided
        self._signer = None
        if private_key:
            self._signer = HyperliquidSigner(private_key_hex=private_key, clock=self._clock)

    @property
    def base_url(self) -> str:
        """
        Return the base URL being used by the client.

        Returns
        -------
        str
        """
        return self._base_url

    @property
    def is_testnet(self) -> bool:
        """
        Return whether the client is using testnet.

        Returns
        -------
        bool
        """
        return self._testnet

    @property
    def is_authenticated(self) -> bool:
        """
        Return whether the client is authenticated.

        Returns
        -------
        bool
        """
        return self._signer is not None

    async def send_request(
        self,
        http_method: HttpMethod,
        endpoint: str,
        payload: dict[str, Any] | None = None,
        signed: bool = False,
        timeout_secs: int = DEFAULT_REQUEST_TIMEOUT_SECONDS,
        ratelimiter_keys: list[str] | None = None,
    ) -> dict[str, Any]:
        """
        Send an HTTP request to the Hyperliquid API.

        Parameters
        ----------
        http_method : HttpMethod
            The HTTP method to use.
        endpoint : str
            The API endpoint to request.
        payload : dict[str, Any] | None
            Optional payload for the request.
        signed : bool, default False
            Whether the request requires authentication.
        timeout_secs : int, default DEFAULT_REQUEST_TIMEOUT_SECONDS
            Request timeout in seconds.
        ratelimiter_keys : list[str] | None
            Optional rate limiter keys.

        Returns
        -------
        dict[str, Any]
            The parsed JSON response.

        Raises
        ------
        ValueError
            If authentication is required but not configured.
        HyperliquidError
            If the API returns an error response.
        """
        url = f"{self._base_url}{endpoint}"

        # Handle authentication for signed requests
        if signed:
            if not self._signer:
                msg = "Authentication required but no private key provided"
                raise ValueError(msg)

            if payload is None:
                payload = {}

            # Get current timestamp as nonce
            nonce = self._clock.timestamp_ms()

            # Sign the request
            signed_payload = self._signer.sign_l1_action(
                action=payload,
                vault_address=self._vault_address,
                nonce=nonce,
                is_mainnet=self._is_mainnet,
            )

            # Replace payload with signed payload
            payload = signed_payload

        # Prepare request body
        body = None
        if payload:
            if http_method == HttpMethod.GET:
                # Add query parameters to URL for GET requests
                query_string = urlencode(payload)
                url = f"{url}?{query_string}"
            else:
                # Encode as JSON for POST requests
                body = msgspec.json.encode(payload)

        # Log request details
        self._log.debug(f"Request: {http_method.name} {endpoint}", LogColor.MAGENTA)

        # Send request
        response: HttpResponse = await self._client.request(
            method=http_method,
            url=url,
            headers=self._headers,
            body=body,
            keys=ratelimiter_keys,
            timeout_secs=timeout_secs,
        )

        # Handle HTTP errors
        if response.status >= 400:
            try:
                error_data = msgspec.json.decode(response.body) if response.body else None
            except msgspec.DecodeError:
                error_data = response.body.decode() if response.body else "Unknown error"

            raise HyperliquidError(
                status=response.status,
                message=error_data,
                headers=response.headers,
            )

        # Parse and return response
        try:
            response_data = msgspec.json.decode(response.body)
        except msgspec.DecodeError as e:
            msg = f"Failed to decode response: {e}"
            raise HyperliquidError(status=response.status, message=msg) from e

        return response_data

    async def get(
        self,
        endpoint: str,
        payload: dict[str, Any] | None = None,
        signed: bool = False,
        timeout_secs: int = DEFAULT_REQUEST_TIMEOUT_SECONDS,
        ratelimiter_keys: list[str] | None = None,
    ) -> dict[str, Any]:
        """
        Send a GET request to the Hyperliquid API.

        Parameters
        ----------
        endpoint : str
            The API endpoint to request.
        payload : dict[str, Any] | None
            Optional query parameters.
        signed : bool, default False
            Whether the request requires authentication.
        timeout_secs : int, default DEFAULT_REQUEST_TIMEOUT_SECONDS
            Request timeout in seconds.
        ratelimiter_keys : list[str] | None
            Optional rate limiter keys.

        Returns
        -------
        dict[str, Any]
            The parsed JSON response.
        """
        return await self.send_request(
            http_method=HttpMethod.GET,
            endpoint=endpoint,
            payload=payload,
            signed=signed,
            timeout_secs=timeout_secs,
            ratelimiter_keys=ratelimiter_keys,
        )

    async def post(
        self,
        endpoint: str,
        payload: dict[str, Any] | None = None,
        signed: bool = False,
        timeout_secs: int = DEFAULT_REQUEST_TIMEOUT_SECONDS,
        ratelimiter_keys: list[str] | None = None,
    ) -> dict[str, Any]:
        """
        Send a POST request to the Hyperliquid API.

        Parameters
        ----------
        endpoint : str
            The API endpoint to request.
        payload : dict[str, Any] | None
            Optional payload to send.
        signed : bool, default False
            Whether the request requires authentication.
        timeout_secs : int, default DEFAULT_REQUEST_TIMEOUT_SECONDS
            Request timeout in seconds.
        ratelimiter_keys : list[str] | None
            Optional rate limiter keys.

        Returns
        -------
        dict[str, Any]
            The parsed JSON response.
        """
        return await self.send_request(
            http_method=HttpMethod.POST,
            endpoint=endpoint,
            payload=payload,
            signed=signed,
            timeout_secs=timeout_secs,
            ratelimiter_keys=ratelimiter_keys,
        )