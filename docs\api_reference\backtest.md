# Backtest

```{eval-rst}
.. automodule:: nautilus_trader.backtest
```

```{eval-rst}
.. automodule:: nautilus_trader.backtest.auction
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

```{eval-rst}
.. automodule:: nautilus_trader.backtest.data_client
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

```{eval-rst}
.. automodule:: nautilus_trader.backtest.engine
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

```{eval-rst}
.. automodule:: nautilus_trader.backtest.exchange
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

```{eval-rst}
.. automodule:: nautilus_trader.backtest.execution_client
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

```{eval-rst}
.. automodule:: nautilus_trader.backtest.models
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

```{eval-rst}
.. automodule:: nautilus_trader.backtest.modules
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

```{eval-rst}
.. automodule:: nautilus_trader.backtest.node
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

```{eval-rst}
.. automodule:: nautilus_trader.backtest.results
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```
