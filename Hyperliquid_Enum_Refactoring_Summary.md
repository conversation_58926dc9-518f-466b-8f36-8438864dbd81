# Hyperliquid 枚举重构总结

## 问题分析

### 原始问题
1. **`sign_order` 使用场景不明确** - 在 signing.py 中定义但应该在更高层使用
2. **枚举定义位置不当** - `OrderType` 和 `TimeInForce` 在 signing.py 中定义
3. **与 Nautilus 原生枚举不兼容** - 值类型不匹配（数字 vs 字符串）

### 根本原因
- **Nautilus 原生枚举**: 使用数字值 (1, 2, 3...)
- **Hyperliquid API**: 需要特定字符串值 ("Gtc", "limit", "stopMarket"...)
- **直接使用原生枚举不可行**

## 解决方案

### 1. 创建专用枚举 (`common/enums.py`)

```python
class HyperliquidOrderType(str, Enum):
    LIMIT = "limit"
    MARKET = "market"
    STOP_MARKET = "stopMarket"
    # ...

class HyperliquidTimeInForce(str, Enum):
    GTC = "Gtc"
    IOC = "Ioc"
    FOK = "Fok"
    # ...
```

### 2. 创建枚举解析器

```python
class HyperliquidEnumParser:
    def __init__(self):
        self.nautilus_to_hyperliquid_order_type = {
            OrderType.LIMIT: HyperliquidOrderType.LIMIT,
            OrderType.MARKET: HyperliquidOrderType.MARKET,
            # ...
        }
    
    def parse_nautilus_order_type(self, order_type: OrderType) -> HyperliquidOrderType:
        # 转换逻辑
```

### 3. 重构 signing.py

**移除的内容:**
- ❌ `OrderType` 和 `TimeInForce` 枚举定义
- ❌ `sign_order` 方法（移到执行层）
- ❌ 相关测试代码

**保留的内容:**
- ✅ 核心签名功能 (`sign_l1_action`, `sign_user_signed_action`)
- ✅ 所有其他交易类型签名方法
- ✅ `NonceManager` 和工具函数

## 使用模式

### 在执行客户端中的正确用法

```python
class HyperliquidExecutionClient:
    def __init__(self):
        self._signer = HyperliquidSigner(...)
        self._enum_parser = HyperliquidEnumParser()
    
    async def _submit_order(self, order: Order):
        # 1. 验证枚举支持
        if not self._enum_parser.validate_order_type(order.order_type):
            raise ValueError(f"Unsupported order type: {order.order_type}")
        
        # 2. 转换枚举
        hl_order_type = self._enum_parser.parse_nautilus_order_type(order.order_type)
        hl_time_in_force = self._enum_parser.parse_nautilus_time_in_force(order.time_in_force)
        
        # 3. 构建订单数据
        order_wire = {
            "a": asset_id,
            "b": is_buy,
            "p": float_to_wire(price),
            "s": float_to_wire(size),
            "t": {"limit": {"tif": hl_time_in_force.value}}
        }
        
        # 4. 签名
        signed_data = self._signer.sign_l1_action(action)
```

## 架构优势

### 1. **关注点分离**
- **signing.py**: 专注于低级签名操作
- **common/enums.py**: 处理枚举转换
- **execution.py**: 处理订单业务逻辑

### 2. **一致性**
- 遵循其他适配器的模式 (Bybit, OKX, Binance)
- 标准的枚举解析器模式
- 清晰的双向映射

### 3. **可维护性**
- 枚举定义集中管理
- 易于添加新的订单类型
- 清晰的验证逻辑

### 4. **类型安全**
- 编译时类型检查
- 明确的转换边界
- 运行时验证

## 文件结构

```
adapters/hyperliquid/
├── common/
│   └── enums.py              # ✅ 新增：枚举定义和解析器
├── util/
│   └── signing.py            # ✅ 重构：移除枚举，专注签名
├── execution.py              # 🔄 待实现：使用枚举解析器
└── execution_order_example.py # ✅ 新增：使用示例
```

## 迁移指南

### 对于现有代码

**之前:**
```python
from adapters.hyperliquid.util.signing import OrderType, TimeInForce

signer.sign_order(
    order_type=OrderType.LIMIT,
    time_in_force=TimeInForce.GTC
)
```

**现在:**
```python
from nautilus_trader.model.enums import OrderType, TimeInForce
from adapters.hyperliquid.common.enums import HyperliquidEnumParser

enum_parser = HyperliquidEnumParser()
hl_order_type = enum_parser.parse_nautilus_order_type(OrderType.LIMIT)
# 在执行客户端中处理订单签名
```

## 支持的映射

### OrderType 映射
| Nautilus | Hyperliquid | 说明 |
|----------|-------------|------|
| `LIMIT` | `"limit"` | 限价单 |
| `MARKET` | `"market"` | 市价单 |
| `STOP_MARKET` | `"stopMarket"` | 止损市价单 |
| `STOP_LIMIT` | `"stopLimit"` | 止损限价单 |
| `MARKET_IF_TOUCHED` | `"takeProfitMarket"` | 止盈市价单 |
| `LIMIT_IF_TOUCHED` | `"takeProfitLimit"` | 止盈限价单 |

### TimeInForce 映射
| Nautilus | Hyperliquid | 说明 |
|----------|-------------|------|
| `GTC` | `"Gtc"` | 一直有效 |
| `IOC` | `"Ioc"` | 立即成交或取消 |
| `FOK` | `"Fok"` | 全部成交或取消 |

## 总结

✅ **完成的改进:**
1. 创建了专用的 Hyperliquid 枚举定义
2. 实现了标准的枚举解析器模式
3. 重构了 signing.py，移除了不当的枚举定义
4. 提供了完整的使用示例

✅ **架构优势:**
- 更好的关注点分离
- 与其他适配器一致的模式
- 类型安全和运行时验证
- 易于维护和扩展

✅ **下一步:**
- 在 `execution.py` 中实现订单签名逻辑
- 在 `http/exchange.py` 中使用枚举解析器
- 添加完整的单元测试
