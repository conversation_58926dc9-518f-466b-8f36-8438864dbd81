# Polymarket

```{eval-rst}
.. automodule:: nautilus_trader.adapters.polymarket
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

## Config

```{eval-rst}
.. automodule:: nautilus_trader.adapters.polymarket.config
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

## Factories

```{eval-rst}
.. automodule:: nautilus_trader.adapters.polymarket.factories
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

## Enums

```{eval-rst}
.. automodule:: nautilus_trader.adapters.polymarket.common.enums
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

## Providers

```{eval-rst}
.. automodule:: nautilus_trader.adapters.polymarket.providers
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

## Data

```{eval-rst}
.. automodule:: nautilus_trader.adapters.polymarket.data
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

## Execution

```{eval-rst}
.. automodule:: nautilus_trader.adapters.polymarket.execution
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```
