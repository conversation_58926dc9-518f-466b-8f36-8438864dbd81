# Exchange Adapter Code Structure Analysis

## Overview

This document analyzes the code structure of Bybit and dYdX adapters in the Nautilus Trader framework to understand the architectural patterns and organization principles for building exchange adapters.

## High-Level Architecture

Both adapters follow a consistent architectural pattern with the following core components:

### 1. **Main Client Classes**
- **Data Client**: Handles market data feeds (quotes, trades, orderbooks, bars)
- **Execution Client**: Manages account operations and trade execution
- **HTTP Client**: Low-level HTTP API connectivity
- **WebSocket Client**: Real-time data streaming
- **Instrument Provider**: Instrument metadata and symbol management

### 2. **Factory Pattern**
- **LiveDataClientFactory**: Creates and configures data clients
- **LiveExecClientFactory**: Creates and configures execution clients

## Directory Structure Analysis

### Common Structure Pattern
```
adapters/{exchange}/
├── __init__.py
├── common/                    # Shared utilities and constants
│   ├── constants.py          # Exchange-specific constants
│   ├── credentials.py        # API credential management
│   ├── enums.py             # Exchange-specific enumerations
│   ├── parsing.py           # Data parsing utilities
│   ├── symbol.py            # Symbol handling logic
│   ├── types.py             # Custom type definitions
│   └── urls.py              # API endpoint URLs
├── config.py                 # Configuration classes
├── data.py                   # Data client implementation
├── execution.py              # Execution client implementation
├── factories.py              # Client factory implementations
├── providers.py              # Instrument provider
├── endpoints/                # API endpoint definitions
│   ├── endpoint.py          # Base endpoint class
│   ├── account/             # Account-related endpoints
│   ├── market/              # Market data endpoints
│   ├── trade/               # Trading endpoints
│   └── user/                # User management endpoints
├── http/                     # HTTP client implementations
│   ├── client.py            # Main HTTP client
│   ├── account.py           # Account HTTP API
│   ├── market.py            # Market HTTP API
│   └── errors.py            # HTTP error handling
├── schemas/                  # Data structure definitions
│   ├── common.py            # Common schemas
│   ├── account/             # Account-related schemas
│   ├── market/              # Market data schemas
│   ├── order.py             # Order schemas
│   ├── position.py          # Position schemas
│   └── ws.py                # WebSocket message schemas
└── websocket/                # WebSocket client
    └── client.py            # WebSocket implementation
```

### Exchange-Specific Variations

**Bybit Additional Components:**
- `loaders.py` - Data loading utilities
- More granular endpoint organization (asset, user endpoints)
- Product type support (spot, linear, inverse, options)

**dYdX Additional Components:**
- `grpc/` directory - gRPC client for blockchain interactions
- Simplified endpoint structure (fewer categories)
- Wallet address management for decentralized operations

## Core Component Analysis

### 1. Configuration Classes (`config.py`)

**Purpose**: Define configuration parameters for data and execution clients

**Key Patterns**:
```python
class {Exchange}DataClientConfig(LiveDataClientConfig, frozen=True):
    # Exchange-specific data client configuration

class {Exchange}ExecClientConfig(LiveExecClientConfig, frozen=True):
    # Exchange-specific execution client configuration
```

**Common Configuration Elements**:
- API credentials (key, secret, passphrase)
- Environment settings (testnet, demo)
- Rate limiting parameters
- WebSocket configuration
- Update intervals

### 2. Data Client (`data.py`)

**Purpose**: Handle market data subscriptions and historical data requests

**Key Responsibilities**:
- WebSocket connection management for real-time data
- Historical data requests via HTTP
- Data parsing and normalization
- Subscription management (quotes, trades, orderbooks, bars)

**Core Structure**:
```python
class {Exchange}DataClient(LiveMarketDataClient):
    def __init__(self, loop, client, msgbus, cache, clock, instrument_provider, ...):
        # Initialize WebSocket clients
        # Set up data decoders
        # Configure subscriptions

    async def _connect(self):
        # Establish connections

    async def _subscribe_**(self, ...):
        # Handle various subscription types

    def _handle_ws_message(self, message):
        # Process incoming WebSocket messages
```

### 3. Execution Client (`execution.py`)

**Purpose**: Handle order management and account operations

**Key Responsibilities**:
- Order submission, modification, cancellation
- Account balance and position monitoring
- Trade execution reporting
- Risk management integration

**Core Structure**:
```python
class {Exchange}ExecutionClient(LiveExecutionClient):
    def __init__(self, loop, client, msgbus, cache, clock, instrument_provider, ...):
        # Initialize HTTP and WebSocket clients
        # Set up account management
        # Configure order handling

    async def _submit_order(self, order):
        # Handle order submission

    async def _modify_order(self, command):
        # Handle order modifications

    async def _cancel_order(self, command):
        # Handle order cancellations
```

### 4. Factory Classes (`factories.py`)

**Purpose**: Create and configure client instances with proper dependencies

**Key Patterns**:
```python
class {Exchange}LiveDataClientFactory(LiveDataClientFactory):
    @staticmethod
    def create(loop, name, config, msgbus, cache, clock):
        # Create HTTP client
        # Create instrument provider
        # Create and return data client

class {Exchange}LiveExecClientFactory(LiveExecClientFactory):
    @staticmethod
    def create(loop, name, config, msgbus, cache, clock):
        # Create HTTP client
        # Create instrument provider
        # Create and return execution client
```

### 5. Common Utilities (`common/`)

**enums.py**: Exchange-specific enumerations and parsing logic
- Order types, sides, statuses
- Time in force options
- Product types
- Enum parser classes for bidirectional conversion

**symbol.py**: Symbol handling and normalization
- Exchange symbol format parsing
- Nautilus symbol conversion
- Product type detection

**parsing.py**: Data transformation utilities
- Price/quantity formatting
- Timestamp conversions
- Data structure transformations

**urls.py**: API endpoint management
- Base URL configuration
- Environment-specific URLs (testnet/mainnet)
- WebSocket endpoint URLs

### 6. HTTP Layer (`http/`)

**client.py**: Core HTTP client with authentication
- Request signing and authentication
- Rate limiting integration
- Error handling and retries
- Response parsing

**Specialized HTTP APIs**:
- `account.py`: Account-related operations
- `market.py`: Market data requests
- Additional modules for specific functionality

### 7. Schemas (`schemas/`)

**Purpose**: Define data structures for API responses and WebSocket messages

**Organization**:
- Grouped by functionality (account, market, trade)
- msgspec.Struct-based definitions for performance
- Validation and type safety

### 8. Endpoints (`endpoints/`)

**Purpose**: Define API endpoint specifications and request/response handling

**Structure**:
```python
class {Exchange}{Operation}Endpoint:
    def __init__(self, client, ...):
        # Configure endpoint

    async def {operation}(self, params):
        # Execute API call
        # Return parsed response
```

## Key Design Patterns

### 1. **Separation of Concerns**
- Clear separation between data and execution responsibilities
- HTTP and WebSocket clients handle different aspects
- Modular endpoint organization

### 2. **Factory Pattern**
- Centralized client creation and configuration
- Dependency injection for testability
- Environment-specific configuration

### 3. **Adapter Pattern**
- Exchange-specific implementations behind common interfaces
- Consistent API across different exchanges
- Nautilus framework integration

### 4. **Observer Pattern**
- WebSocket message handling through callbacks
- Event-driven architecture for real-time data
- Message bus integration for system-wide communication

### 5. **Strategy Pattern**
- Configurable behavior through configuration classes
- Different handling for various product types
- Environment-specific adaptations

## Implementation Guidelines

### What Goes Where

**`common/`**: Shared utilities, constants, and exchange-specific logic
- Enumerations and parsing logic
- Symbol handling
- URL management
- Credential handling

**`config.py`**: Configuration classes with validation
- API credentials
- Connection parameters
- Feature flags
- Environment settings

**`data.py`**: Market data client implementation
- WebSocket subscriptions
- Historical data requests
- Data normalization
- Real-time feed management

**`execution.py`**: Trading client implementation
- Order management
- Account monitoring
- Position tracking
- Trade reporting

**`factories.py`**: Client creation and dependency injection
- HTTP client setup
- Instrument provider creation
- Client configuration and initialization

**`http/`**: HTTP API layer
- Authentication and signing
- Request/response handling
- Rate limiting
- Error management

**`schemas/`**: Data structure definitions
- API response models
- WebSocket message formats
- Validation schemas

**`endpoints/`**: API endpoint specifications
- Request parameter handling
- Response parsing
- Endpoint-specific logic

**`websocket/`**: Real-time communication
- Connection management
- Message routing
- Reconnection logic

This structure provides a scalable, maintainable foundation for exchange adapter development while maintaining consistency across different exchange implementations.

## Detailed Implementation Examples

### 1. Enum Parser Implementation Pattern

Both adapters implement comprehensive enum parsers for bidirectional conversion:

```python
class {Exchange}EnumParser:
    def __init__(self):
        # Bidirectional mappings
        self.exchange_to_nautilus_order_side = {
            ExchangeOrderSide.BUY: OrderSide.BUY,
            ExchangeOrderSide.SELL: OrderSide.SELL,
        }
        self.nautilus_to_exchange_order_side = {
            v: k for k, v in self.exchange_to_nautilus_order_side.items()
        }

    def parse_nautilus_order_side(self, side: OrderSide) -> ExchangeOrderSide:
        return self.nautilus_to_exchange_order_side[side]

    def parse_exchange_order_side(self, side: ExchangeOrderSide) -> OrderSide:
        return self.exchange_to_nautilus_order_side[side]
```

### 2. HTTP Client Authentication Pattern

```python
class {Exchange}HttpClient:
    def __init__(self, clock, api_key, api_secret, base_url, ...):
        self._clock = clock
        self._api_key = api_key
        self._api_secret = api_secret
        self._base_url = base_url

    async def sign_request(self, method, url_path, payload=None):
        timestamp = str(self._clock.timestamp_ms())
        signature = self._generate_signature(timestamp, method, url_path, payload)

        headers = {
            "X-API-KEY": self._api_key,
            "X-TIMESTAMP": timestamp,
            "X-SIGNATURE": signature,
        }

        return await self.send_request(method, url_path, payload, headers)
```

### 3. WebSocket Message Handling Pattern

```python
class {Exchange}DataClient(LiveMarketDataClient):
    def _handle_ws_message(self, raw: bytes) -> None:
        try:
            # Decode message
            message = self._decoder_ws_general.decode(raw)

            # Route by message type
            if message.topic.startswith("orderbook"):
                self._handle_orderbook_update(message)
            elif message.topic.startswith("trade"):
                self._handle_trade_update(message)
            elif message.topic.startswith("kline"):
                self._handle_kline_update(message)

        except Exception as e:
            self._log.error(f"Error handling WebSocket message: {e}")
```

### 4. Configuration Validation Pattern

```python
class {Exchange}DataClientConfig(LiveDataClientConfig, frozen=True):
    api_key: str | None = None
    api_secret: str | None = None
    testnet: bool = False
    product_types: list[ProductType] | None = None

    def __post_init__(self):
        # Validation logic
        if self.api_key and not self.api_secret:
            raise ValueError("api_secret required when api_key provided")
```

## Best Practices and Patterns

### 1. **Error Handling Strategy**

- **HTTP Errors**: Custom exception classes with error codes
- **WebSocket Errors**: Graceful reconnection with exponential backoff
- **Data Validation**: Schema validation with meaningful error messages
- **Rate Limiting**: Respect exchange limits with proper queuing

### 2. **Performance Optimization**

- **msgspec**: Use for fast JSON encoding/decoding
- **Connection Pooling**: Reuse HTTP connections
- **Batch Operations**: Group API calls when possible
- **Caching**: Cache instrument metadata and static data

### 3. **Testing Strategy**

- **Unit Tests**: Test individual components in isolation
- **Integration Tests**: Test with mock exchange responses
- **End-to-End Tests**: Test with sandbox/testnet environments
- **Performance Tests**: Validate under load conditions

### 4. **Documentation Requirements**

- **API Documentation**: Document all public methods and classes
- **Configuration Guide**: Explain all configuration options
- **Integration Examples**: Provide working code examples
- **Troubleshooting Guide**: Common issues and solutions

### 5. **Security Considerations**

- **Credential Management**: Never log or expose API secrets
- **Request Signing**: Implement proper authentication
- **Rate Limiting**: Respect exchange limits to avoid bans
- **Input Validation**: Validate all user inputs

## Exchange-Specific Considerations

### Centralized Exchanges (like Bybit)
- **Product Types**: Support multiple trading pairs and instruments
- **Account Types**: Handle different account modes (unified, classic)
- **Order Types**: Support complex order types and conditions
- **Rate Limits**: Implement sophisticated rate limiting

### Decentralized Exchanges (like dYdX)
- **Wallet Integration**: Handle wallet connections and signing
- **Blockchain Interaction**: Integrate with blockchain APIs (gRPC)
- **Gas Management**: Handle transaction fees and gas estimation
- **Network Handling**: Support multiple networks (mainnet, testnet)

## Maintenance and Evolution

### 1. **API Version Management**
- Support multiple API versions simultaneously
- Graceful migration between versions
- Backward compatibility considerations

### 2. **Feature Flags**
- Enable/disable features through configuration
- A/B testing for new implementations
- Gradual rollout of changes

### 3. **Monitoring and Observability**
- Comprehensive logging at appropriate levels
- Metrics collection for performance monitoring
- Health checks and status reporting
- Error tracking and alerting

### 4. **Scalability Considerations**
- Horizontal scaling support
- Resource usage optimization
- Connection management
- Memory usage patterns

This comprehensive structure ensures robust, maintainable, and scalable exchange adapter implementations that can evolve with changing requirements and exchange API updates.
