# HyperLiquid API 符号表示法

HyperLiquid API（当前版本v0）使用了一些非标准的符号表示法。相关的标准化将在未来的v1 API版本中进行统一更改。

## 符号表

| 缩写 | 全称 | 说明 |
|------|------|------|
| Px | Price | 价格 |
| Sz | Size | 数量，以基础货币为单位 |
| Szi | Signed size | 有符号数量，正数表示做多，负数表示做空 |
| Ntl | Notional | 美元金额，等于 Px * Sz |
| Side | Side of trade or book | B = Bid = Buy（买入），A = Ask = Short（卖出）。对于交易记录，Side表示主动成交方向 |
| Asset | Asset | 表示交易资产的整数。详见资产ID文档 |
| Tif | Time in force | GTC = good until canceled（一直有效直到取消），ALO = add liquidity only（仅增加流动性，即只做挂单），IOC = immediate or cancel（立即成交或取消） |

## 说明

1. **价格(Px)**：表示交易资产的价格。

2. **数量(Sz)**：表示交易的数量，以基础货币为单位。

3. **有符号数量(Szi)**：带有方向的数量，正数表示做多（买入），负数表示做空（卖出）。

4. **名义价值(Ntl)**：交易的美元价值，计算方式为价格乘以数量(Px * Sz)。

5. **方向(Side)**：
   - B = Bid = Buy：买入方向
   - A = Ask = Short：卖出方向
   - 对于交易记录，Side表示主动成交的一方

6. **资产(Asset)**：用整数表示的交易资产标识符。详细信息请参考资产ID文档。

7. **有效期(Tif)**：订单的有效时间设置：
   - GTC (Good Till Canceled)：订单一直有效，直到被取消
   - ALO (Add Liquidity Only)：仅限挂单，也称为"Post Only"
   - IOC (Immediate Or Cancel)：立即成交或取消，不挂单

## 注意事项

- 当前API版本(v0)使用的是非标准符号表示法
- 未来的v1 API版本将对这些符号进行标准化处理
- 在开发过程中，请注意这些符号的特定含义，以确保正确解析API响应和构造API请求
