# -------------------------------------------------------------------------------------------------
#  Copyright (C) 2015-2023 Nautech Systems Pty Ltd. All rights reserved.
#  https://nautechsystems.io
#
#  Licensed under the GNU Lesser General Public License Version 3.0 (the "License");
#  You may not use this file except in compliance with the License.
#  You may obtain a copy of the License at https://www.gnu.org/licenses/lgpl-3.0.en.html
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
# -------------------------------------------------------------------------------------------------
"""
Provides error handling for Hyperliquid HTTP API.

This module defines custom exceptions for handling Hyperliquid API errors,
following nautilus_trader patterns for error handling.
"""

from typing import Any


class HyperliquidError(Exception):
    """
    Base exception for Hyperliquid API errors.

    This exception is raised when the Hyperliquid API returns an error response
    or when there are issues with request processing.
    """

    def __init__(
        self,
        status: int,
        message: Any = None,
        headers: dict[str, str] | None = None,
    ) -> None:
        """
        Initialize a Hyperliquid error.

        Parameters
        ----------
        status : int
            The HTTP status code.
        message : Any
            The error message or response data.
        headers : dict[str, str] | None
            Optional HTTP response headers.
        """
        self.status = status
        self.message = message
        self.headers = headers or {}

        # Format error message
        if isinstance(message, dict):
            # Try to extract meaningful error message from response
            error_msg = message.get("error", message.get("msg", str(message)))
        else:
            error_msg = str(message) if message else f"HTTP {status} error"

        super().__init__(f"Hyperliquid API error {status}: {error_msg}")

    def __repr__(self) -> str:
        """Return string representation of the error."""
        return (
            f"HyperliquidError(status={self.status}, "
            f"message={self.message!r}, headers={self.headers!r})"
        )


class HyperliquidClientError(HyperliquidError):
    """
    Exception for client errors (4xx status codes).

    This exception is raised when the client sends an invalid request
    to the Hyperliquid API.
    """

    pass


class HyperliquidServerError(HyperliquidError):
    """
    Exception for server errors (5xx status codes).

    This exception is raised when the Hyperliquid API experiences
    internal server errors.
    """

    pass


class HyperliquidAuthenticationError(HyperliquidClientError):
    """
    Exception for authentication errors.

    This exception is raised when there are issues with API authentication,
    such as invalid signatures or missing credentials.
    """

    pass


class HyperliquidRateLimitError(HyperliquidClientError):
    """
    Exception for rate limit errors.

    This exception is raised when the API rate limit is exceeded.
    """

    pass


def raise_for_status(status: int, message: Any = None, headers: dict[str, str] | None = None) -> None:
    """
    Raise appropriate exception based on HTTP status code.

    Parameters
    ----------
    status : int
        The HTTP status code.
    message : Any
        The error message or response data.
    headers : dict[str, str] | None
        Optional HTTP response headers.

    Raises
    ------
    HyperliquidAuthenticationError
        For 401 Unauthorized errors.
    HyperliquidRateLimitError
        For 429 Too Many Requests errors.
    HyperliquidClientError
        For other 4xx client errors.
    HyperliquidServerError
        For 5xx server errors.
    """
    if status == 401:
        raise HyperliquidAuthenticationError(status, message, headers)
    elif status == 429:
        raise HyperliquidRateLimitError(status, message, headers)
    elif 400 <= status < 500:
        raise HyperliquidClientError(status, message, headers)
    elif 500 <= status < 600:
        raise HyperliquidServerError(status, message, headers)
