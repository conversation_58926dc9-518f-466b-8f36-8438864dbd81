# HyperLiquid API 签名机制

本文档详细介绍了HyperLiquid API的签名机制，包括签名方法、常见错误和最佳实践。

## 概述

HyperLiquid API要求对所有交易操作进行签名，以确保操作的真实性和完整性。签名过程涉及使用私钥对操作数据进行签名，然后将签名与请求一起发送。

## 签名方法

HyperLiquid有两种主要的签名方案：

1. **L1操作签名**：用于与区块链直接交互的操作，如订单和取消。
2. **用户签名操作**：用于需要用户确认的操作，如转账和提款。

### Python SDK中的签名方法

Python SDK提供了两个主要的签名方法：

- `sign_l1_action`：用于签署L1操作，如订单和取消。
- `sign_user_signed_action`：用于签署用户签名操作，如转账和提款。

## 签名格式

签名对象通常包含以下字段：

```json
{
  "signature": {
    "r": "0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef",
    "s": "0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef",
    "v": 27
  }
}
```

其中：
- `r`和`s`是签名的组成部分
- `v`是恢复ID，用于确定公钥

## 常见签名错误

不正确的签名会导致基于签名和有效载荷恢复不同的签名者，并导致以下错误之一：

```
"L1 error: User or API Wallet 0x0123... does not exist."
"Must deposit before performing actions. User: 0x123..."
```

其中返回的地址与您签名的钱包的公共地址不匹配。对于不同的输入，返回的地址也会变化。不正确的签名不会指示为什么不正确，这使得调试更具挑战性。

### 常见错误原因

1. **不了解有两种签名方案**：确保使用正确的签名方法（`sign_l1_action`与`sign_user_signed_action`）。

2. **msgpack字段顺序问题**：msgpack序列化中字段的顺序很重要，确保字段按正确的顺序排列。

3. **数字尾随零的问题**：确保数字格式正确，特别是处理尾随零时。

4. **地址字段中大写字符的问题**：建议在签名和发送之前将任何地址小写。有时字段被解析为字节，导致它在网络上自动小写。

5. **本地验证与网络验证不一致**：认为签名必须正确，因为本地调用recover signer会得到正确的地址。recover signer的有效载荷是基于操作构建的，不一定匹配网络上的验证方式。

## 调试签名问题

如果遇到签名问题，请尝试以下步骤：

1. **仔细阅读Python SDK**：确保您的实现与SDK完全匹配。

2. **添加日志**：如果SDK不起作用，添加日志以找出输出在哪里发生分歧。

3. **检查地址格式**：确保地址使用小写字母，并且格式正确。

4. **验证nonce**：确保nonce是唯一的，并且在适当的时间范围内。

5. **比较有效载荷**：比较本地构建的有效载荷与SDK构建的有效载荷，确保它们完全匹配。

## 签名示例

以下是使用Python SDK签署订单的示例：

```python
from hyperliquid.utils import sign_l1_action

# 私钥（示例，实际使用中应安全存储）
private_key = "0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef"

# 订单操作
action = {
    "type": "order",
    "orders": [{
        "a": 0,  # 资产ID
        "b": True,  # 是否买入
        "p": "30000",  # 价格
        "s": "0.1",  # 数量
        "r": False,  # 是否只减仓
        "t": {
            "limit": {
                "tif": "Gtc"  # 有效期类型
            }
        }
    }],
    "grouping": "na"
}

# 当前时间戳作为nonce
nonce = int(time.time() * 1000)

# 签署操作
signature = sign_l1_action(private_key, action, nonce)

# 构建请求
request = {
    "action": action,
    "nonce": nonce,
    "signature": signature
}
```

## 最佳实践

1. **使用SDK**：强烈建议使用现有的SDK而不是手动生成签名，以避免签名错误。

2. **保护私钥**：确保私钥安全存储，不要在代码中硬编码或暴露在日志中。

3. **使用唯一nonce**：每个请求使用唯一的nonce，建议使用当前时间戳。

4. **小写地址**：在签名和发送之前将所有地址转换为小写。

5. **验证签名**：在发送请求之前，验证签名是否正确。

6. **处理错误**：实现适当的错误处理机制，以应对签名错误。

7. **记录日志**：记录所有请求和响应，以便在出现问题时进行调试。

## 总结

正确实现HyperLiquid API的签名机制对于成功与交易所交互至关重要。通过遵循本文档中的最佳实践，开发者可以减少签名错误的发生，并在错误发生时更有效地进行调试和恢复。

强烈建议使用官方SDK或经过验证的第三方库来处理签名，而不是尝试手动实现签名逻辑。
