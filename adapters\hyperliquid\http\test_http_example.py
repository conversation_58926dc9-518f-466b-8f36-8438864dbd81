#!/usr/bin/env python3
# -------------------------------------------------------------------------------------------------
#  Copyright (C) 2015-2023 Nautech Systems Pty Ltd. All rights reserved.
#  https://nautechsystems.io
#
#  Licensed under the GNU Lesser General Public License Version 3.0 (the "License");
#  You may not use this file except in compliance with the License.
#  You may obtain a copy of the License at https://www.gnu.org/licenses/lgpl-3.0.en.html
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
# -------------------------------------------------------------------------------------------------
"""
Example usage of Hyperliquid HTTP API client.

This script demonstrates how to use the Hyperliquid HTTP API client
for market data and trading operations.
"""

import asyncio

from nautilus_trader.adapters.hyperliquid.http import (
    HyperliquidAccountHttpApi,
    HyperliquidHttpClient,
    HyperliquidMarketHttpApi,
)
from nautilus_trader.common.component import LiveClock


async def test_market_data():
    """Test market data API functionality."""
    print("Testing Hyperliquid Market Data API...")
    
    # Create HTTP client (no authentication needed for market data)
    clock = LiveClock()
    client = HyperliquidHttpClient(
        clock=clock,
        testnet=True,  # Use testnet for testing
    )
    
    # Create market API
    market_api = HyperliquidMarketHttpApi(client)
    
    try:
        # Test getting all mid prices
        print("\n1. Getting all mid prices...")
        mids = await market_api.get_all_mids()
        print(f"Found {len(mids)} trading pairs")
        for symbol, price in list(mids.items())[:5]:  # Show first 5
            print(f"  {symbol}: {price}")
        
        # Test getting exchange metadata
        print("\n2. Getting exchange metadata...")
        meta = await market_api.get_meta()
        universe = meta.get("universe", [])
        print(f"Found {len(universe)} perpetual assets")
        for asset in universe[:3]:  # Show first 3
            print(f"  {asset['name']}: {asset['szDecimals']} decimals")
        
        # Test getting spot metadata
        print("\n3. Getting spot metadata...")
        spot_meta = await market_api.get_spot_meta()
        spot_universe = spot_meta.get("universe", [])
        print(f"Found {len(spot_universe)} spot pairs")
        for pair in spot_universe[:3]:  # Show first 3
            print(f"  {pair['name']}: index {pair['index']}")
        
        # Test getting L2 book for BTC (if available)
        if "BTC" in mids:
            print("\n4. Getting L2 order book for BTC...")
            book = await market_api.get_l2_book("BTC")
            if book and len(book) >= 2:
                bids, asks = book[0], book[1]
                print(f"  Best bid: {bids[0]['px']} (size: {bids[0]['sz']})")
                print(f"  Best ask: {asks[0]['px']} (size: {asks[0]['sz']})")
        
        print("\n✅ Market data API test completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Market data API test failed: {e}")
        raise


async def test_authenticated_operations():
    """Test authenticated operations (requires private key)."""
    print("\nTesting Hyperliquid Authenticated Operations...")
    
    # Note: This requires a valid private key for testnet
    # For demonstration purposes, we'll show the structure
    private_key = "0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef"  # Placeholder
    
    clock = LiveClock()
    client = HyperliquidHttpClient(
        clock=clock,
        private_key=private_key,
        testnet=True,
    )
    
    # Create account API
    account_api = HyperliquidAccountHttpApi(client)
    
    print("⚠️  Authenticated operations require a valid private key")
    print("   This example shows the API structure but won't execute")
    print("   Replace the placeholder private key with a real testnet key to test")
    
    # Example operations (commented out to avoid errors with placeholder key)
    """
    try:
        # Get account state
        user_address = "0x..." # Your wallet address
        account_state = await account_api.get_account_state(user_address)
        print(f"Account value: {account_state['marginSummary']['accountValue']}")
        
        # Get open orders
        open_orders = await account_api.get_open_orders(user_address)
        print(f"Open orders: {len(open_orders)}")
        
        # Place a limit order (example)
        order_response = await account_api.place_limit_order(
            asset=0,  # BTC perpetual
            is_buy=True,
            price="30000.0",
            size="0.001",
            time_in_force="Gtc",
        )
        print(f"Order placed: {order_response}")
        
    except Exception as e:
        print(f"Authenticated operation failed: {e}")
    """


async def main():
    """Run the HTTP API tests."""
    print("🚀 Hyperliquid HTTP API Test Suite")
    print("=" * 50)
    
    # Test market data (no authentication required)
    await test_market_data()
    
    # Test authenticated operations (requires private key)
    await test_authenticated_operations()
    
    print("\n" + "=" * 50)
    print("✅ HTTP API test suite completed!")


if __name__ == "__main__":
    asyncio.run(main())
