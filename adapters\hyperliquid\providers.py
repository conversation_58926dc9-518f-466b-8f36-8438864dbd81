import asyncio
from decimal import Decimal
from typing import Dict, List, Optional, Set

from nautilus_trader.adapters.hyperliquid.common.constants import HYPERLIQUID_VENUE
from nautilus_trader.adapters.hyperliquid.http.client import HyperliquidHttpClient
from nautilus_trader.common.component import LiveClock
from nautilus_trader.common.component import Logger
from nautilus_trader.common.providers import InstrumentProvider
from nautilus_trader.model.currencies import USD
from nautilus_trader.model.enums import AccountType
from nautilus_trader.model.identifiers import InstrumentId
from nautilus_trader.model.identifiers import Symbol
from nautilus_trader.model.instruments import CryptoPerpetual
from nautilus_trader.model.objects import Price
from nautilus_trader.model.objects import Quantity


class HyperliquidInstrumentProvider(InstrumentProvider):
    """
    HyperLiquid交易品种提供器
    
    负责加载和管理HyperLiquid交易所的交易品种信息。
    """
    
    def __init__(
        self,
        client: HyperliquidHttpClient,
        clock: LiveClock,
        testnet: bool = False,
    ) -> None:
        super().__init__()
        
        self._clock = clock
        self._http_client = client
        self._testnet = testnet
        self._log = Logger(type(self).__name__)
        
        # 缓存
        self._perp_instruments: Dict[str, CryptoPerpetual] = {}
        self._asset_id_map: Dict[str, int] = {}  # 符号 -> 资产ID
        self._coin_map: Dict[int, str] = {}  # 资产ID -> 符号
        
    async def load_all_async(self, filters: Optional[Dict] = None) -> None:
        """
        加载所有交易品种
        
        Parameters
        ----------
        filters : Optional[Dict]
            可选的过滤条件
        """
        self._log.info("正在加载所有交易品种...")
        
        try:
            # 获取所有交易品种信息
            response = await self._http_client.get("/info")
            meta_data = response.get("data", {}).get("universe", [])
            
            if not meta_data:
                self._log.warning("未找到交易品种信息")
                return
                
            # 解析交易品种信息
            for i, coin_data in enumerate(meta_data):
                coin = coin_data.get("name")
                if not coin:
                    continue
                    
                # 记录资产ID映射
                self._asset_id_map[coin] = i
                self._coin_map[i] = coin
                
                # 创建交易品种
                instrument = self._create_perpetual(coin_data, i)
                if instrument:
                    self._perp_instruments[coin] = instrument
                    self._instruments[instrument.id] = instrument
                    
            self._log.info(f"已加载 {len(self._perp_instruments)} 个永续合约交易品种")
        except Exception as e:
           