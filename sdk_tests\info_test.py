import pytest

from hyperliquid.info import Info
from hyperliquid.utils.types import L2BookData, Meta, SpotMeta

TEST_META: Meta = {"universe": []}
TEST_SPOT_META: SpotMeta = {"universe": [], "tokens": []}


@pytest.mark.vcr()
def test_get_user_state():
    info = Info(skip_ws=True, meta=TEST_META, spot_meta=TEST_SPOT_META)
    response = info.user_state("******************************************")
    assert len(response["assetPositions"]) == 12
    assert response["marginSummary"]["accountValue"] == "1182.312496"


@pytest.mark.vcr()
def test_get_open_orders():
    info = Info(skip_ws=True, meta=TEST_META, spot_meta=TEST_SPOT_META)
    response = info.open_orders("******************************************")
    assert len(response) == 196


@pytest.mark.vcr()
def test_get_frontend_open_orders():
    info = Info(skip_ws=True, meta=TEST_META, spot_meta=TEST_SPOT_META)
    response = info.frontend_open_orders("******************************************")
    assert len(response) == 3


@pytest.mark.vcr()
def test_get_all_mids():
    info = Info(skip_ws=True, meta=TEST_META, spot_meta=TEST_SPOT_META)
    response = info.all_mids()
    assert "BTC" in response
    assert "ETH" in response
    assert "ATOM" in response
    assert "MATIC" in response


@pytest.mark.vcr()
def test_get_user_fills():
    info = Info(skip_ws=True, meta=TEST_META, spot_meta=TEST_SPOT_META)
    response = info.user_fills("******************************************")
    assert isinstance(response, list)
    assert response[0]["crossed"] is True


@pytest.mark.vcr()
def test_get_user_fills_by_time():
    info = Info(skip_ws=True, meta=TEST_META, spot_meta=TEST_SPOT_META)
    response = info.user_fills_by_time(
        "******************************************", start_time=1683245555699, end_time=1683245884863
    )
    assert isinstance(response, list)
    assert len(response) == 500


@pytest.mark.vcr()
def test_get_info():
    info = Info(skip_ws=True, meta=TEST_META, spot_meta=TEST_SPOT_META)
    response = info.meta()
    assert len(response["universe"]) == 28
    assert response["universe"][0]["name"] == "BTC"
    assert response["universe"][0]["szDecimals"] == 5


@pytest.mark.vcr()
@pytest.mark.parametrize("endTime", [None, 1684811870000])
def test_get_funding_history(endTime):
    info = Info(skip_ws=True, spot_meta=TEST_SPOT_META)
    if endTime is None:
        response = info.funding_history(name="BTC", startTime=1681923833000)
    else:
        response = info.funding_history(name="BTC", startTime=1681923833000, endTime=endTime)
    assert len(response) != 0
    assert response[0]["coin"] == "BTC"
    for key in ["coin", "fundingRate", "premium", "time"]:
        assert key in response[0].keys()


@pytest.mark.vcr()
def test_get_l2_snapshot():
    info = Info(skip_ws=True, spot_meta=TEST_SPOT_META)
    response: L2BookData = info.l2_snapshot(name="DYDX")
    assert len(response) != 0
    assert len(response["levels"]) == 2
    assert response["coin"] == "DYDX"
    for key in ["coin", "time"]:
        assert key in response.keys()
    for key in ["n", "sz", "px"]:
        assert key in response["levels"][0][0].keys()
        assert key in response["levels"][1][0].keys()


@pytest.mark.vcr()
def test_get_candles_snapshot():
    info = Info(skip_ws=True, spot_meta=TEST_SPOT_META)
    response = info.candles_snapshot(name="kPEPE", interval="1h", startTime=1684702007000, endTime=1684784807000)
    assert len(response) == 24
    for key in ["T", "c", "h", "i", "l", "n", "o", "s", "t", "v"]:
        assert key in response[0].keys()


@pytest.mark.vcr()
def test_user_funding_history_with_end_time():
    info = Info(skip_ws=True, meta=TEST_META, spot_meta=TEST_SPOT_META)
    response = info.user_funding_history(
        user="******************************************", startTime=1681923833000, endTime=1682010233000
    )
    assert isinstance(response, list), "The answer should be a list"
    for record in response:
        assert "delta" in record, "There must be a key 'delta'"
        assert "hash" in record, "There must be a key 'hash'"
        assert "time" in record, "There must be a key 'time'"
        delta = record["delta"]
        for key in ["coin", "fundingRate", "szi", "type", "usdc"]:
            assert key in delta, f"There must be a key '{key}' in 'delta'"
        assert delta["type"] == "funding", "The type must be 'funding'"


@pytest.mark.vcr()
def test_user_funding_history_without_end_time():
    info = Info(skip_ws=True, meta=TEST_META, spot_meta=TEST_SPOT_META)
    response = info.user_funding_history(user="******************************************", startTime=1681923833000)
    assert isinstance(response, list), "The answer must be a list"
    for record in response:
        assert "delta" in record, "There must be a key 'delta'"
        assert "hash" in record, "There must be a key 'hash'"
        assert "time" in record, "There must be a key 'time'"
        delta = record["delta"]
        for key in ["coin", "fundingRate", "szi", "type", "usdc"]:
            assert key in delta, f"There must be a key '{key}' in 'delta'"
        assert delta["type"] == "funding", "The type must be 'funding'"
