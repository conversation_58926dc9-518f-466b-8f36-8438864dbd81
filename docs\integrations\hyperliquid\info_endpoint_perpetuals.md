# HyperLiquid API 永续合约Info端点

本文档详细介绍了HyperLiquid API中专门用于永续合约的Info端点功能。这些端点用于获取永续合约的元数据、资产上下文、账户状态和历史数据等信息。

## 基本信息

- **端点URL**: `https://api.hyperliquid.xyz/info`
- **请求方法**: `POST`
- **内容类型**: `application/json`

## 获取所有永续合约交易所

获取所有可用的永续合约交易所列表。

### 请求

```json
{
  "type": "perpDexs"
}
```

### 响应

```json
[
  null,
  {
    "name": "test",
    "full_name": "test dex",
    "deployer": "******************************************",
    "oracle_updater": null
  }
]
```

## 获取永续合约元数据

获取永续合约的元数据，包括可交易的币种、小数位数和最大杠杆等信息。

### 请求

```json
{
  "type": "meta",
  "dex": "" // 可选，默认为第一个永续合约交易所
}
```

### 响应

```json
{
  "universe": [
    {
      "name": "BTC",
      "szDecimals": 5,
      "maxLeverage": 50
    },
    {
      "name": "ETH",
      "szDecimals": 4,
      "maxLeverage": 50
    },
    {
      "name": "HPOS",
      "szDecimals": 0,
      "maxLeverage": 3,
      "onlyIsolated": true
    },
    {
      "name": "LOOM",
      "szDecimals": 1,
      "maxLeverage": 3,
      "onlyIsolated": true,
      "isDelisted": true
    }
  ]
}
```

## 获取永续合约资产上下文

获取永续合约的资产上下文，包括标记价格、当前资金费率、未平仓合约数量等信息。

### 请求

```json
{
  "type": "metaAndAssetCtxs"
}
```

### 响应

```json
[
  {
    "universe": [
      {
        "name": "BTC",
        "szDecimals": 5,
        "maxLeverage": 50
      },
      // 更多币种...
    ]
  },
  [
    {
      "dayNtlVlm": "1169046.29406",
      "funding": "0.0000125",
      "impactPxs": [
        "14.3047",
        "14.3444"
      ],
      "markPx": "14.3161",
      "midPx": "14.314",
      "openInterest": "688.11",
      "oraclePx": "14.32",
      "premium": "0.00031774",
      "prevDayPx": "15.322"
    },
    // 更多资产上下文...
  ]
]
```

## 获取用户的永续合约账户摘要

获取用户的未平仓头寸和保证金摘要。

### 请求

```json
{
  "type": "clearinghouseState",
  "user": "******************************************",
  "dex": "" // 可选，默认为第一个永续合约交易所
}
```

### 响应

```json
{
  "assetPositions": [
    {
      "position": {
        "coin": "ETH",
        "cumFunding": {
          "allTime": "514.085417",
          "sinceChange": "0.0",
          "sinceOpen": "0.0"
        },
        "entryPx": "2986.3",
        "leverage": {
          "rawUsd": "-95.059824",
          "type": "isolated",
          "value": 20
        },
        "liquidationPx": "2866.********",
        "marginUsed": "4.967826",
        "maxLeverage": 50,
        "positionValue": "100.02765",
        "returnOnEquity": "-0.0026789",
        "szi": "0.0335",
        "unrealizedPnl": "-0.0134"
      },
      "type": "oneWay"
    }
  ],
  "crossMaintenanceMarginUsed": "0.0",
  "crossMarginSummary": {
    "accountValue": "13104.514502",
    "totalMarginUsed": "0.0",
    "totalNtlPos": "0.0",
    "totalRawUsd": "13104.514502"
  },
  "marginSummary": {
    "accountValue": "13109.482328",
    "totalMarginUsed": "4.967826",
    "totalNtlPos": "100.02765",
    "totalRawUsd": "13009.454678"
  },
  "time": *************,
  "withdrawable": "13104.514502"
}
```

## 获取用户的资金费用历史或非资金费用账本更新

获取用户的资金费用历史或非资金费用账本更新（如存款、转账和提款）。

### 请求

```json
{
  "type": "userFunding", // 或 "userNonFundingLedgerUpdates"
  "user": "******************************************",
  "startTime": *************, // 开始时间（毫秒），包含
  "endTime": ************* // 结束时间（毫秒），包含，默认为当前时间
}
```

### 响应

```json
[
  {
    "delta": {
      "coin": "ETH",
      "fundingRate": "0.0000417",
      "szi": "49.1477",
      "type": "funding",
      "usdc": "-3.625312"
    },
    "hash": "0xa166e3fa63c25663024b03f2e0da011a00307e4017465df020210d3d432e7cb8",
    "time": *************
  },
  // 更多记录...
]
```

## 获取历史资金费率

获取特定币种的历史资金费率。

### 请求

```json
{
  "type": "fundingHistory",
  "coin": "ETH",
  "startTime": *************, // 开始时间（毫秒），包含
  "endTime": ************* // 结束时间（毫秒），包含，默认为当前时间
}
```

### 响应

```json
[
  {
    "coin": "ETH",
    "fundingRate": "-0.00022196",
    "premium": "-0.00052196",
    "time": 1683849600076
  }
]
```

## 获取不同交易所的预测资金费率

获取不同交易所的预测资金费率。

### 请求

```json
{
  "type": "predictedFundings"
}
```

### 响应

```json
[
  [
    "AVAX",
    [
      [
        "BinPerp",
        {
          "fundingRate": "0.0001",
          "nextFundingTime": 1733961600000
        }
      ],
      [
        "HlPerp",
        {
          "fundingRate": "0.0000125",
          "nextFundingTime": 1733958000000
        }
      ],
      [
        "BybitPerp",
        {
          "fundingRate": "0.0001",
          "nextFundingTime": 1733961600000
        }
      ]
    ]
  ],
  // 更多币种...
]
```

## 查询达到未平仓合约上限的永续合约

获取已达到未平仓合约上限的永续合约列表。

### 请求

```json
{
  "type": "perpsAtOpenInterestCap"
}
```

### 响应

```json
["BADGER", "CANTO", "FTM", "LOOM", "PURR"]
```

## 最佳实践

1. **缓存元数据**：元数据不经常变化，可以缓存以减少API调用。
2. **使用分页**：对于大型数据集，使用分页以避免超时和数据丢失。
3. **处理错误**：实现适当的错误处理机制，以应对API可能返回的各种错误。
4. **监控资金费率**：定期获取资金费率，以便在费率变化时调整策略。
5. **检查未平仓合约上限**：在下单前检查币种是否已达到未平仓合约上限。
