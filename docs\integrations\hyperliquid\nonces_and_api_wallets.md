# HyperLiquid API Nonces和API钱包

本文档详细介绍了HyperLiquid交易所中的Nonces机制和API钱包的使用方法，这对于开发自动化交易策略至关重要。

## 背景

去中心化的L1区块链必须防止重放攻击。例如，当用户签署USDC转账交易时，接收方不能多次广播同一交易以耗尽发送方的钱包。为解决这个问题，以太坊为每个地址存储一个"nonce"，这是一个从0开始的数字。每个交易必须使用恰好"nonce + 1"才能被包含在区块中。

## API钱包

API钱包在文档中也被称为`agent wallets`（代理钱包）。主账户可以授权API钱包代表主账户或任何子账户进行签名。

**重要说明**：API钱包仅用于签名。要查询与主账户或子账户相关的账户数据，必须传入该账户的实际地址。一个常见的错误是使用代理钱包地址，这会导致空结果。

## API钱包清理

在以下情况下，API钱包及其关联的nonce状态可能会被清理：

1. **钱包被注销**：当发送ApproveAgent操作注册新的未命名API钱包时，现有的未命名API钱包会被注销。同样，当发送具有匹配名称的ApproveAgent操作时，现有的命名API钱包也会被注销。

2. **钱包过期**：当API钱包超过其设定的有效期时。

3. **注册代理的账户没有资金**：当注册代理的账户不再持有资金时。

**重要提示**：对于以编程方式使用API钱包的用户，**强烈建议不要重复使用其地址**。一旦代理被注销，其使用过的nonce状态可能会被清理。在未来使用时生成新的代理钱包，以避免意外行为。例如，一旦nonce集被清理，之前签名的操作可能会被重放。

## HyperLiquid Nonces机制

以太坊的设计不适用于链上订单簿。做市策略可能在一秒内发送数千个订单和取消请求。要求在区块链上精确排序包含将破坏任何策略。

在HyperLiquid上，每个地址存储100个最高的nonces。每个新交易必须具有大于此集合中最小nonce的nonce，并且该nonce之前从未被使用过。Nonces按签名者跟踪，如果使用地址的私钥签名，则签名者是用户地址；如果使用API钱包签名，则签名者是代理地址。

Nonces必须在`(T - 2天, T + 1天)`范围内，其中`T`是交易区块的Unix毫秒时间戳。

## 从中心化交易所迁移自动化策略的步骤

以下步骤可能有助于将自动化策略从中心化交易所迁移到HyperLiquid：

1. **每个交易进程使用一个API钱包**：请注意，nonces按签名者（即私钥）存储，因此由同一API钱包签名的不同子账户将共享API钱包的nonce跟踪器。建议为不同的子账户使用单独的API钱包。

2. **批处理请求**：在每个交易进程中，设置一个任务，每0.1秒定期批处理订单和取消请求。建议将IOC和GTC订单与ALO订单分开批处理，因为仅包含ALO订单的批次会被验证者优先处理。

3. **交易逻辑任务**：交易逻辑任务将订单和取消发送到批处理任务。

4. **唯一nonce**：对于每批订单或取消，获取并递增一个原子计数器，确保地址的唯一nonce。如果需要，原子计数器可以快进到当前Unix毫秒。

这种结构对于2秒内的乱序交易具有鲁棒性，这对于地理位置靠近API服务器的自动化策略应该足够。

## 子账户和保险库用户的建议

请注意，nonces按签名者存储，即用于签署交易的私钥的地址。因此，建议每个交易进程或前端会话使用单独的私钥进行签名。特别是，为用户、保险库或子账户签名的单个API钱包共享相同的nonce集。

如果用户想要并行使用多个子账户，更容易的方法是在主账户下生成两个单独的API钱包，并为每个子账户使用一个API钱包。这避免了每个子账户使用的nonce集之间的冲突。

## 最佳实践

1. **不要重用API钱包地址**：一旦API钱包被注销，生成新的API钱包而不是重用旧地址。

2. **为不同子账户使用不同API钱包**：这可以避免nonce冲突。

3. **实现批处理机制**：定期批处理订单和取消请求，而不是单独发送每个请求。

4. **维护原子计数器**：确保每个交易使用唯一的nonce。

5. **考虑地理位置**：将自动化交易系统部署在靠近API服务器的位置，以减少延迟。

## 示例：API钱包管理

以下是一个简单的Python示例，展示如何管理API钱包和nonces：

```python
import time
import threading

class NonceManager:
    def __init__(self):
        self.nonce = int(time.time() * 1000)  # 初始化为当前时间戳（毫秒）
        self.lock = threading.Lock()
    
    def get_next_nonce(self):
        with self.lock:
            current_time = int(time.time() * 1000)
            # 确保nonce至少是当前时间
            self.nonce = max(self.nonce + 1, current_time)
            return self.nonce

class ApiWalletManager:
    def __init__(self):
        self.api_wallets = {}  # 子账户ID -> (API钱包, NonceManager)
    
    def create_api_wallet_for_subaccount(self, subaccount_id):
        # 在实际应用中，这里应该生成新的API钱包
        api_wallet = f"new_api_wallet_{subaccount_id}_{int(time.time())}"
        nonce_manager = NonceManager()
        self.api_wallets[subaccount_id] = (api_wallet, nonce_manager)
        return api_wallet
    
    def get_nonce_for_subaccount(self, subaccount_id):
        if subaccount_id not in self.api_wallets:
            self.create_api_wallet_for_subaccount(subaccount_id)
        _, nonce_manager = self.api_wallets[subaccount_id]
        return nonce_manager.get_next_nonce()
```

## 总结

理解并正确实现HyperLiquid的nonces机制和API钱包管理对于开发高效、可靠的自动化交易策略至关重要。通过遵循本文档中的最佳实践，开发者可以避免常见的陷阱，并确保其交易系统能够顺利运行。
