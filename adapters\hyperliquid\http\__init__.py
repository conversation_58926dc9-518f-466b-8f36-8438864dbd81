# -------------------------------------------------------------------------------------------------
#  Copyright (C) 2015-2023 Nautech Systems Pty Ltd. All rights reserved.
#  https://nautechsystems.io
#
#  Licensed under the GNU Lesser General Public License Version 3.0 (the "License");
#  You may not use this file except in compliance with the License.
#  You may obtain a copy of the License at https://www.gnu.org/licenses/lgpl-3.0.en.html
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
# -------------------------------------------------------------------------------------------------
"""
Hyperliquid HTTP API client components.

This package provides HTTP client implementations for the Hyperliquid exchange,
including market data, trading operations, and account management.
"""

from nautilus_trader.adapters.hyperliquid.http.account import HyperliquidAccountHttpApi
from nautilus_trader.adapters.hyperliquid.http.client import HyperliquidHttpClient
from nautilus_trader.adapters.hyperliquid.http.error import HyperliquidError
from nautilus_trader.adapters.hyperliquid.http.exchange import HyperliquidExchangeHttpApi
from nautilus_trader.adapters.hyperliquid.http.market import HyperliquidMarketHttpApi


__all__ = [
    "HyperliquidHttpClient",
    "HyperliquidMarketHttpApi",
    "HyperliquidExchangeHttpApi",
    "HyperliquidAccountHttpApi",
    "HyperliquidError",
]