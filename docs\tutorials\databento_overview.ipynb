{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["# Databento overview\n", "\n", "Databento documentation: \n", "\n", "* [https://databento.com/docs](https://databento.com/docs)\n", "\n", "## 3 services\n", "\n", "Databento provides 3 types of services:\n", "\n", "1. `Historical` - for data older than 24 hours\n", "2. `Live` - for data within the last 24 hours\n", "3. `Reference` - all data (Historical + Live together)\n", "\n", "## 3 file formats\n", "\n", "Databento supports 3 formats for data:\n", "\n", "* `DBN` - Databento Binary Encoding (binary)\n", "* `csv` - comma separated values (text)\n", "* `json` - JavaScript Object notation (text)\n", "\n", "## Python library\n", "\n", "Databento provides a simple Python library (used in this tutorial):\n", "\n", "`pip install -U databento`"]}, {"cell_type": "markdown", "id": "1", "metadata": {}, "source": ["## <PERSON><PERSON>as\n", "\n", "Schema is just a sophisticated name for `type of data` you want.\n", "\n", "Most used schemas ordered from most detailed:\n", "\n", "| Schema | Type | Description |\n", "|--------|------|-------------|\n", "| `mbo` | L3 data | Provides every order book event across every price level, keyed by order ID. Allows determination of queue position for each order, offering highest level of granularity available. |\n", "| `mbp-10` | L2 data | Provides every order book event across top ten price levels, keyed by price. Includes trades and changes to aggregate market depth, with total size and order count at top ten price levels. |\n", "| `mbp-1` | L1 data | Provides every order book event updating the top price level (BBO). Includes trades and changes to book depth, with total size and order count at BBO. |\n", "| `bbo-1s` | L1 sampled | Similar to L1 data but sampled in 1 second intervals. Provides last best bid, best offer, and sale at 1-second intervals. |\n", "| `tbbo` | L1 trades | Provides every trade event alongside the BBO immediately before the effect of each trade. Subset of MBP-1. |\n", "| `trades` | Trade data | Provides every trade event. This is a subset of MBO data. |\n", "| `ohlcv-1s` | 1s bars | OHLCV bars aggregated from trades at 1-second intervals. |\n", "| `ohlcv-1m` | 1m bars | OHLCV bars aggregated from trades at 1-minute intervals. |\n", "| `ohlcv-1h` | 1h bars | OHLCV bars aggregated from trades at 1-hour intervals. |\n", "| `ohlcv-1d` | 1d bars | OHLCV bars aggregated from trades at 1-day intervals. |\n", "| `definition` | Reference | Provides reference information about instruments including symbol, name, expiration date, listing date, tick size, strike price. |\n", "| `status` | Exchange status | Provides updates about trading session like halts, pauses, short-selling restrictions, auction start, and other matching engine statuses. |\n", "| `statistics` | Exchange stats | Provides official summary statistics published by venue, including daily volume, open interest, settlement prices, and official open/high/low prices. |\n", "\n", "**How Databento generates lower-resolution data?**\n", "\n", "1. Databento first collects the most detailed market data available from each source (mostly `mbo` if available)\n", "2. and then derives all other formats from this most granular data to ensure 100% consistency across all data types (schemas).\n", "\n", "Additional sources:\n", "\n", "* Example tutorial how to convert tick/trades data into bars:\n", "    * [https://databento.com/docs/examples/basics-historical/tick-resampling/example](https://databento.com/docs/examples/basics-historical/tick-resampling/example)\n", "* All schemas explained in detail:\n", "    * [https://databento.com/docs/schemas-and-data-formats?historical=python&live=python&reference=python](https://databento.com/docs/schemas-and-data-formats?historical=python&live=python&reference=python)"]}, {"cell_type": "markdown", "id": "2", "metadata": {"jp-MarkdownHeadingCollapsed": true}, "source": ["## Symbology\n", "\n", "Symbology is just a sophisticated name for the naming convention of various instruments. Abbreviation `stypes` is often used in API and docs and means \"symbology types\".\n", "\n", "Databento support 5 symbologies (naming conventions):\n", "\n", "| Symbology Type    | Description                                      | Example/Pattern                | Key Notes                                                                   |\n", "|:-----------------|:-------------------------------------------------|:------------------------------|:----------------------------------------------------------------------------|\n", "| `raw_symbol`     | Original string symbols used by data publisher    | `AAPL`, `ESH3`                | Best for direct market connectivity environments                             |\n", "| `instrument_id`  | Unique numeric IDs assigned by publisher          | `12345`, `9876543`            | Space-efficient but can be remapped daily by some publishers                 |\n", "| `parent`         | Groups related symbols using root symbol          | `ES.FUT`, `ES.OPT`            | Allows querying all futures/options for a root symbol at once                |\n", "| `continuous`     | References instruments that change over time      | `ES.c.0`, `CL.n.1`, `ZN.v.0`  | Roll rules: Calendar (c), Open Interest (n), Volume (v)                      |\n", "| `ALL_SYMBOLS`    | Requests all symbols in dataset                  | `ALL_SYMBOLS`                  |    |\n", "\n", "\n", "When requesting data, **input** and **output** symbology can be specified. These 4 combinations are supported (for various exchanges / publishers):\n", "\n", "| SType in    | SType out      | DBEQ.BASIC | GLBX.MDP3 | IFEU.IMPACT | NDEX.IMPACT | OPRA.PILLAR | XNAS.ITCH |\n", "|:---------------|:-----------------|:-----------|:----------|:------------|:------------|:------------|:----------|\n", "| `parent`       | `instrument_id`  |            | ✓         | ✓           | ✓           | ✓           |           |\n", "| `continuous`   | `instrument_id`  |            | ✓         |             |             |             |           |\n", "| `raw_symbol`   | `instrument_id`  | ✓          | ✓         | ✓           | ✓           | ✓           | ✓         |\n", "| `instrument_id`| `raw_symbol`     | ✓          | ✓         | ✓           | ✓           | ✓           | ✓         |\n", "\n", "For more details:\n", "\n", "* [https://databento.com/docs/standards-and-conventions/symbology?historical=python&live=python&reference=python](https://databento.com/docs/standards-and-conventions/symbology?historical=python&live=python&reference=python)"]}, {"cell_type": "markdown", "id": "3", "metadata": {}, "source": ["## Databento file format\n", "\n", "Databento uses its own file format for market-data. It is called **Databento Binary Encoding (DBN)**.\n", "Think of it like more performant + compressed alternative of CSV / JSON files.\n", "\n", "You can easily load DBN file and convert it into simple CSV / JSON data.\n", "\n", "For more details:\n", "\n", "* [https://databento.com/docs/standards-and-conventions/databento-binary-encoding#getting-started-with-dbn?historical=python&live=python&reference=python](https://databento.com/docs/standards-and-conventions/databento-binary-encoding#getting-started-with-dbn?historical=python&live=python&reference=python)"]}, {"cell_type": "markdown", "id": "4", "metadata": {}, "source": ["# Historical API examples"]}, {"cell_type": "markdown", "id": "5", "metadata": {}, "source": ["## Authenticate & connect to Databento"]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [], "source": ["import databento as db\n", "\n", "\n", "# Establish connection and authenticate\n", "API_KEY = \"db-8VWGBis54s4ewGVciMRakNxLCJKen\"   # put your API key here (existing key is just example, not real)\n", "client = db.Historical(API_KEY)"]}, {"cell_type": "markdown", "id": "7", "metadata": {}, "source": ["## <PERSON><PERSON><PERSON>\n", "\n", "### List Publishers\n", "\n", "Shows all data publishers."]}, {"cell_type": "code", "execution_count": null, "id": "8", "metadata": {}, "outputs": [], "source": ["publishers = client.metadata.list_publishers()\n", "\n", "# Show only first five from long list\n", "publishers[:5]"]}, {"cell_type": "markdown", "id": "9", "metadata": {}, "source": ["Example output:\n", "\n", "```python\n", "[{'publisher_id': 1,\n", "  'dataset': 'GLBX.MDP3',\n", "  'venue': 'GLBX',\n", "  'description': 'CME Globex MDP 3.0'},\n", " {'publisher_id': 2,\n", "  'dataset': 'XNAS.ITCH',\n", "  'venue': 'XNAS',\n", "  'description': 'Nasdaq TotalView-ITCH'},\n", " {'publisher_id': 3,\n", "  'dataset': 'XBOS.ITCH',\n", "  'venue': 'XBOS',\n", "  'description': 'Nasdaq BX TotalView-ITCH'},\n", " {'publisher_id': 4,\n", "  'dataset': 'XPSX.ITCH',\n", "  'venue': 'XPSX',\n", "  'description': 'Nasdaq PSX TotalView-ITCH'},\n", " {'publisher_id': 5,\n", "  'dataset': 'BATS.PITCH',\n", "  'venue': 'BATS',\n", "  'description': 'Cboe BZX Depth Pitch'}]\n", "```"]}, {"cell_type": "markdown", "id": "10", "metadata": {}, "source": ["### List Datasets\n", "\n", "Each dataset is in format: `PUBLISHER.DATASET`\n", "\n", "* Publisher / Market code is based on: [https://www.iso20022.org/market-identifier-codes](https://www.iso20022.org/market-identifier-codes)"]}, {"cell_type": "code", "execution_count": null, "id": "11", "metadata": {}, "outputs": [], "source": ["datasets = client.metadata.list_datasets()\n", "datasets"]}, {"cell_type": "markdown", "id": "12", "metadata": {}, "source": ["Example output:\n", "\n", "```python\n", "['ARCX.PILLAR',\n", " 'DBEQ.BASIC',\n", " 'EPRL.DOM',\n", " 'EQUS.SUMMARY',\n", " 'GLBX.MDP3',\n", " 'IEXG.TOPS',\n", " 'IFEU.IMPACT',\n", " 'NDEX.IMPACT',\n", " 'OPRA.PILLAR',\n", " 'XASE.PILLAR',\n", " 'XBOS.ITCH',\n", " 'XCHI.PILLAR',\n", " 'XCIS.TRADESBBO',\n", " 'XNAS.BASIC',\n", " 'XNAS.ITCH',\n", " 'XNYS.PILLAR',\n", " 'XPSX.ITCH']\n", "```"]}, {"cell_type": "markdown", "id": "13", "metadata": {}, "source": ["### List Schemas\n", "\n", "List all supported data formats in Databento."]}, {"cell_type": "code", "execution_count": null, "id": "14", "metadata": {}, "outputs": [], "source": ["schemas = client.metadata.list_schemas(dataset=\"GLBX.MDP3\")\n", "schemas"]}, {"cell_type": "markdown", "id": "15", "metadata": {}, "source": ["Example output:\n", "\n", "```python\n", "['mbo',\n", " 'mbp-1',\n", " 'mbp-10',\n", " 'tbbo',\n", " 'trades',\n", " 'bbo-1s',\n", " 'bbo-1m',\n", " 'ohlcv-1s',\n", " 'ohlcv-1m',\n", " 'ohlcv-1h',\n", " 'ohlcv-1d',\n", " 'definition',\n", " 'statistics',\n", " 'status']\n", "```"]}, {"cell_type": "markdown", "id": "16", "metadata": {}, "source": ["### Dataset condition\n", "\n", "Show data availability and quality."]}, {"cell_type": "code", "execution_count": null, "id": "17", "metadata": {}, "outputs": [], "source": ["conditions = client.metadata.get_dataset_condition(\n", "    dataset=\"GLBX.MDP3\",\n", "    start_date=\"2022-06-06\",\n", "    end_date=\"2022-06-10\",\n", ")\n", "\n", "conditions"]}, {"cell_type": "markdown", "id": "18", "metadata": {}, "source": ["Example output:\n", "\n", "```python\n", "[{'date': '2022-06-06',\n", "  'condition': 'available',\n", "  'last_modified_date': '2024-05-18'},\n", " {'date': '2022-06-07',\n", "  'condition': 'available',\n", "  'last_modified_date': '2024-05-21'},\n", " {'date': '2022-06-08',\n", "  'condition': 'available',\n", "  'last_modified_date': '2024-05-21'},\n", " {'date': '2022-06-09',\n", "  'condition': 'available',\n", "  'last_modified_date': '2024-05-21'},\n", " {'date': '2022-06-10',\n", "  'condition': 'available',\n", "  'last_modified_date': '2024-05-22'}]\n", "```"]}, {"cell_type": "markdown", "id": "19", "metadata": {}, "source": ["### Dataset range\n", "\n", "Show available range for dataset.\n", "\n", "* Use this method to discover data availability.\n", "* The start and end values in the response can be used with the `timeseries.get_range` and `batch.submit_job` endpoints."]}, {"cell_type": "code", "execution_count": null, "id": "20", "metadata": {}, "outputs": [], "source": ["available_range = client.metadata.get_dataset_range(dataset=\"GLBX.MDP3\")\n", "available_range"]}, {"cell_type": "markdown", "id": "21", "metadata": {}, "source": ["Example output:\n", "\n", "```python\n", "{'start': '2010-06-06T00:00:00.000000000Z',\n", " 'end': '2025-01-18T00:00:00.000000000Z'}\n", "```"]}, {"cell_type": "markdown", "id": "22", "metadata": {}, "source": ["### Record count\n", "\n", "Returns count of records return from data query."]}, {"cell_type": "code", "execution_count": null, "id": "23", "metadata": {}, "outputs": [], "source": ["record_count = client.metadata.get_record_count(\n", "    dataset=\"GLBX.MDP3\",\n", "    symbols=[\"ESM2\"],   # ES (S&P contract) expiring in June 2022\n", "    schema=\"ohlcv-1h\",  # 1 hour bars ; only time-ranges that are multiplies of 10-minutes (cannot be used for 1-min bars)\n", "    start=\"2022-01-06\", # including start\n", "    end=\"2022-01-07\"    # excluding end\n", ")\n", "\n", "# There is one hour break on the exchange, so 23 hourly bars are OK\n", "record_count"]}, {"cell_type": "markdown", "id": "24", "metadata": {}, "source": ["Example output:\n", "\n", "`23`"]}, {"cell_type": "markdown", "id": "25", "metadata": {}, "source": ["### Costs\n", "\n", "Get costs = how much you pay for the data in US dollars."]}, {"cell_type": "code", "execution_count": null, "id": "26", "metadata": {}, "outputs": [], "source": ["cost = client.metadata.get_cost(\n", "    dataset=\"GLBX.MDP3\",\n", "    symbols=[\"ESM2\"],\n", "    schema=\"ohlcv-1h\",  # 1 hour bars ; only time-ranges that are multiplies of 10-minutes (cannot be used for 1-min bars)\n", "    start=\"2022-01-06\", # including start\n", "    end=\"2022-01-07\"    # excluding end\n", ")\n", "\n", "cost"]}, {"cell_type": "markdown", "id": "27", "metadata": {}, "source": ["Example output:\n", "\n", "`0.00022791326`"]}, {"cell_type": "markdown", "id": "28", "metadata": {}, "source": ["## Time series data\n", "\n", "### `get_range`\n", "\n", "* Makes a streaming request for time series data from Databento.\n", "* This is the primary method for getting historical market data, instrument definitions, and status data directly into your application.\n", "* This method only returns after all of the data has been downloaded, which can take a long time.\n", "\n", "**Warning:**\n", "* `ts_event` represents start-time of aggregation. So if we download bars, the timestamp represents **opening time** for each bar."]}, {"cell_type": "code", "execution_count": null, "id": "29", "metadata": {}, "outputs": [], "source": ["data = client.timeseries.get_range(\n", "    dataset=\"GLBX.MDP3\",\n", "    symbols=[\"ESM2\"],            # ES (S&P contract) expiring in June 2022\n", "    schema=\"ohlcv-1h\",           # Hourly bars\n", "    start=\"2022-06-01T00:00:00\",\n", "    end=\"2022-06-03T00:10:00\",\n", "    limit=5,                    # Optional limit on count of results\n", ")\n", "\n", "# Data are received in DBNStore format\n", "data"]}, {"cell_type": "markdown", "id": "30", "metadata": {"jp-MarkdownHeadingCollapsed": true}, "source": ["Example output:\n", "\n", "`<DBNStore(schema=ohlcv-1h)>`"]}, {"cell_type": "code", "execution_count": null, "id": "31", "metadata": {}, "outputs": [], "source": ["# Convert DBN format to pandas-dataframe\n", "df = data.to_df()\n", "\n", "# Preview\n", "print(len(df))\n", "df"]}, {"cell_type": "markdown", "id": "32", "metadata": {}, "source": ["Example output: *(not real data, just example of output format)*\n", "\n", "| ts_event | rtype | publisher_id | instrument_id | open | high | low | close | volume | symbol |\n", "|:--|:--|:--|:--|:--|:--|:--|:--|:--|:--|\n", "| 2022-06-01 00:00:00+00:00 | 34 | 1 | 3403 | 4149.25 | 4153.50 | 4149.00 | 4150.75 | 9281 | ESM2 |\n", "| 2022-06-01 01:00:00+00:00 | 34 | 1 | 3403 | 4151.00 | 4157.75 | 4149.50 | 4154.25 | 11334 | ESM2 |\n", "| 2022-06-01 02:00:00+00:00 | 34 | 1 | 3403 | 4154.25 | 4155.25 | 4146.50 | 4147.00 | 7258 | ESM2 |"]}, {"cell_type": "markdown", "id": "33", "metadata": {}, "source": ["Note:\n", "\n", "* `rtype` = 1-hour bars \n", "* More codes like this: [https://databento.com/docs/standards-and-conventions/common-fields-enums-types#rtype?historical=python&live=python&reference=python](https://databento.com/docs/standards-and-conventions/common-fields-enums-types#rtype?historical=python&live=python&reference=python)"]}, {"cell_type": "markdown", "id": "34", "metadata": {}, "source": ["## Symbols\n", "\n", "### `resolve`\n", "\n", "Resolve a list of symbols from an **input** symbology type, to an **output** symbology type.\n", "\n", "* Example: `raw_symbol` to an `instrument_id`: `ESM2` → `3403`"]}, {"cell_type": "code", "execution_count": null, "id": "35", "metadata": {}, "outputs": [], "source": ["result = client.symbology.resolve(\n", "    dataset=\"GLBX.MDP3\",\n", "    symbols=[\"ESM2\"],\n", "    stype_in=\"raw_symbol\",\n", "    stype_out=\"instrument_id\",\n", "    start_date=\"2022-06-01\",\n", "    end_date=\"2022-06-30\",\n", ")\n", "\n", "result"]}, {"cell_type": "markdown", "id": "36", "metadata": {}, "source": ["Example output:\n", "\n", "```python\n", "{'result': {'ESM2': [{'d0': '2022-06-01', 'd1': '2022-06-26', 's': '3403'}]},\n", " 'symbols': ['ESM2'],\n", " 'stype_in': 'raw_symbol',\n", " 'stype_out': 'instrument_id',\n", " 'start_date': '2022-06-01',\n", " 'end_date': '2022-06-30',\n", " 'partial': [],\n", " 'not_found': [],\n", " 'message': 'OK',\n", " 'status': 0}\n", "```"]}, {"cell_type": "markdown", "id": "37", "metadata": {}, "source": ["Most important is the `result` and key-value pair `'s': '3403'`, which contains value of instrument_id."]}, {"cell_type": "markdown", "id": "38", "metadata": {}, "source": ["## DBNStore operations\n", "\n", "The `DBNStore` object is an helper class for working with `DBN` encoded data."]}, {"cell_type": "markdown", "id": "39", "metadata": {}, "source": ["### `from_bytes`\n", "\n", "Read data from a DBN byte stream."]}, {"cell_type": "code", "execution_count": null, "id": "40", "metadata": {}, "outputs": [], "source": ["dbn_data = client.timeseries.get_range(\n", "    dataset=\"GLBX.MDP3\",\n", "    symbols=[\"ESM2\"],\n", "    schema=\"ohlcv-1h\",\n", "    start=\"2022-06-06\",\n", "    limit=3\n", ")\n", "\n", "dbn_data.to_df()"]}, {"cell_type": "markdown", "id": "41", "metadata": {}, "source": ["Example output: *(not real data, just example of output format)*\n", "\n", "| ts_event | rtype | publisher_id | instrument_id | open | high | low | close | volume | symbol |\n", "|:--|:--|:--|:--|:--|:--|:--|:--|:--|:--|\n", "| 2022-06-06 00:00:00+00:00 | 34 | 1 | 3403 | 4109.50 | 4117.00 | 4105.50 | 4115.75 | 8541 | ESM2 |\n", "| 2022-06-06 01:00:00+00:00 | 34 | 1 | 3403 | 4115.75 | 4122.75 | 4113.00 | 4122.25 | 14008 | ESM2 |\n", "| 2022-06-06 02:00:00+00:00 | 34 | 1 | 3403 | 4122.25 | 4127.00 | 4120.75 | 4126.25 | 10150 | ESM2 |"]}, {"cell_type": "code", "execution_count": null, "id": "42", "metadata": {}, "outputs": [], "source": ["# Save streamed data to file - recommended suffix is: `*.dbn.zst`\n", "path = \"./GLBX-ESM2-20220606.ohlcv-1h.dbn.zst\"\n", "dbn_data.to_file(path)"]}, {"cell_type": "code", "execution_count": null, "id": "43", "metadata": {}, "outputs": [], "source": ["# Load data from previously saved file and create DBN object again\n", "with open(path, \"rb\") as saved:\n", "    loaded_dbn_data = db.DBNStore.from_bytes(saved)\n", "\n", "loaded_dbn_data.to_df()"]}, {"cell_type": "markdown", "id": "44", "metadata": {}, "source": ["Example output *(not real data, just example of output format)*:\n", "\n", "| ts_event | rtype | publisher_id | instrument_id | open | high | low | close | volume | symbol |\n", "|:--|:--|:--|:--|:--|:--|:--|:--|:--|:--|\n", "| 2022-06-06 00:00:00+00:00 | 34 | 1 | 3403 | 4109.50 | 4117.00 | 4105.50 | 4115.75 | 8541 | ESM2 |\n", "| 2022-06-06 01:00:00+00:00 | 34 | 1 | 3403 | 4115.75 | 4122.75 | 4113.00 | 4122.25 | 14008 | ESM2 |\n", "| 2022-06-06 02:00:00+00:00 | 34 | 1 | 3403 | 4122.25 | 4127.00 | 4120.75 | 4126.25 | 10150 | ESM2 |"]}, {"cell_type": "markdown", "id": "45", "metadata": {}, "source": ["### `from_file`\n", "\n", "Reads data from a DBN file."]}, {"cell_type": "code", "execution_count": null, "id": "46", "metadata": {}, "outputs": [], "source": ["loaded_dbn_data = db.DBNStore.from_file(path)\n", "loaded_dbn_data.to_df()"]}, {"cell_type": "markdown", "id": "47", "metadata": {}, "source": ["Example output: *(not real data, just example of output format)*\n", "\n", "| ts_event | rtype | publisher_id | instrument_id | open | high | low | close | volume | symbol |\n", "|:--|:--|:--|:--|:--|:--|:--|:--|:--|:--|\n", "| 2022-06-06 00:00:00+00:00 | 34 | 1 | 3403 | 4109.50 | 4117.00 | 4105.50 | 4115.75 | 8541 | ESM2 |\n", "| 2022-06-06 01:00:00+00:00 | 34 | 1 | 3403 | 4115.75 | 4122.75 | 4113.00 | 4122.25 | 14008 | ESM2 |\n", "| 2022-06-06 02:00:00+00:00 | 34 | 1 | 3403 | 4122.25 | 4127.00 | 4120.75 | 4126.25 | 10150 | ESM2 |"]}, {"cell_type": "markdown", "id": "48", "metadata": {}, "source": ["### `to_csv`\n", "\n", "Write data to a file in CSV format."]}, {"cell_type": "code", "execution_count": null, "id": "49", "metadata": {}, "outputs": [], "source": ["dbn_data = client.timeseries.get_range(\n", "    dataset=\"GLBX.MDP3\",\n", "    symbols=[\"ESM2\"],\n", "    schema=\"ohlcv-1h\",\n", "    start=\"2022-06-06\",\n", "    limit=3\n", ")\n", "\n", "# Export to CSV file\n", "dbn_data.to_csv(\"GLBX-ESM2-20220606-ohlcv-1h.csv\")"]}, {"cell_type": "markdown", "id": "50", "metadata": {}, "source": ["### `to_df`\n", "\n", "Converts DBN data to a pandas DataFrame."]}, {"cell_type": "code", "execution_count": null, "id": "51", "metadata": {}, "outputs": [], "source": ["# Export to pandas DataFrame\n", "dbn_data.to_df()"]}, {"cell_type": "markdown", "id": "52", "metadata": {}, "source": ["Example output: *(not real data, just example of output format)*\n", "\n", "| ts_event | rtype | publisher_id | instrument_id | open | high | low | close | volume | symbol |\n", "|:--|:--|:--|:--|:--|:--|:--|:--|:--|:--|\n", "| 2022-06-06 00:00:00+00:00 | 34 | 1 | 3403 | 4109.50 | 4117.00 | 4105.50 | 4115.75 | 8541 | ESM2 |\n", "| 2022-06-06 01:00:00+00:00 | 34 | 1 | 3403 | 4115.75 | 4122.75 | 4113.00 | 4122.25 | 14008 | ESM2 |\n", "| 2022-06-06 02:00:00+00:00 | 34 | 1 | 3403 | 4122.25 | 4127.00 | 4120.75 | 4126.25 | 10150 | ESM2 |"]}, {"cell_type": "markdown", "id": "53", "metadata": {}, "source": ["### `to_json`\n", "\n", "Write data to a file in JSON format."]}, {"cell_type": "code", "execution_count": null, "id": "54", "metadata": {}, "outputs": [], "source": ["# Export to pandas DataFrame\n", "dbn_data.to_json(\"GLBX-ESM2-20220606-ohlcv-1h.json\")"]}, {"cell_type": "markdown", "id": "55", "metadata": {}, "source": ["### `to_file`\n", "\n", "Write data to a DBN file."]}, {"cell_type": "code", "execution_count": null, "id": "56", "metadata": {}, "outputs": [], "source": ["# Export to DBN file\n", "dbn_data.to_file(\"GLBX-ESM2-20220606.ohlcv-1h.dbn.zst\")"]}, {"cell_type": "markdown", "id": "57", "metadata": {}, "source": ["### `to_n<PERSON><PERSON>`\n", "\n", "* Converts data to a numpy N-dimensional array.\n", "* Each element will contain a Python representation of the binary fields as a `Tuple`."]}, {"cell_type": "code", "execution_count": null, "id": "58", "metadata": {}, "outputs": [], "source": ["# Export to numpy-array\n", "ndarray = dbn_data.to_ndarray()\n", "n<PERSON><PERSON>"]}, {"cell_type": "markdown", "id": "59", "metadata": {}, "source": ["### `to_parquet`\n", "\n", "* Write data to a file in [Apache parquet](https://parquet.apache.org/) format."]}, {"cell_type": "code", "execution_count": null, "id": "60", "metadata": {}, "outputs": [], "source": ["# Export to Apache Parquet file\n", "dbn_data.to_parquet(\"GLBX-ESM2-20220606-ohlcv-1h.parquet\")"]}, {"cell_type": "markdown", "id": "61", "metadata": {}, "source": ["### `for` cycle\n", "\n", "* You can use standard python `for` cycle to iterate over DBN file content."]}, {"cell_type": "code", "execution_count": null, "id": "62", "metadata": {}, "outputs": [], "source": ["# Let's load some data first\n", "dbn_data = client.timeseries.get_range(\n", "    dataset=\"GLBX.MDP3\",\n", "    symbols=[\"ESM2\"],\n", "    schema=\"ohlcv-1h\",\n", "    start=\"2022-06-06\",\n", "    limit=3\n", ")\n", "\n", "# Contains 3 hourly bars\n", "dbn_data.to_df()"]}, {"cell_type": "markdown", "id": "63", "metadata": {}, "source": ["Example output: *(not real data, just example of output format)*\n", "\n", "| ts_event | rtype | publisher_id | instrument_id | open | high | low | close | volume | symbol |\n", "|:--|:--|:--|:--|:--|:--|:--|:--|:--|:--|\n", "| 2022-06-06 00:00:00+00:00 | 34 | 1 | 3403 | 4109.50 | 4117.00 | 4105.50 | 4115.75 | 8541 | ESM2 |\n", "| 2022-06-06 01:00:00+00:00 | 34 | 1 | 3403 | 4115.75 | 4122.75 | 4113.00 | 4122.25 | 14008 | ESM2 |\n", "| 2022-06-06 02:00:00+00:00 | 34 | 1 | 3403 | 4122.25 | 4127.00 | 4120.75 | 4126.25 | 10150 | ESM2 |"]}, {"cell_type": "code", "execution_count": null, "id": "64", "metadata": {}, "outputs": [], "source": ["# We can use DBN data in for-cycle:\n", "for bar in dbn_data:\n", "    print(bar)   # print full bar data\n", "    break        # intentionally break to see only 1st bar"]}, {"cell_type": "markdown", "id": "65", "metadata": {}, "source": ["Example output:\n", "\n", "```\n", "OhlcvMsg {\n", "    hd: Record<PERSON>eader {\n", "        length: 14,\n", "        rtype: Ohlcv1H,\n", "        publisher_id: GlbxMdp3Glbx,\n", "        instrument_id: 3403,\n", "        ts_event: 1654473600000000000\n", "    },\n", "    open: 4109.500000000,\n", "    high: 4117.000000000,\n", "    low: 4105.500000000,\n", "    close: 4115.750000000,\n", "    volume: 4543\n", "}\n", "```"]}, {"cell_type": "code", "execution_count": null, "id": "66", "metadata": {}, "outputs": [], "source": ["for bar in dbn_data:\n", "    print(f\"Bar open: {bar.open}\")  # print only bar-open information\n", "    break                           # intentionally break to see only 1st bar"]}, {"cell_type": "markdown", "id": "67", "metadata": {}, "source": ["Example output:\n", "\n", "`Bar open: 4108500000000`"]}, {"cell_type": "markdown", "id": "68", "metadata": {}, "source": ["# Examples\n", "\n", "## Download 1-min 6E data"]}, {"cell_type": "code", "execution_count": null, "id": "69", "metadata": {}, "outputs": [], "source": ["from datetime import timedelta\n", "\n", "import pandas as pd\n", "import pytz\n", "\n", "\n", "pd.set_option(\"display.max_columns\", None)\n", "pd.set_option(\"display.max_rows\", None)"]}, {"cell_type": "code", "execution_count": null, "id": "70", "metadata": {}, "outputs": [], "source": ["# Settings\n", "dataset=\"GLBX.MDP3\"\n", "symbol=\"6E.v.0\"\n", "stype_in=\"continuous\"\n", "schema=\"ohlcv-1m\"\n", "start=\"2025-01-01\"\n", "end=\"2025-01-05\""]}, {"cell_type": "code", "execution_count": null, "id": "71", "metadata": {}, "outputs": [], "source": ["# Check costs in dollars\n", "cost = client.metadata.get_cost(\n", "    dataset=dataset,\n", "    symbols=[symbol],\n", "    stype_in=stype_in,\n", "    schema=schema,\n", "    start=start,\n", "    end=end,\n", ")\n", "\n", "print(f\"{cost:.2f}$\")"]}, {"cell_type": "markdown", "id": "72", "metadata": {}, "source": ["Example output:\n", "\n", "`0.01$`"]}, {"cell_type": "code", "execution_count": null, "id": "73", "metadata": {}, "outputs": [], "source": ["# Download data\n", "data = client.timeseries.get_range(\n", "    dataset=dataset,\n", "    symbols=[symbol],\n", "    stype_in=stype_in,\n", "    schema=schema,\n", "    start=start,\n", "    end=end,\n", ")\n", "\n", "# Export data in DBNStore format (CSV data are 10x bigger)\n", "data.to_file(f\"{dataset}_{symbol}_{start}-{end}.{schema}.dbn.zst\")"]}, {"cell_type": "code", "execution_count": null, "id": "74", "metadata": {}, "outputs": [], "source": ["# Cleanup and view data as DataFrame\n", "df = (\n", "    data.to_df()\n", "    .reset_index()\n", "    .rename(columns={\"ts_event\": \"datetime\"})\n", "    .drop(columns=[\"rtype\", \"publisher_id\", \"instrument_id\"])\n", "\n", "    # Nice order of columns\n", "    .reindex(columns=[\"symbol\", \"datetime\", \"open\", \"high\", \"low\", \"close\", \"volume\"])\n", "\n", "    # Localize datetime to Bratislava\n", "    .assign(datetime = lambda df: pd.to_datetime(df[\"datetime\"], utc=True))  # Mark as UTC datetime\n", "    .assign(datetime = lambda df: df[\"datetime\"].dt.tz_convert(pytz.timezone(\"Europe/Bratislava\")))  # Convert to Bratislava timezone\n", "\n", "    # Add 1-minute, so datetime represents closing time of the bar (not opening time)\n", "    .assign(datetime = lambda df: df[\"datetime\"] + <PERSON><PERSON><PERSON>(minutes=1))\n", ")\n", "\n", "# Preview\n", "print(len(df))\n", "df.head(3)"]}, {"cell_type": "markdown", "id": "75", "metadata": {}, "source": ["Example output: *(not real data, just example of output format)*\n", "\n", "`2734`\n", "\n", "| symbol | datetime | open | high | low | close | volume |\n", "|:--|:--|:--|:--|:--|:--|:--|\n", "| 6E.v.0 | 2025-01-02 00:01:00+01:00 | 1.03890 | 1.03930 | 1.03845 | 1.03905 | 291 |\n", "| 6E.v.0 | 2025-01-02 00:02:00+01:00 | 1.03900 | 1.03900 | 1.03870 | 1.03880 | 311 |\n", "| 6E.v.0 | 2025-01-02 00:03:00+01:00 | 1.03880 | 1.03890 | 1.03870 | 1.03885 | 140 |\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.8"}}, "nbformat": 4, "nbformat_minor": 5}