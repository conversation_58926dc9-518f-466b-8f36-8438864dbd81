# -------------------------------------------------------------------------------------------------
#  Copyright (C) 2015-2023 Nautech Systems Pty Ltd. All rights reserved.
#  https://nautechsystems.io
#
#  Licensed under the GNU Lesser General Public License Version 3.0 (the "License");
#  You may not use this file except in compliance with the License.
#  You may obtain a copy of the License at https://www.gnu.org/licenses/lgpl-3.0.en.html
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
# -------------------------------------------------------------------------------------------------
"""
Provides Hyperliquid market data HTTP API client.

This module implements the Info endpoint functionality for retrieving market data,
user information, and historical data from the Hyperliquid API.
"""

from typing import Any

from nautilus_trader.adapters.hyperliquid.http.client import HyperliquidHttpClient
from nautilus_trader.core.nautilus_pyo3 import HttpMethod


class HyperliquidMarketHttpApi:
    """
    Hyperliquid market data HTTP API client.

    This class provides methods for accessing the Hyperliquid Info endpoint,
    which handles read-only operations for market data and user information.
    """

    def __init__(self, client: HyperliquidHttpClient) -> None:
        """
        Initialize the market data API client.

        Parameters
        ----------
        client : HyperliquidHttpClient
            The HTTP client for making requests.
        """
        self._client = client

    # Market Data Methods

    async def get_all_mids(self, dex: str = "") -> dict[str, str]:
        """
        Retrieve all mid prices for actively traded coins.

        Parameters
        ----------
        dex : str, default ""
            The DEX to query (empty string for default).

        Returns
        -------
        dict[str, str]
            Dictionary mapping coin names to mid prices.
        """
        payload = {"type": "allMids", "dex": dex}
        return await self._client.post("/info", payload)

    async def get_l2_book(
        self,
        coin: str,
        n_sig_figs: int | None = None,
        mantissa: int | None = None,
    ) -> list[list[dict[str, Any]]]:
        """
        Retrieve L2 order book snapshot for a coin.

        Parameters
        ----------
        coin : str
            The coin to retrieve order book for.
        n_sig_figs : int | None
            Number of significant figures for aggregation (2, 3, 4, 5, or None).
        mantissa : int | None
            Mantissa value (1, 2, or 5) when n_sig_figs is 5.

        Returns
        -------
        list[list[dict[str, Any]]]
            Order book data with bids and asks.
        """
        payload = {"type": "l2Book", "coin": coin}
        if n_sig_figs is not None:
            payload["nSigFigs"] = n_sig_figs
        if mantissa is not None:
            payload["mantissa"] = mantissa

        return await self._client.post("/info", payload)

    async def get_candles_snapshot(
        self,
        coin: str,
        interval: str,
        start_time: int,
        end_time: int,
    ) -> list[dict[str, Any]]:
        """
        Retrieve candles snapshot for a coin.

        Parameters
        ----------
        coin : str
            The coin to retrieve candles for.
        interval : str
            The candlestick interval (e.g., "1m", "5m", "15m", "1h", "1d").
        start_time : int
            Start time in milliseconds.
        end_time : int
            End time in milliseconds.

        Returns
        -------
        list[dict[str, Any]]
            List of candle data.
        """
        req = {
            "coin": coin,
            "interval": interval,
            "startTime": start_time,
            "endTime": end_time,
        }
        payload = {"type": "candleSnapshot", "req": req}
        return await self._client.post("/info", payload)

    async def get_meta(self, dex: str = "") -> dict[str, Any]:
        """
        Retrieve exchange perpetual metadata.

        Parameters
        ----------
        dex : str, default ""
            The DEX to query (empty string for default).

        Returns
        -------
        dict[str, Any]
            Exchange metadata including universe information.
        """
        payload = {"type": "meta", "dex": dex}
        return await self._client.post("/info", payload)

    async def get_meta_and_asset_ctxs(self) -> list[Any]:
        """
        Retrieve exchange metadata and asset contexts.

        Returns
        -------
        list[Any]
            Metadata and asset context information.
        """
        payload = {"type": "metaAndAssetCtxs"}
        return await self._client.post("/info", payload)

    async def get_spot_meta(self) -> dict[str, Any]:
        """
        Retrieve exchange spot metadata.

        Returns
        -------
        dict[str, Any]
            Spot exchange metadata including tokens and universe.
        """
        payload = {"type": "spotMeta"}
        return await self._client.post("/info", payload)

    async def get_spot_meta_and_asset_ctxs(self) -> list[Any]:
        """
        Retrieve exchange spot metadata and asset contexts.

        Returns
        -------
        list[Any]
            Spot metadata and asset context information.
        """
        payload = {"type": "spotMetaAndAssetCtxs"}
        return await self._client.post("/info", payload)

    async def get_funding_history(
        self,
        coin: str,
        start_time: int,
        end_time: int | None = None,
    ) -> list[dict[str, Any]]:
        """
        Retrieve funding history for a coin.

        Parameters
        ----------
        coin : str
            The coin to retrieve funding history for.
        start_time : int
            Start time in milliseconds.
        end_time : int | None
            End time in milliseconds (defaults to current time).

        Returns
        -------
        list[dict[str, Any]]
            List of funding history records.
        """
        payload = {"type": "fundingHistory", "coin": coin, "startTime": start_time}
        if end_time is not None:
            payload["endTime"] = end_time

        return await self._client.post("/info", payload)

    # User Data Methods (require authentication)

    async def get_user_state(self, user: str, dex: str = "") -> dict[str, Any]:
        """
        Retrieve trading details about a user.

        Parameters
        ----------
        user : str
            User address in 42-character hexadecimal format.
        dex : str, default ""
            The DEX to query (empty string for default).

        Returns
        -------
        dict[str, Any]
            User's trading state including positions and margin summary.
        """
        payload = {"type": "clearinghouseState", "user": user, "dex": dex}
        return await self._client.post("/info", payload)

    async def get_spot_user_state(self, user: str) -> dict[str, Any]:
        """
        Retrieve spot trading state for a user.

        Parameters
        ----------
        user : str
            User address in 42-character hexadecimal format.

        Returns
        -------
        dict[str, Any]
            User's spot trading state.
        """
        payload = {"type": "spotClearinghouseState", "user": user}
        return await self._client.post("/info", payload)

    async def get_open_orders(self, user: str, dex: str = "") -> list[dict[str, Any]]:
        """
        Retrieve a user's open orders.

        Parameters
        ----------
        user : str
            User address in 42-character hexadecimal format.
        dex : str, default ""
            The DEX to query (empty string for default).

        Returns
        -------
        list[dict[str, Any]]
            List of open orders.
        """
        payload = {"type": "openOrders", "user": user, "dex": dex}
        return await self._client.post("/info", payload)

    async def get_frontend_open_orders(self, user: str, dex: str = "") -> list[dict[str, Any]]:
        """
        Retrieve a user's open orders with additional frontend information.

        Parameters
        ----------
        user : str
            User address in 42-character hexadecimal format.
        dex : str, default ""
            The DEX to query (empty string for default).

        Returns
        -------
        list[dict[str, Any]]
            List of open orders with frontend details.
        """
        payload = {"type": "frontendOpenOrders", "user": user, "dex": dex}
        return await self._client.post("/info", payload)

    async def get_user_fills(self, user: str, aggregate_by_time: bool = False) -> list[dict[str, Any]]:
        """
        Retrieve a user's fills.

        Parameters
        ----------
        user : str
            User address in 42-character hexadecimal format.
        aggregate_by_time : bool, default False
            Whether to aggregate partial fills by time.

        Returns
        -------
        list[dict[str, Any]]
            List of user fills.
        """
        payload = {"type": "userFills", "user": user, "aggregateByTime": aggregate_by_time}
        return await self._client.post("/info", payload)

    async def get_user_fills_by_time(
        self,
        user: str,
        start_time: int,
        end_time: int | None = None,
        aggregate_by_time: bool = False,
    ) -> list[dict[str, Any]]:
        """
        Retrieve a user's fills by time range.

        Parameters
        ----------
        user : str
            User address in 42-character hexadecimal format.
        start_time : int
            Start time in milliseconds.
        end_time : int | None
            End time in milliseconds (defaults to current time).
        aggregate_by_time : bool, default False
            Whether to aggregate partial fills by time.

        Returns
        -------
        list[dict[str, Any]]
            List of user fills in the time range.
        """
        payload = {
            "type": "userFillsByTime",
            "user": user,
            "startTime": start_time,
            "aggregateByTime": aggregate_by_time,
        }
        if end_time is not None:
            payload["endTime"] = end_time

        return await self._client.post("/info", payload)

    async def get_order_status(self, user: str, oid: int) -> dict[str, Any]:
        """
        Query the status of an order.

        Parameters
        ----------
        user : str
            User address in 42-character hexadecimal format.
        oid : int
            Order ID or client order ID.

        Returns
        -------
        dict[str, Any]
            Order status information.
        """
        payload = {"type": "orderStatus", "user": user, "oid": oid}
        return await self._client.post("/info", payload)

    async def get_sub_accounts(self, user: str) -> list[dict[str, Any]]:
        """
        Retrieve user's sub-accounts.

        Parameters
        ----------
        user : str
            User address in 42-character hexadecimal format.

        Returns
        -------
        list[dict[str, Any]]
            List of sub-accounts with their details.
        """
        payload = {"type": "subAccounts", "user": user}
        return await self._client.post("/info", payload)

    async def get_portfolio(self, user: str) -> list[Any]:
        """
        Query user's portfolio information.

        Parameters
        ----------
        user : str
            User address in 42-character hexadecimal format.

        Returns
        -------
        list[Any]
            Portfolio data including account value and PnL history.
        """
        payload = {"type": "portfolio", "user": user}
        return await self._client.post("/info", payload)