# Hyperliquid WebSocket Client

This directory contains the Hyperliquid WebSocket client implementation for the Nautilus trader framework.

## Overview

The Hyperliquid WebSocket client provides real-time streaming data and supports all Hyperliquid WebSocket features including:

- **Market Data Subscriptions**: Trades, order books, candlesticks, best bid/offer, all mid prices
- **User Data Subscriptions**: User events, order updates, fills, funding fees, notifications
- **POST Requests**: Send information and action requests through WebSocket
- **Automatic Reconnection**: Exponential backoff reconnection strategy
- **Rate Limiting**: Configurable subscription rate limiting
- **Heartbeat Management**: Automatic ping/pong to maintain connection

## Files

- `client_new.py` - New WebSocket client implementation following Nautilus patterns
- `client.py` - Original WebSocket client (legacy, contains Chinese comments)
- `types.py` - WebSocket message type definitions using msgspec
- `example_usage.py` - Example usage demonstrating client features
- `__init__.py` - Module exports

## Key Features

### 1. Nautilus Trader Integration

The client follows Nautilus trader patterns:
- Uses `LiveClock` for timing
- Uses `Logger` for logging with color support
- Uses `WebSocketClient` from `nautilus_pyo3` for core WebSocket functionality
- Follows PEP8 style guidelines
- Uses modern Python type hints

### 2. Subscription Management

```python
# Subscribe to trades
await client.subscribe_trades("BTC")

# Subscribe to order book
await client.subscribe_order_book("ETH", n_sig_figs=5)

# Subscribe to candlesticks
await client.subscribe_candles("SOL", "1m")

# Bulk subscribe
subscriptions = [
    {"type": "trades", "coin": "BTC"},
    {"type": "l2Book", "coin": "BTC", "nSigFigs": 5, "mantissa": None},
]
await client.bulk_subscribe(subscriptions)
```

### 3. User Data Subscriptions

```python
# Subscribe to user events
await client.subscribe_user_events(user_address)

# Subscribe to order updates
await client.subscribe_order_updates(user_address)

# Subscribe to user fills
await client.subscribe_user_fills(user_address, aggregate_by_time=False)
```

### 4. WebSocket POST Requests

```python
# Get order book snapshot
orderbook = await client.get_l2_orderbook("BTC", n_sig_figs=5)

# Get recent trades
trades = await client.get_recent_trades("ETH")

# Send action request (requires signature)
response = await client.post_action_request(
    action=order_action,
    nonce=nonce,
    signature=signature,
    vault_address=vault_address
)
```

### 5. Order Management via WebSocket

```python
# Place an order
response = await client.place_order_via_ws(
    action=order_action,
    nonce=nonce,
    signature=signature,
    vault_address=vault_address
)

# Cancel an order by order ID
response = await client.cancel_order_via_ws(
    action=cancel_action,
    nonce=nonce,
    signature=signature,
    vault_address=vault_address
)

# Cancel an order by client order ID
response = await client.cancel_order_by_cloid_via_ws(
    action=cancel_by_cloid_action,
    nonce=nonce,
    signature=signature,
    vault_address=vault_address
)

# Modify an order
response = await client.modify_order_via_ws(
    action=modify_action,
    nonce=nonce,
    signature=signature,
    vault_address=vault_address
)

# Batch modify orders
response = await client.batch_modify_orders_via_ws(
    action=batch_modify_action,
    nonce=nonce,
    signature=signature,
    vault_address=vault_address
)

# Schedule cancel (dead man's switch)
response = await client.schedule_cancel_via_ws(
    action=schedule_cancel_action,
    nonce=nonce,
    signature=signature,
    vault_address=vault_address
)

# Update leverage
response = await client.update_leverage_via_ws(
    action=update_leverage_action,
    nonce=nonce,
    signature=signature,
    vault_address=vault_address
)
```

### 6. Connection Management

```python
# Connect
await client.connect()

# Check connection status
status = await client.get_connection_status()

# Disconnect
await client.disconnect()
```

## Configuration

### Constructor Parameters

- `clock`: LiveClock instance for timing
- `handler`: Callback function for message events
- `handler_reconnect`: Optional callback for reconnection events
- `testnet`: Whether to use testnet (default: False)
- `base_url`: Custom WebSocket URL (optional)
- `loop`: Event loop (optional, uses current loop if None)
- `subscription_rate_limit_per_second`: Rate limit for subscriptions (default: 5)
- `ping_interval`: Heartbeat interval in seconds (default: 50)

### Network Settings

- **Mainnet**: `wss://api.hyperliquid.xyz/ws`
- **Testnet**: `wss://api.hyperliquid-testnet.xyz/ws`
- **Timeout**: 60 seconds (Hyperliquid closes connections without messages)

## Error Handling

The client implements robust error handling:

- **Connection Errors**: Automatic reconnection with exponential backoff
- **Message Errors**: Logged but don't interrupt the connection
- **Rate Limiting**: Built-in rate limiting to avoid overwhelming the server
- **Timeout Handling**: POST requests have configurable timeouts

## Supported Subscription Types

### Market Data
- `trades` - Trade data
- `l2Book` - Order book data
- `candle` - Candlestick data
- `bbo` - Best bid/offer
- `allMids` - All mid prices
- `activeAssetCtx` - Active asset context

### User Data
- `userEvents` - User events
- `orderUpdates` - Order updates
- `userFills` - User fills
- `userFundings` - User funding fees
- `userNonFundingLedgerUpdates` - Non-funding ledger updates
- `notification` - Notifications
- `webData2` - User aggregated information
- `activeAssetData` - Active asset data (perpetuals only)
- `userTwapSliceFills` - User TWAP slice fills
- `userTwapHistory` - User TWAP history

## Message Types

The `types.py` file defines msgspec structures for all WebSocket messages:

- `HyperliquidWsMessage` - Base message structure
- `HyperliquidWsTradeData` - Trade data
- `HyperliquidWsBookData` - Order book data
- `HyperliquidWsCandleData` - Candlestick data
- `HyperliquidWsUserFillsData` - User fills data
- And many more...

## Example Usage

See `example_usage.py` for a complete example demonstrating:
- Connection establishment
- Market data subscriptions
- WebSocket POST requests
- Bulk subscriptions
- Error handling
- Graceful disconnection

## Best Practices

1. **Rate Limiting**: Don't exceed the subscription rate limit
2. **Error Handling**: Always handle connection errors gracefully
3. **Resource Cleanup**: Always disconnect when done
4. **Message Handling**: Process messages efficiently to avoid blocking
5. **Reconnection**: Use the built-in reconnection handler for reliability

## Integration with Nautilus

This WebSocket client is designed to integrate seamlessly with Nautilus trader:

- Use in data clients for real-time market data
- Use in execution clients for order updates and fills
- Follows Nautilus logging and error handling patterns
- Compatible with Nautilus event loop management
