# Trading

```{eval-rst}
.. automodule:: nautilus_trader.trading
```

```{eval-rst}
.. automodule:: nautilus_trader.trading.controller
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

```{eval-rst}
.. automodule:: nautilus_trader.trading.filters
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

```{eval-rst}
.. automodule:: nautilus_trader.trading.strategy
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

```{eval-rst}
.. automodule:: nautilus_trader.trading.trader
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```
