# Binance

```{eval-rst}
.. automodule:: nautilus_trader.adapters.binance
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

## Config

```{eval-rst}
.. automodule:: nautilus_trader.adapters.binance.config
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

## Factories

```{eval-rst}
.. automodule:: nautilus_trader.adapters.binance.factories
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

## Enums

```{eval-rst}
.. automodule:: nautilus_trader.adapters.binance.common.enums
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

## Types

```{eval-rst}
.. automodule:: nautilus_trader.adapters.binance.common.types
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

## Futures

### Data

```{eval-rst}
.. automodule:: nautilus_trader.adapters.binance.futures.data
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

### Enums

```{eval-rst}
.. automodule:: nautilus_trader.adapters.binance.futures.enums
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

### Execution

```{eval-rst}
.. automodule:: nautilus_trader.adapters.binance.futures.execution
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

### Providers

```{eval-rst}
.. automodule:: nautilus_trader.adapters.binance.futures.providers
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

### Types

```{eval-rst}
.. automodule:: nautilus_trader.adapters.binance.futures.types
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

## Spot

### Data

```{eval-rst}
.. automodule:: nautilus_trader.adapters.binance.spot.data
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

### Enums

```{eval-rst}
.. automodule:: nautilus_trader.adapters.binance.spot.enums
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

### Execution

```{eval-rst}
.. automodule:: nautilus_trader.adapters.binance.spot.execution
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

### Providers

```{eval-rst}
.. automodule:: nautilus_trader.adapters.binance.spot.providers
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```
