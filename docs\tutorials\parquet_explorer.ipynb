{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["# Parquet Explorer\n", "\n", "This tutorial explores some basic query operations on Parquet files written by Nautilus. We'll utilize both the `datafusio`n and `pyarrow` libraries.\n", "\n", "Before proceeding, ensure that you have `datafusion` installed. If not, you can install it by running:\n", "```bash\n", "pip install datafusion\n", "```"]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["import datafusion\n", "import pyarrow.parquet as pq"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["trade_tick_path = \"../../tests/test_data/nautilus/trades.parquet\"\n", "bar_path = \"../../tests/test_data/nautilus/bars.parquet\""]}, {"cell_type": "code", "execution_count": null, "id": "3", "metadata": {}, "outputs": [], "source": ["# Create a context\n", "ctx = datafusion.SessionContext()"]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [], "source": ["# Run this cell once (otherwise will error)\n", "ctx.register_parquet(\"trade_0\", trade_tick_path)\n", "ctx.register_parquet(\"bar_0\", bar_path)"]}, {"cell_type": "markdown", "id": "5", "metadata": {}, "source": ["### TradeTick data"]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [], "source": ["query = \"SELECT * FROM trade_0 ORDER BY ts_init\"\n", "df = ctx.sql(query)"]}, {"cell_type": "code", "execution_count": null, "id": "7", "metadata": {}, "outputs": [], "source": ["df.schema()"]}, {"cell_type": "code", "execution_count": null, "id": "8", "metadata": {}, "outputs": [], "source": ["df"]}, {"cell_type": "code", "execution_count": null, "id": "9", "metadata": {}, "outputs": [], "source": ["table = pq.read_table(trade_tick_path)"]}, {"cell_type": "code", "execution_count": null, "id": "10", "metadata": {}, "outputs": [], "source": ["table.schema"]}, {"cell_type": "markdown", "id": "11", "metadata": {}, "source": ["### Bar data"]}, {"cell_type": "code", "execution_count": null, "id": "12", "metadata": {}, "outputs": [], "source": ["query = \"SELECT * FROM bar_0 ORDER BY ts_init\"\n", "df = ctx.sql(query)"]}, {"cell_type": "code", "execution_count": null, "id": "13", "metadata": {}, "outputs": [], "source": ["df.schema()"]}, {"cell_type": "code", "execution_count": null, "id": "14", "metadata": {}, "outputs": [], "source": ["df"]}, {"cell_type": "code", "execution_count": null, "id": "15", "metadata": {}, "outputs": [], "source": ["table = pq.read_table(bar_path)\n", "table.schema"]}, {"cell_type": "code", "execution_count": null, "id": "16", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 5}