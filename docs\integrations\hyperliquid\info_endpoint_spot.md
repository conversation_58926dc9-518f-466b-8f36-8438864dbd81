# HyperLiquid API 现货交易Info端点

本文档详细介绍了HyperLiquid API中专门用于现货交易的Info端点功能。这些端点用于获取现货交易的元数据、资产上下文、用户代币余额和代币详情等信息。

## 基本信息

- **端点URL**: `https://api.hyperliquid.xyz/info`
- **请求方法**: `POST`
- **内容类型**: `application/json`

## 获取现货元数据

获取现货交易的元数据，包括可交易的代币、小数位数和交易对等信息。

### 请求

```json
{
  "type": "spotMeta"
}
```

### 响应

```json
{
  "tokens": [
    {
      "name": "USDC",
      "szDecimals": 8,
      "weiDecimals": 8,
      "index": 0,
      "tokenId": "0x6d1e7cde53ba9467b783cb7c530ce054",
      "isCanonical": true,
      "evmContract": null,
      "fullName": null
    },
    {
      "name": "PURR",
      "szDecimals": 0,
      "weiDecimals": 5,
      "index": 1,
      "tokenId": "0xc1fb593aeffbeb02f85e0308e9956a90",
      "isCanonical": true,
      "evmContract": null,
      "fullName": null
    },
    {
      "name": "HFUN",
      "szDecimals": 2,
      "weiDecimals": 8,
      "index": 2,
      "tokenId": "0xbaf265ef389da684513d98d68edf4eae",
      "isCanonical": false,
      "evmContract": null,
      "fullName": null
    }
  ],
  "universe": [
    {
      "name": "PURR/USDC",
      "tokens": [1, 0],
      "index": 0,
      "isCanonical": true
    },
    {
      "tokens": [2, 0],
      "name": "@1",
      "index": 1,
      "isCanonical": false
    }
  ]
}
```

## 获取现货资产上下文

获取现货交易的资产上下文，包括标记价格、中间价和前一天价格等信息。

### 请求

```json
{
  "type": "spotMetaAndAssetCtxs"
}
```

### 响应

```json
[
  {
    "tokens": [
      {
        "name": "USDC",
        "szDecimals": 8,
        "weiDecimals": 8,
        "index": 0,
        "tokenId": "0x6d1e7cde53ba9467b783cb7c530ce054",
        "isCanonical": true,
        "evmContract": null,
        "fullName": null
      },
      {
        "name": "PURR",
        "szDecimals": 0,
        "weiDecimals": 5,
        "index": 1,
        "tokenId": "0xc1fb593aeffbeb02f85e0308e9956a90",
        "isCanonical": true,
        "evmContract": null,
        "fullName": null
      }
    ],
    "universe": [
      {
        "name": "PURR/USDC",
        "tokens": [1, 0],
        "index": 0,
        "isCanonical": true
      }
    ]
  },
  [
    {
      "dayNtlVlm": "8906.0",
      "markPx": "0.14",
      "midPx": "0.209265",
      "prevDayPx": "0.20432"
    }
  ]
]
```

## 获取用户的代币余额

获取用户的代币余额信息。

### 请求

```json
{
  "type": "spotClearinghouseState",
  "user": "0x0000000000000000000000000000000000000000"
}
```

### 响应

```json
{
  "balances": [
    {
      "coin": "USDC",
      "token": 0,
      "hold": "0.0",
      "total": "14.625485",
      "entryNtl": "0.0"
    },
    {
      "coin": "PURR",
      "token": 1,
      "hold": "0",
      "total": "2000",
      "entryNtl": "1234.56"
    }
  ]
}
```

## 获取现货部署拍卖信息

获取关于现货部署拍卖的信息。

### 请求

```json
{
  "type": "spotDeployState",
  "user": "0x0000000000000000000000000000000000000000"
}
```

### 响应

```json
{
  "states": [
    {
      "token": 150,
      "spec": {
        "name": "HYPE",
        "szDecimals": 2,
        "weiDecimals": 8
      },
      "fullName": "Hyperliquid",
      "spots": [107],
      "maxSupply": 1000000000,
      "hyperliquidityGenesisBalance": "120000",
      "totalGenesisBalanceWei": "100000000000000000",
      "userGenesisBalances": [
        ["0xdddddddddddddddddddddddddddddddddddddddd", "428,062,211"]
        // 更多用户...
      ],
      "existingTokenGenesisBalances": [
        [1, "0"]
        // 更多代币...
      ]
    }
  ],
  "gasAuction": {
    "startTimeSeconds": 1733929200,
    "durationSeconds": 111600,
    "startGas": "181305.90046",
    "currentGas": null,
    "endGas": "181291.247358"
  }
}
```

## 获取代币详情

获取特定代币的详细信息。

### 请求

```json
{
  "type": "tokenDetails",
  "tokenId": "0x00000000000000000000000000000000"
}
```

### 响应

```json
{
  "name": "TEST",
  "maxSupply": "1852229076.12716007",
  "totalSupply": "851681534.05516005",
  "circulatingSupply": "851681534.05516005",
  "szDecimals": 0,
  "weiDecimals": 5,
  "midPx": "3.2049",
  "markPx": "3.2025",
  "prevDayPx": "3.2025",
  "genesis": {
    "userBalances": [
      [
        "0x0000000000000000000000000000000000000001",
        "1000000000.0"
      ],
      [
        "0xffffffffffffffffffffffffffffffffffffffff",
        "1000000000.0"
      ]
    ],
    "existingTokenBalances": []
  },
  "deployer": "0x0000000000000000000000000000000000000001",
  "deployGas": "100.0",
  "deployTime": "2024-06-05T10:50:59.434",
  "seededUsdc": "0.0",
  "nonCirculatingUserBalances": [],
  "futureEmissions": "0.0"
}
```

## 字段说明

### 代币字段

- **name**: 代币名称
- **szDecimals**: 交易数量的小数位数
- **weiDecimals**: 代币内部表示的小数位数
- **index**: 代币在系统中的索引
- **tokenId**: 代币的唯一标识符
- **isCanonical**: 是否为规范代币
- **evmContract**: EVM合约地址（如果有）
- **fullName**: 代币的全名（如果有）

### 交易对字段

- **name**: 交易对名称
- **tokens**: 交易对中的代币索引数组，[基础代币索引, 报价代币索引]
- **index**: 交易对在系统中的索引
- **isCanonical**: 是否为规范交易对

### 资产上下文字段

- **dayNtlVlm**: 24小时名义交易量
- **markPx**: 标记价格
- **midPx**: 中间价格（买卖价的平均值）
- **prevDayPx**: 前一天的收盘价

### 余额字段

- **coin**: 代币名称
- **token**: 代币索引
- **hold**: 冻结的数量（用于未成交订单）
- **total**: 总余额
- **entryNtl**: 入场名义价值

## 最佳实践

1. **缓存元数据**：元数据不经常变化，可以缓存以减少API调用。
2. **处理小数位数**：注意不同代币的szDecimals和weiDecimals可能不同，确保正确处理小数位数。
3. **监控余额**：定期获取用户余额，以确保有足够的资金进行交易。
4. **验证交易对**：在下单前验证交易对是否存在和活跃。
5. **处理错误**：实现适当的错误处理机制，以应对API可能返回的各种错误。
