# HyperLiquid API WebSocket

HyperLiquid提供WebSocket端点，用于实时数据流和作为HTTP请求发送的替代方案。本文档详细介绍了WebSocket的连接方法、订阅类型和数据格式。

## 连接信息

WebSocket URL根据网络不同而不同：

- **主网**：`wss://api.hyperliquid.xyz/ws`
- **测试网**：`wss://api.hyperliquid-testnet.xyz/ws`

## 连接示例

使用命令行连接WebSocket：

```bash
$ wscat -c wss://api.hyperliquid.xyz/ws
Connected (press CTRL+C to quit)
> { "method": "subscribe", "subscription": { "type": "trades", "coin": "SOL" } }
< {"channel":"subscriptionResponse","data":{"method":"subscribe","subscription":{"type":"trades","coin":"SOL"}}}
```

## 订阅消息

要订阅特定的数据流，需要发送订阅消息。订阅消息格式如下：

```json
{
  "method": "subscribe",
  "subscription": { ... }
}
```

订阅确认会提供时间序列数据（例如用户成交）的先前数据快照。这些快照消息标记为`isSnapshot: true`，如果之前的消息已经处理过，可以忽略。

## 订阅类型

订阅对象包含您想要订阅的特定数据流的详细信息。以下是可用的订阅类型：

1. **allMids**：所有中间价格
   - 订阅消息：`{ "type": "allMids", "dex": "<dex>" }`
   - 数据格式：`AllMids`
   - `dex`字段表示获取中间价格的永续合约交易所
   - 注意：`dex`字段是可选的，如果不提供，则使用第一个永续合约交易所

2. **notification**：通知消息
   - 订阅消息：`{ "type": "notification", "user": "<address>" }`
   - 数据格式：`Notification`

3. **webData2**：用户聚合信息
   - 订阅消息：`{ "type": "webData2", "user": "<address>" }`
   - 数据格式：`WebData2`

4. **candle**：K线数据
   - 订阅消息：`{ "type": "candle", "coin": "<coin_symbol>", "interval": "<candle_interval>" }`
   - 数据格式：`Candle[]`

5. **l2Book**：订单簿数据
   - 订阅消息：`{ "type": "l2Book", "coin": "<coin_symbol>" }`
   - 可选参数：nSigFigs: int, mantissa: int
   - 数据格式：`WsBook`

6. **trades**：交易数据
   - 订阅消息：`{ "type": "trades", "coin": "<coin_symbol>" }`
   - 数据格式：`WsTrade[]`

7. **orderUpdates**：订单更新
   - 订阅消息：`{ "type": "orderUpdates", "user": "<address>" }`
   - 数据格式：`WsOrder[]`

8. **userEvents**：用户事件
   - 订阅消息：`{ "type": "userEvents", "user": "<address>" }`
   - 数据格式：`WsUserEvent`

9. **userFills**：用户成交
   - 订阅消息：`{ "type": "userFills", "user": "<address>" }`
   - 可选参数：`aggregateByTime: bool`
   - 数据格式：`WsUserFills`

10. **userFundings**：用户资金费用
    - 订阅消息：`{ "type": "userFundings", "user": "<address>" }`
    - 数据格式：`WsUserFundings`

11. **userNonFundingLedgerUpdates**：用户非资金费用账本更新
    - 订阅消息：`{ "type": "userNonFundingLedgerUpdates", "user": "<address>" }`
    - 数据格式：`WsUserNonFundingLedgerUpdates`

12. **activeAssetCtx**：活跃资产上下文
    - 订阅消息：`{ "type": "activeAssetCtx", "coin": "coin_symbol>" }`
    - 数据格式：`WsActiveAssetCtx`或`WsActiveSpotAssetCtx`

13. **activeAssetData**：活跃资产数据（仅支持永续合约）
    - 订阅消息：`{ "type": "activeAssetData", "user": "<address>", "coin": "coin_symbol>" }`
    - 数据格式：`WsActiveAssetData`

14. **userTwapSliceFills**：用户TWAP切片成交
    - 订阅消息：`{ "type": "userTwapSliceFills", "user": "<address>" }`
    - 数据格式：`WsUserTwapSliceFills`

15. **userTwapHistory**：用户TWAP历史
    - 订阅消息：`{ "type": "userTwapHistory", "user": "<address>" }`
    - 数据格式：`WsUserTwapHistory`

16. **bbo**：最佳买卖价
    - 订阅消息：`{ "type": "bbo", "coin": "<coin>" }`
    - 数据格式：`WsBbo`

## 数据格式

服务器将对成功的订阅响应一条消息，其中`channel`属性设置为`"subscriptionResponse"`，以及提供原始订阅的`data`字段。然后，服务器将开始发送消息，其中`channel`属性设置为相应的订阅类型，例如`"allMids"`，而`data`字段提供订阅的数据。

以下是一些主要数据类型的定义：

```typescript
// 交易数据
interface WsTrade {
  coin: string;
  side: string;
  px: string;
  sz: string;
  hash: string;
  time: number;
  // tid是(buyer_oid, seller_oid)的50位哈希
  // 对于全局唯一的交易ID，使用(block_time, coin, tid)
  tid: number;
  users: [string, string] // [买方, 卖方]
}

// 订单簿快照
interface WsBook {
  coin: string;
  levels: [Array<WsLevel>, Array<WsLevel>];
  time: number;
}

// 最佳买卖价
interface WsBbo {
  coin: string;
  time: number;
  bbo: [WsLevel | null, WsLevel | null];
}

// 价格级别
interface WsLevel {
  px: string; // 价格
  sz: string; // 数量
  n: number; // 订单数量
}

// 用户成交
interface WsUserFills {
  isSnapshot?: boolean;
  user: string;
  fills: Array<WsFill>;
}

// 成交详情
interface WsFill {
  coin: string;
  px: string; // 价格
  sz: string; // 数量
  side: string;
  time: number;
  startPosition: string;
  dir: string; // 用于前端显示
  closedPnl: string;
  hash: string; // L1交易哈希
  oid: number; // 订单ID
  crossed: boolean; // 订单是否跨越点差（是否为主动方）
  fee: string; // 负数表示返利
  tid: number; // 唯一交易ID
  liquidation?: FillLiquidation;
  feeToken: string; // 支付费用的代币
  builderFee?: string; // 支付给构建者的金额，也包含在fee中
}
```

## 取消订阅

要取消订阅特定的数据流，需要发送取消订阅消息，格式如下：

```json
{
  "method": "unsubscribe",
  "subscription": { ... }
}
```

`subscription`对象应与订阅时发送的原始订阅消息匹配。这允许服务器识别您想要取消订阅的特定数据流。

## 最佳实践

1. **使用适当的订阅类型**：根据您的需求选择合适的订阅类型，避免订阅不必要的数据。

2. **处理断连**：实现重连逻辑，以处理可能的WebSocket连接断开。

3. **处理快照**：对于带有`isSnapshot: true`的消息，确保正确处理这些初始数据快照。

4. **限制订阅数量**：避免订阅过多的数据流，这可能会导致性能问题。

5. **实现心跳机制**：定期发送心跳消息，以保持连接活跃。

## 示例代码

以下是一个简单的Python示例，展示如何连接到WebSocket并订阅交易数据：

```python
import json
import websocket

def on_message(ws, message):
    data = json.loads(message)
    print(f"Received: {data}")

def on_error(ws, error):
    print(f"Error: {error}")

def on_close(ws, close_status_code, close_msg):
    print("Connection closed")

def on_open(ws):
    print("Connection opened")
    # 订阅BTC交易数据
    subscription = {
        "method": "subscribe",
        "subscription": {
            "type": "trades",
            "coin": "BTC"
        }
    }
    ws.send(json.dumps(subscription))

if __name__ == "__main__":
    # 连接到主网WebSocket
    ws = websocket.WebSocketApp("wss://api.hyperliquid.xyz/ws",
                              on_open=on_open,
                              on_message=on_message,
                              on_error=on_error,
                              on_close=on_close)
    ws.run_forever()
```

## 总结

HyperLiquid的WebSocket API提供了丰富的实时数据流，可用于构建高性能的交易应用程序。通过正确使用订阅和取消订阅机制，您可以有效地管理数据流，并确保您的应用程序获取所需的实时信息。
