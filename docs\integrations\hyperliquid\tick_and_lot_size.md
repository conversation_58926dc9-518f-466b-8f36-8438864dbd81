# HyperLiquid API 价格精度和数量精度

在HyperLiquid交易所中，价格(Price/px)和数量(Size/sz)都有最大可接受的小数位数限制。本文档详细说明了这些精度规则。

## 基本规则

### 价格精度(Tick Size)

价格可以有最多5个有效数字，但小数位数不能超过`MAX_DECIMALS - szDecimals`，其中：
- 永续合约的`MAX_DECIMALS`为6
- 现货交易的`MAX_DECIMALS`为8

**特殊情况**：整数价格始终被允许，无论有多少有效数字。例如，`123456.0`是有效价格，即使`12345.6`不是。

### 数量精度(Lot Size)

数量会被四舍五入到资产的`szDecimals`位小数。例如，如果`szDecimals = 3`，那么`1.001`是有效数量，但`1.0001`不是。

## 获取精度信息

每个资产的`szDecimals`可以在info端点的meta响应中找到：

```
GET https://api.hyperliquid.xyz/info
```

响应中包含每个资产的`szDecimals`值，用于确定该资产的数量精度。

## 永续合约价格示例

以下是永续合约价格精度的一些示例：

1. `1234.5` 是有效的，但 `1234.56` 不是（有效数字过多）

2. `0.001234` 是有效的，但 `0.0012345` 不是（小数位数超过6位）

3. 如果 `szDecimals = 1`，那么 `0.01234` 是有效的，但 `0.012345` 不是（小数位数超过`6 - szDecimals`位）

## 现货价格示例

以下是现货交易价格精度的一些示例：

1. 如果 `szDecimals` 是0或1，那么 `0.0001234` 是有效的

2. 如果 `szDecimals` 大于2，那么 `0.0001234` 不是有效的（小数位数超过`8 - szDecimals`位）

## 价格精度计算公式

### 永续合约

最大小数位数 = 6 - szDecimals

### 现货交易

最大小数位数 = 8 - szDecimals

## 最佳实践

1. **动态获取精度信息**：在交易前通过API获取最新的精度信息，而不是硬编码。

2. **价格舍入**：在提交订单前，确保价格已经按照正确的精度进行了舍入。

3. **数量舍入**：同样，确保交易数量已经按照资产的`szDecimals`进行了舍入。

4. **验证有效数字**：确保价格不超过5个有效数字（除非是整数）。

5. **错误处理**：实现适当的错误处理机制，以应对因精度不正确而被拒绝的订单。

## 示例代码

以下是一个简单的JavaScript函数，用于根据资产的`szDecimals`值格式化价格和数量：

```javascript
function formatPrice(price, szDecimals, isPerp = true) {
    // 计算最大小数位数
    const maxDecimals = isPerp ? 6 - szDecimals : 8 - szDecimals;
    
    // 检查是否为整数
    if (Number.isInteger(Number(price))) {
        return price.toString();
    }
    
    // 检查有效数字
    const parts = price.toString().split('.');
    if (parts[0].length + parts[1].length > 5 && parts[0] !== '0') {
        throw new Error('Price has too many significant figures');
    }
    
    // 格式化价格
    return Number(price).toFixed(Math.min(maxDecimals, parts[1].length));
}

function formatSize(size, szDecimals) {
    return Number(size).toFixed(szDecimals);
}
```

## 总结

理解并正确应用HyperLiquid的价格和数量精度规则对于成功交易至关重要。通过遵循本文档中的指导原则，开发者可以确保其交易请求符合交易所的要求，从而避免因精度问题导致的订单拒绝。
