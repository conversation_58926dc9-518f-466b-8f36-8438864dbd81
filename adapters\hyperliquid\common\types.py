# -------------------------------------------------------------------------------------------------
#  Copyright (C) 2015-2025 Nautech Systems Pty Ltd. All rights reserved.
#  https://nautechsystems.io
#
#  Licensed under the GNU Lesser General Public License Version 3.0 (the "License");
#  You may not use this file except in compliance with the License.
#  You may obtain a copy of the License at https://www.gnu.org/licenses/lgpl-3.0.en.html
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
# -------------------------------------------------------------------------------------------------

import msgspec


# WebSocket message schemas for Hyperliquid

class HyperliquidWsMessage(msgspec.Struct, frozen=True):
    """
    Base WebSocket message structure for Hyperliquid.
    """
    channel: str
    data: msgspec.Raw | None = None


class HyperliquidWsSubscription(msgspec.Struct, frozen=True):
    """
    WebSocket subscription message structure.
    """
    method: str
    subscription: msgspec.Raw


class HyperliquidWsUnsubscription(msgspec.Struct, frozen=True):
    """
    WebSocket unsubscription message structure.
    """
    method: str
    subscription: msgspec.Raw


class HyperliquidWsPostRequest(msgspec.Struct, frozen=True):
    """
    WebSocket POST request message structure.
    """
    method: str
    id: int
    request: msgspec.Raw


class HyperliquidWsPing(msgspec.Struct, frozen=True):
    """
    WebSocket ping message structure.
    """
    method: str = "ping"


class HyperliquidWsPong(msgspec.Struct, frozen=True):
    """
    WebSocket pong response structure.
    """
    channel: str = "pong"


# Trade data structures

class HyperliquidWsTrade(msgspec.Struct, frozen=True):
    """
    WebSocket trade data structure.
    """
    coin: str
    side: str
    px: str
    sz: str
    hash: str
    time: int
    tid: int
    users: list[str]  # [buyer, seller]


class HyperliquidWsTradeData(msgspec.Struct, frozen=True):
    """
    WebSocket trade channel data structure.
    """
    channel: str = "trades"
    data: list[HyperliquidWsTrade]


# Order book data structures

class HyperliquidWsLevel(msgspec.Struct, frozen=True):
    """
    Order book price level structure.
    """
    px: str  # Price
    sz: str  # Size
    n: int   # Number of orders


class HyperliquidWsBook(msgspec.Struct, frozen=True):
    """
    WebSocket order book data structure.
    """
    coin: str
    levels: list[list[HyperliquidWsLevel]]  # [bids, asks]
    time: int


class HyperliquidWsBookData(msgspec.Struct, frozen=True):
    """
    WebSocket order book channel data structure.
    """
    channel: str = "l2Book"
    data: HyperliquidWsBook


# Candlestick data structures

class HyperliquidWsCandle(msgspec.Struct, frozen=True):
    """
    WebSocket candlestick data structure.
    """
    t: int    # Timestamp
    T: int    # Close timestamp
    s: str    # Symbol
    i: str    # Interval
    o: str    # Open price
    c: str    # Close price
    h: str    # High price
    l: str    # Low price
    v: str    # Volume
    n: int    # Number of trades


class HyperliquidWsCandleData(msgspec.Struct, frozen=True):
    """
    WebSocket candlestick channel data structure.
    """
    channel: str = "candle"
    data: list[HyperliquidWsCandle]


# Best bid/offer data structures

class HyperliquidWsBbo(msgspec.Struct, frozen=True):
    """
    WebSocket best bid/offer data structure.
    """
    coin: str
    bid: str
    ask: str
    bidSz: str
    askSz: str
    time: int


class HyperliquidWsBboData(msgspec.Struct, frozen=True):
    """
    WebSocket BBO channel data structure.
    """
    channel: str = "bbo"
    data: HyperliquidWsBbo


# User data structures

class HyperliquidWsFill(msgspec.Struct, frozen=True):
    """
    WebSocket user fill data structure.
    """
    coin: str
    px: str
    sz: str
    side: str
    time: int
    startPosition: str
    dir: str
    closedPnl: str
    hash: str
    oid: int
    crossed: bool
    fee: str
    tid: int


class HyperliquidWsUserFills(msgspec.Struct, frozen=True):
    """
    WebSocket user fills data structure.
    """
    isSnapshot: bool | None = None
    user: str
    fills: list[HyperliquidWsFill]


class HyperliquidWsUserFillsData(msgspec.Struct, frozen=True):
    """
    WebSocket user fills channel data structure.
    """
    channel: str = "userFills"
    data: HyperliquidWsUserFills


class HyperliquidWsOrderUpdate(msgspec.Struct, frozen=True):
    """
    WebSocket order update data structure.
    """
    order: msgspec.Raw
    status: str
    statusTimestamp: int


class HyperliquidWsOrderUpdatesData(msgspec.Struct, frozen=True):
    """
    WebSocket order updates channel data structure.
    """
    channel: str = "orderUpdates"
    data: list[HyperliquidWsOrderUpdate]


# All mids data structures

class HyperliquidWsAllMids(msgspec.Struct, frozen=True):
    """
    WebSocket all mids data structure.
    """
    mids: dict[str, str]  # coin -> mid price


class HyperliquidWsAllMidsData(msgspec.Struct, frozen=True):
    """
    WebSocket all mids channel data structure.
    """
    channel: str = "allMids"
    data: HyperliquidWsAllMids


# Subscription response

class HyperliquidWsSubscriptionResponse(msgspec.Struct, frozen=True):
    """
    WebSocket subscription response structure.
    """
    channel: str = "subscriptionResponse"
    data: msgspec.Raw


# POST response

class HyperliquidWsPostResponse(msgspec.Struct, frozen=True):
    """
    WebSocket POST response structure.
    """
    channel: str = "post"
    data: msgspec.Raw


# Error response

class HyperliquidWsError(msgspec.Struct, frozen=True):
    """
    WebSocket error response structure.
    """
    channel: str = "error"
    data: msgspec.Raw


# Union type for all possible WebSocket messages
HyperliquidWsMessageUnion = (
    HyperliquidWsTradeData |
    HyperliquidWsBookData |
    HyperliquidWsCandleData |
    HyperliquidWsBboData |
    HyperliquidWsUserFillsData |
    HyperliquidWsOrderUpdatesData |
    HyperliquidWsAllMidsData |
    HyperliquidWsSubscriptionResponse |
    HyperliquidWsPostResponse |
    HyperliquidWsError |
    HyperliquidWsPong
)
