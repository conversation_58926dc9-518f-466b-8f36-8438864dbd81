# HyperLiquid API Info端点

Info端点是HyperLiquid API的核心组件，用于获取交易所和特定用户的信息。不同的请求体会产生不同的响应体模式。本文档详细介绍了Info端点的使用方法和主要功能。

## 基本信息

- **端点URL**: `https://api.hyperliquid.xyz/info`
- **请求方法**: `POST`
- **内容类型**: `application/json`

## 分页

对于接受时间范围的响应，每次最多返回500个元素或不同的数据块。要查询更大的范围，请使用最后返回的时间戳作为下一个`startTime`进行分页。

## 永续合约与现货

本节中的端点以及WebSocket订阅适用于永续合约和现货交易。

- **永续合约**：`coin`是`meta`响应中返回的名称。
- **现货**：`coin`应为`PURR/USDC`（对于PURR）或`@{index}`（对于其他现货代币），其中index是`spotMeta`响应的`universe`字段中现货对的索引。例如，主网上HYPE的现货索引是`@107`，因为HYPE的代币索引是150，现货对`@107`的代币是`[150, 0]`。

## 用户地址

要查询主账户或子账户的账户数据，必须传入该账户的实际地址。一个常见的错误是使用代理钱包的地址，这会导致空结果。

## 主要功能

### 1. 获取所有币种的中间价

```json
// 请求
{
  "type": "allMids",
  "dex": "" // 可选，默认为第一个永续合约交易所
}

// 响应
{
  "APE": "4.33245",
  "ARB": "1.21695"
}
```

### 2. 获取用户的未平仓订单

```json
// 请求
{
  "type": "openOrders",
  "user": "******************************************",
  "dex": "" // 可选
}

// 响应
[
  {
    "coin": "BTC",
    "limitPx": "29792.0",
    "oid": 91490942,
    "side": "A",
    "sz": "0.0",
    "timestamp": 1681247412573
  }
]
```

### 3. 获取用户的成交记录

```json
// 请求
{
  "type": "userFills",
  "user": "******************************************",
  "aggregateByTime": false // 可选，当为true时，部分成交会被合并
}

// 响应
[
  {
    "closedPnl": "0.0",
    "coin": "AVAX",
    "crossed": false,
    "dir": "Open Long",
    "hash": "0xa166e3fa63c25663024b03f2e0da011a00307e4017465df020210d3d432e7cb8",
    "oid": 90542681,
    "px": "18.435",
    "side": "B",
    "startPosition": "26.86",
    "sz": "93.53",
    "time": 1681222254710,
    "fee": "0.01",
    "feeToken": "USDC",
    "builderFee": "0.01", // 可选，如果为0则不存在
    "tid": 118906512037719
  }
]
```

### 4. 按时间获取用户的成交记录

```json
// 请求
{
  "type": "userFillsByTime",
  "user": "******************************************",
  "startTime": 1681222254000, // 开始时间（毫秒），包含
  "endTime": 1681222354000, // 结束时间（毫秒），包含，默认为当前时间
  "aggregateByTime": false // 可选
}
```

### 5. 查询订单状态

```json
// 请求
{
  "type": "orderStatus",
  "user": "******************************************",
  "oid": 91490942 // 订单ID或客户端订单ID
}

// 响应
{
  "status": "order",
  "order": {
    "order": {
      "coin": "ETH",
      "side": "A",
      "limitPx": "2412.7",
      "sz": "0.0",
      "oid": 1,
      "timestamp": 1724361546645,
      "triggerCondition": "N/A",
      "isTrigger": false,
      "triggerPx": "0.0",
      "children": [],
      "isPositionTpsl": false,
      "reduceOnly": true,
      "orderType": "Market",
      "origSz": "0.0076",
      "tif": "FrontendMarket",
      "cloid": null
    },
    "status": "filled", // 可能的值: open, filled, canceled, triggered, rejected, marginCanceled等
    "statusTimestamp": 1724361546645
  }
}
```

### 6. 获取L2订单簿快照

```json
// 请求
{
  "type": "l2Book",
  "coin": "BTC",
  "nSigFigs": null, // 可选，聚合级别的有效数字，有效值为2、3、4、5和null
  "mantissa": null // 可选，仅当nSigFigs为5时允许，接受值为1、2或5
}

// 响应
[
  [ // 买单
    {
      "px": "19900",
      "sz": "1",
      "n": 1 // 组成该价格级别的不同订单数量
    },
    // 更多买单...
  ],
  [ // 卖单
    {
      "px": "20100",
      "sz": "1",
      "n": 1
    },
    // 更多卖单...
  ]
]
```

### 7. 获取K线数据快照

```json
// 请求
{
  "type": "candleSnapshot",
  "req": {
    "coin": "BTC",
    "interval": "15m",
    "startTime": *************,
    "endTime": *************
  }
}

// 响应
[
  {
    "T": *************, // 结束时间
    "c": "29258.0", // 收盘价
    "h": "29309.0", // 最高价
    "i": "15m", // 间隔
    "l": "29250.0", // 最低价
    "n": 189, // 交易数量
    "o": "29295.0", // 开盘价
    "s": "BTC", // 币种
    "t": *************, // 开始时间
    "v": "0.98639" // 交易量
  }
]
```

### 8. 获取用户的子账户

```json
// 请求
{
  "type": "subAccounts",
  "user": "******************************************"
}

// 响应
[
  {
    "name": "Test",
    "subAccountUser": "******************************************",
    "master": "******************************************",
    "clearinghouseState": {
      "marginSummary": {
        "accountValue": "29.78001",
        "totalNtlPos": "0.0",
        "totalRawUsd": "29.78001",
        "totalMarginUsed": "0.0"
      },
      // 更多清算所状态...
    },
    "spotState": {
      "balances": [
        {
          "coin": "USDC",
          "token": 0,
          "total": "0.22",
          "hold": "0.0",
          "entryNtl": "0.0"
        }
      ]
    }
  }
]
```

### 9. 查询用户的投资组合

```json
// 请求
{
  "type": "portfolio",
  "user": "******************************************"
}

// 响应
[
  [
    "day",
    {
      "accountValueHistory": [
        [*************, "0.0"],
        [*************, "0.0"]
        // 更多历史记录...
      ],
      "pnlHistory": [
        [*************, "0.0"],
        [*************, "0.0"]
        // 更多历史记录...
      ],
      "vlm": "0.0"
    }
  ],
  ["week", { /* ... */ }],
  ["month", { /* ... */ }],
  ["allTime", { /* ... */ }],
  ["perpDay", { /* ... */ }],
  ["perpWeek", { /* ... */ }],
  ["perpMonth", { /* ... */ }],
  ["perpAllTime", { /* ... */ }]
]
```

## 其他功能

Info端点还提供以下功能：

1. **查询用户费率限制**：`"type": "userRateLimit"`
2. **检查构建者费用批准**：`"type": "maxBuilderFee"`
3. **获取用户的历史订单**：`"type": "historicalOrders"`
4. **获取用户的TWAP切片成交**：`"type": "userTwapSliceFills"`
5. **获取保险库详情**：`"type": "vaultDetails"`
6. **获取用户的保险库存款**：`"type": "userVaultEquities"`
7. **查询用户角色**：`"type": "userRole"`
8. **查询用户的推荐信息**：`"type": "referral"`
9. **查询用户的质押委托**：`"type": "delegations"`
10. **查询用户的质押摘要**：`"type": "delegatorSummary"`
11. **查询用户的质押历史**：`"type": "delegatorHistory"`
12. **查询用户的质押奖励**：`"type": "delegatorRewards"`

## 最佳实践

1. **使用分页**：对于大型数据集，实现分页以避免超时和数据丢失。
2. **正确使用地址**：确保使用账户的实际地址，而不是代理钱包地址。
3. **处理错误**：实现适当的错误处理机制，以应对API可能返回的各种错误。
4. **缓存常用数据**：缓存不经常变化的数据，如币种元数据，以减少API调用。
5. **遵守速率限制**：监控和遵守API的速率限制，以避免被限制访问。
