{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["# Databento data catalog\n", "\n", "Tutorial for [NautilusTrader](https://nautilustrader.io/docs/) a high-performance algorithmic trading platform and event driven backtester.\n", "\n", "[View source on GitHub](https://github.com/nautechsystems/nautilus_trader/blob/develop/docs/tutorials/databento_data_catalog.ipynb).\n", "\n", ":::info\n", "We are currently working on this tutorial.\n", ":::"]}, {"cell_type": "markdown", "id": "1", "metadata": {}, "source": ["## Overview\n", "\n", "This tutorial will walk through how to set up a Nautilus Parquet data catalog with various Databento schemas."]}, {"cell_type": "markdown", "id": "2", "metadata": {}, "source": ["## Prerequisites\n", "\n", "- Python 3.11+ installed\n", "- [JupyterLab](https://jupyter.org/) or similar installed (`pip install -U jupyterlab`)\n", "- [NautilusTrader](https://pypi.org/project/nautilus_trader/) latest release installed (`pip install -U nautilus_trader`)\n", "- [databento](https://pypi.org/project/databento/) Python client library installed to make data requests (`pip install -U databento`)\n", "- [Databento](https://databento.com) account"]}, {"cell_type": "markdown", "id": "3", "metadata": {}, "source": ["## Requesting data"]}, {"cell_type": "markdown", "id": "4", "metadata": {}, "source": ["We'll use a Databento historical client for the rest of this tutorial. You can either initialize one by passing your Databento API key to the constructor, or implicitly use the `DATABENTO_API_KEY` environment variable (as shown)."]}, {"cell_type": "code", "execution_count": null, "id": "5", "metadata": {}, "outputs": [], "source": ["import databento as db\n", "\n", "\n", "client = db.Historical()  # This will use the DATABENTO_API_KEY environment variable (recommended best practice)"]}, {"cell_type": "markdown", "id": "6", "metadata": {}, "source": ["**It's important to note that every historical streaming request from `timeseries.get_range` will incur a cost (even for the same data), therefore we need to**:\n", "- Know and understand the cost prior to making a request\n", "- Not make requests for the same data more than once (not efficient)\n", "- Persist the responses to disk by writing zstd compressed DBN files (so that we don't have to request again)"]}, {"cell_type": "markdown", "id": "7", "metadata": {}, "source": ["We can use a metadata [get_cost endpoint](https://databento.com/docs/api-reference-historical/metadata/metadata-get-cost?historical=python&live=python) from the Databento API to get a quote on the cost, prior to each request.\n", "Each request sequence will first request the cost of the data, and then make a request only if the data doesn't already exist on disk.\n", "\n", "Note the response returned is in USD, displayed as fractional cents."]}, {"cell_type": "markdown", "id": "8", "metadata": {}, "source": ["The following request is only for a small amount of data (as used in this Medium article [Building high-frequency trading signals in Python with Databento and sklearn](https://databento.com/blog/hft-sklearn-python)), just to demonstrate the basic workflow. "]}, {"cell_type": "code", "execution_count": null, "id": "9", "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "\n", "from databento import DBNStore"]}, {"cell_type": "markdown", "id": "10", "metadata": {}, "source": ["We'll prepare a directory for the raw Databento DBN format data, which we'll use for the rest of the tutorial."]}, {"cell_type": "code", "execution_count": null, "id": "11", "metadata": {}, "outputs": [], "source": ["DATABENTO_DATA_DIR = Path(\"databento\")\n", "DATABENTO_DATA_DIR.mkdir(exist_ok=True)"]}, {"cell_type": "code", "execution_count": null, "id": "12", "metadata": {}, "outputs": [], "source": ["# Request cost quote (USD) - this endpoint is 'free'\n", "client.metadata.get_cost(\n", "    dataset=\"GLBX.MDP3\",\n", "    symbols=[\"ES.n.0\"],\n", "    stype_in=\"continuous\",\n", "    schema=\"mbp-10\",\n", "    start=\"2023-12-06T14:30:00\",\n", "    end=\"2023-12-06T20:30:00\",\n", ")"]}, {"cell_type": "markdown", "id": "13", "metadata": {}, "source": ["Use the historical API to request for the data used in the Medium article."]}, {"cell_type": "code", "execution_count": null, "id": "14", "metadata": {}, "outputs": [], "source": ["path = DATABENTO_DATA_DIR / \"es-front-glbx-mbp10.dbn.zst\"\n", "\n", "if not path.exists():\n", "    # Request data\n", "    client.timeseries.get_range(\n", "        dataset=\"GLBX.MDP3\",\n", "        symbols=[\"ES.n.0\"],\n", "        stype_in=\"continuous\",\n", "        schema=\"mbp-10\",\n", "        start=\"2023-12-06T14:30:00\",\n", "        end=\"2023-12-06T20:30:00\",\n", "        path=path,  # <-- Passing a `path` parameter will ensure the data is written to disk\n", "    )"]}, {"cell_type": "markdown", "id": "15", "metadata": {}, "source": ["Inspect the data by reading from disk and convert to a pandas.DataFrame"]}, {"cell_type": "code", "execution_count": null, "id": "16", "metadata": {}, "outputs": [], "source": ["data = DBNStore.from_file(path)\n", "\n", "df = data.to_df()\n", "df"]}, {"cell_type": "markdown", "id": "17", "metadata": {}, "source": ["## Write to data catalog"]}, {"cell_type": "code", "execution_count": null, "id": "18", "metadata": {}, "outputs": [], "source": ["import shutil\n", "from pathlib import Path\n", "\n", "from nautilus_trader.adapters.databento.loaders import DatabentoDataLoader\n", "from nautilus_trader.model import InstrumentId\n", "from nautilus_trader.persistence.catalog import ParquetDataCatalog"]}, {"cell_type": "code", "execution_count": null, "id": "19", "metadata": {}, "outputs": [], "source": ["CATALOG_PATH = Path.cwd() / \"catalog\"\n", "\n", "# Clear if it already exists\n", "if CATALOG_PATH.exists():\n", "    shutil.rmtree(CATALOG_PATH)\n", "CATALOG_PATH.mkdir()\n", "\n", "# Create a catalog instance\n", "catalog = ParquetDataCatalog(CATALOG_PATH)"]}, {"cell_type": "markdown", "id": "20", "metadata": {}, "source": ["Now that we've prepared the data catalog, we need a `DatabentoDataLoader` which we'll use to decode and load the data into Nautilus objects."]}, {"cell_type": "code", "execution_count": null, "id": "21", "metadata": {}, "outputs": [], "source": ["loader = DatabentoDataLoader()"]}, {"cell_type": "markdown", "id": "22", "metadata": {}, "source": ["Next, we'll load Rust pyo3 objects to write to the catalog (we could use legacy Cython objects, but this is slightly more efficient) by setting `as_legacy_cython=False`.\n", "\n", "We also pass an `instrument_id`, which is not required but makes data loading faster as symbology mapping is not required."]}, {"cell_type": "code", "execution_count": null, "id": "23", "metadata": {}, "outputs": [], "source": ["path = DATABENTO_DATA_DIR / \"es-front-glbx-mbp10.dbn.zst\"\n", "instrument_id = InstrumentId.from_str(\"ES.n.0\")  # This should be the raw symbol (update)\n", "\n", "depth10 = loader.from_dbn_file(\n", "    path=path,\n", "    instrument_id=instrument_id,\n", "    as_legacy_cython=False,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "24", "metadata": {}, "outputs": [], "source": ["# Write data to catalog (this takes ~20 seconds or ~250,000/second for writing MBP-10 at the moment)\n", "catalog.write_data(depth10)"]}, {"cell_type": "code", "execution_count": null, "id": "25", "metadata": {}, "outputs": [], "source": ["# Test reading from catalog\n", "depths = catalog.order_book_depth10()\n", "len(depths)"]}, {"cell_type": "markdown", "id": "26", "metadata": {}, "source": ["## Preparing a month of AAPL trades"]}, {"cell_type": "markdown", "id": "27", "metadata": {}, "source": ["Now we'll expand on this workflow by preparing a month of AAPL trades on the Nasdaq exchange using the Databento `trade` schema, which will translate to Nautilus `TradeTick` objects."]}, {"cell_type": "code", "execution_count": null, "id": "28", "metadata": {}, "outputs": [], "source": ["# Request cost quote (USD) - this endpoint is 'free'\n", "client.metadata.get_cost(\n", "    dataset=\"XNAS.ITCH\",\n", "    symbols=[\"AAPL\"],\n", "    schema=\"trades\",\n", "    start=\"2024-01\",\n", ")"]}, {"cell_type": "markdown", "id": "29", "metadata": {}, "source": ["When requesting historical data with the Databento `Historical` data client, ensure you pass a `path` parameter to write the data to disk."]}, {"cell_type": "code", "execution_count": null, "id": "30", "metadata": {}, "outputs": [], "source": ["path = DATABENTO_DATA_DIR / \"aapl-xnas-202401.trades.dbn.zst\"\n", "\n", "if not path.exists():\n", "    # Request data\n", "    client.timeseries.get_range(\n", "        dataset=\"XNAS.ITCH\",\n", "        symbols=[\"AAPL\"],\n", "        schema=\"trades\",\n", "        start=\"2024-01\",\n", "        path=path,  # <-- Passing a `path` parameter\n", "    )"]}, {"cell_type": "markdown", "id": "31", "metadata": {}, "source": ["Inspect the data by reading from disk and convert to a pandas.DataFrame"]}, {"cell_type": "code", "execution_count": null, "id": "32", "metadata": {}, "outputs": [], "source": ["data = DBNStore.from_file(path)\n", "\n", "df = data.to_df()\n", "df"]}, {"cell_type": "markdown", "id": "33", "metadata": {}, "source": ["We'll use an `InstrumentId` of `\"AAPL.XNAS\"`, where XNAS is the ISO 10383 MIC (Market Identifier Code) for the Nasdaq venue.\n", "\n", "While passing an `instrument_id` to the loader isn't strictly necessary, it speeds up data loading by eliminating the need for symbology mapping. Additionally, setting the `as_legacy_cython` option to False further optimizes the process since we'll be writing the loaded data to the catalog. Although we could use legacy Cython objects, this method is more efficient for loading."]}, {"cell_type": "code", "execution_count": null, "id": "34", "metadata": {}, "outputs": [], "source": ["instrument_id = InstrumentId.from_str(\"AAPL.XNAS\")\n", "\n", "trades = loader.from_dbn_file(\n", "    path=path,\n", "    instrument_id=instrument_id,\n", "    as_legacy_cython=False,\n", ")"]}, {"cell_type": "markdown", "id": "35", "metadata": {}, "source": ["Here we'll organize our data as a file per month, this is an arbitrary choice as a file per day could be just as valid.\n", "\n", "It may also be a good idea to create a function which can return the correct `basename_template` value for a given chunk of data."]}, {"cell_type": "code", "execution_count": null, "id": "36", "metadata": {}, "outputs": [], "source": ["# Write data to catalog\n", "catalog.write_data(trades, basename_template=\"2024-01\")"]}, {"cell_type": "code", "execution_count": null, "id": "37", "metadata": {}, "outputs": [], "source": ["trades = catalog.trade_ticks([instrument_id])"]}, {"cell_type": "code", "execution_count": null, "id": "38", "metadata": {}, "outputs": [], "source": ["len(trades)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 5}