# Common

```{eval-rst}
.. automodule:: nautilus_trader.common
```

```{eval-rst}
.. automodule:: nautilus_trader.common.actor
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

```{eval-rst}
.. automodule:: nautilus_trader.common.factories
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

## Component

```{eval-rst}
.. automodule:: nautilus_trader.common.component
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

## Executor

```{eval-rst}
.. automodule:: nautilus_trader.common.executor
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

## Generators

```{eval-rst}
.. automodule:: nautilus_trader.common.generators
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

```{eval-rst}
.. automodule:: nautilus_trader.common.providers
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```
