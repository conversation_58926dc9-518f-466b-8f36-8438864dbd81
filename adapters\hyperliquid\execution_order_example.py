"""
Example of how to use the Hyperliquid signing functionality.

This demonstrates both the direct sign_order method and the enum-based approach
for order signing in the execution client.
"""

from typing import Any, Dict, Optional, Union

from nautilus_trader.model.enums import OrderSide, OrderType, TimeInForce
from nautilus_trader.common.component import LiveClock

from adapters.hyperliquid.common.enums import HyperliquidEnumParser, HyperliquidTimeInForce, HyperliquidTriggerType
from adapters.hyperliquid.util.signing import HyperliquidSigner, float_to_wire


class HyperliquidOrderSigner:
    """
    Example class showing how to properly handle order signing with enum conversion.

    This would typically be part of the execution client.
    """

    def __init__(self, private_key: str, clock: LiveClock, testnet: bool = False):
        self._signer = HyperliquidSigner(
            clock=clock,
            private_key_hex=private_key,
            is_mainnet=not testnet
        )
        self._enum_parser = HyperliquidEnumParser()

    def sign_order(
        self,
        asset_id: int,
        order_side: OrderSide,
        price: Union[str, float],
        size: Union[str, float],
        order_type: OrderType = OrderType.LIMIT,
        time_in_force: TimeInForce = TimeInForce.GTC,
        reduce_only: bool = False,
        client_id: Optional[str] = None,
        trigger_price: Optional[Union[str, float]] = None,
        vault_address: Optional[str] = None,
        nonce: Optional[int] = None,
        expires_after: Optional[int] = None,
    ) -> Dict[str, Any]:
        """
        Sign an order using proper enum conversion.

        Args:
            asset_id: The asset ID
            order_side: Nautilus OrderSide enum
            price: Order price
            size: Order size
            order_type: Nautilus OrderType enum
            time_in_force: Nautilus TimeInForce enum
            reduce_only: Whether this is reduce-only
            client_id: Optional client order ID
            trigger_price: Optional trigger price for stop/take-profit orders
            vault_address: Optional vault address
            nonce: Optional nonce (will use timestamp if None)
            expires_after: Optional expiration time

        Returns:
            Signed order data ready for API submission
        """
        # Validate enums are supported
        if not self._enum_parser.validate_order_type(order_type):
            raise ValueError(f"Unsupported order type: {order_type}")
        if not self._enum_parser.validate_time_in_force(time_in_force):
            raise ValueError(f"Unsupported time in force: {time_in_force}")

        # Convert Nautilus enums to Hyperliquid format
        hl_order_side = self._enum_parser.parse_nautilus_order_side(order_side)
        hl_order_type = self._enum_parser.parse_nautilus_order_type(order_type)
        hl_time_in_force = self._enum_parser.parse_nautilus_time_in_force(time_in_force)

        # Convert to wire format
        price_str = float_to_wire(price)
        size_str = float_to_wire(size)

        # Build order wire format
        order_wire = {
            "a": asset_id,
            "b": hl_order_side.value == "B",  # Convert to boolean
            "p": price_str,
            "s": size_str,
            "r": reduce_only,
        }

        # Add order type specific fields
        if hl_order_type.value == "limit":
            order_wire["t"] = {"limit": {"tif": hl_time_in_force.value}}
        elif hl_order_type.value == "market":
            order_wire["t"] = {"market": {}}
        elif hl_order_type.value in ["stopMarket", "stopLimit", "takeProfitMarket", "takeProfitLimit"]:
            if trigger_price is None:
                raise ValueError(f"trigger_price is required for {hl_order_type.value} orders")
            trigger_price_str = float_to_wire(trigger_price)

            if hl_order_type.value == "stopMarket":
                order_wire["t"] = {"stopMarket": {"trigger": trigger_price_str}}
            elif hl_order_type.value == "stopLimit":
                order_wire["t"] = {"stopLimit": {"trigger": trigger_price_str, "tif": hl_time_in_force.value}}
            elif hl_order_type.value == "takeProfitMarket":
                order_wire["t"] = {"takeProfitMarket": {"trigger": trigger_price_str}}
            elif hl_order_type.value == "takeProfitLimit":
                order_wire["t"] = {"takeProfitLimit": {"trigger": trigger_price_str, "tif": hl_time_in_force.value}}

        # Add client ID if provided
        if client_id:
            order_wire["c"] = client_id

        # Build action
        action = {
            "type": "order",
            "orders": [order_wire],
            "grouping": "na",
        }

        # Sign the action
        return self._signer.sign_l1_action(
            action=action,
            vault_address=vault_address,
            nonce=nonce,
            expires_after=expires_after,
        )

    def sign_order_direct(
        self,
        asset_id: int,
        order_side: OrderSide,
        price: Union[str, float],
        size: Union[str, float],
        order_type: OrderType = OrderType.LIMIT,
        time_in_force: TimeInForce = TimeInForce.GTC,
        reduce_only: bool = False,
        client_id: Optional[str] = None,
        trigger_price: Optional[Union[str, float]] = None,
        vault_address: Optional[str] = None,
        nonce: Optional[int] = None,
        expires_after: Optional[int] = None,
    ) -> Dict[str, Any]:
        """
        Sign an order using the direct sign_order method (simpler approach).

        This method uses the new sign_order function that accepts dict parameters,
        similar to the official SDK pattern.

        Args:
            asset_id: The asset ID
            order_side: Nautilus OrderSide enum
            price: Order price
            size: Order size
            order_type: Nautilus OrderType enum
            time_in_force: Nautilus TimeInForce enum
            reduce_only: Whether this is reduce-only
            client_id: Optional client order ID
            trigger_price: Optional trigger price for stop/take-profit orders
            vault_address: Optional vault address
            nonce: Optional nonce (will use timestamp if None)
            expires_after: Optional expiration time

        Returns:
            Signed order data ready for API submission
        """
        # Validate enums are supported
        if not self._enum_parser.validate_order_type(order_type):
            raise ValueError(f"Unsupported order type: {order_type}")
        if not self._enum_parser.validate_time_in_force(time_in_force):
            raise ValueError(f"Unsupported time in force: {time_in_force}")

        # Convert Nautilus enums to Hyperliquid format
        is_buy = self._enum_parser.parse_nautilus_order_side(order_side)
        hl_time_in_force = self._enum_parser.parse_nautilus_time_in_force(time_in_force)

        # Build order type structure based on order type
        if order_type == OrderType.LIMIT:
            order_type_dict = {"limit": {"tif": hl_time_in_force.value}}
        elif order_type in [OrderType.STOP_MARKET, OrderType.STOP_LIMIT,
                           OrderType.MARKET_IF_TOUCHED, OrderType.LIMIT_IF_TOUCHED]:
            if trigger_price is None:
                raise ValueError(f"trigger_price is required for {order_type}")

            # Determine trigger type and market flag
            is_market = order_type in [OrderType.STOP_MARKET, OrderType.MARKET_IF_TOUCHED]
            tpsl = "sl" if order_type in [OrderType.STOP_MARKET, OrderType.STOP_LIMIT] else "tp"

            order_type_dict = {
                "trigger": {
                    "triggerPx": float(trigger_price),
                    "isMarket": is_market,
                    "tpsl": tpsl
                }
            }
        else:
            raise ValueError(f"Unsupported order type: {order_type}")

        # Build order parameters dict
        order_params = {
            "asset": asset_id,
            "is_buy": is_buy,
            "sz": float(size),
            "limit_px": float(price),
            "order_type": order_type_dict,
            "reduce_only": reduce_only,
        }

        # Add optional parameters
        if client_id:
            order_params["cloid"] = client_id
        if vault_address:
            order_params["vault_address"] = vault_address
        if nonce:
            order_params["nonce"] = nonce
        if expires_after:
            order_params["expires_after"] = expires_after

        # Use the direct sign_order method
        return self._signer.sign_order(order_params)


# Example usage
if __name__ == "__main__":
    import json

    # Initialize components
    clock = LiveClock()
    private_key = "0xe908f86dbb4d55ac876378565aafeabc187f6690f046459397b17d9b9a19688e"

    # Create order signer
    order_signer = HyperliquidOrderSigner(
        private_key=private_key,
        clock=clock,
        testnet=True
    )

    print("=== Method 1: Using enum-based approach (sign_order) ===")

    # Example: Sign a limit buy order
    try:
        signed_order = order_signer.sign_order(
            asset_id=0,  # BTC
            order_side=OrderSide.BUY,
            price="30000.0",
            size="0.001",
            order_type=OrderType.LIMIT,
            time_in_force=TimeInForce.GTC,
            client_id="example_order_001"
        )

        print("✅ Successfully signed limit order (enum-based):")
        print(f"   Signature keys: {list(signed_order.keys())}")

    except Exception as e:
        print(f"❌ Error signing order: {e}")

    print("\n=== Method 2: Using direct sign_order method ===")

    # Example: Sign a limit buy order using direct method
    try:
        signed_order_direct = order_signer.sign_order_direct(
            asset_id=0,  # BTC
            order_side=OrderSide.BUY,
            price="30000.0",
            size="0.001",
            order_type=OrderType.LIMIT,
            time_in_force=TimeInForce.GTC,
            client_id="example_direct_001"
        )

        print("✅ Successfully signed limit order (direct method):")
        print(f"   Signature keys: {list(signed_order_direct.keys())}")

    except Exception as e:
        print(f"❌ Error signing order: {e}")

    # Example: Sign a stop market order using direct method
    try:
        signed_stop_order = order_signer.sign_order_direct(
            asset_id=0,  # BTC
            order_side=OrderSide.SELL,
            price="29000.0",  # This will be ignored for market orders
            size="0.001",
            order_type=OrderType.STOP_MARKET,
            trigger_price="29500.0",
            client_id="example_stop_001"
        )

        print("✅ Successfully signed stop market order (direct method):")
        print(f"   Signature keys: {list(signed_stop_order.keys())}")

    except Exception as e:
        print(f"❌ Error signing stop order: {e}")

    print("\n=== Method 3: Using raw dict parameters (most flexible) ===")

    # Example: Sign using raw dict (like official SDK)
    try:
        raw_order_params = {
            "asset": 0,
            "is_buy": True,
            "sz": 0.001,
            "limit_px": 30000.0,
            "order_type": {"limit": {"tif": "Gtc"}},
            "reduce_only": False,
            "cloid": "raw_order_001"
        }

        signed_raw_order = order_signer._signer.sign_order(raw_order_params)
        print("✅ Successfully signed order (raw dict method):")
        print(f"   Signature keys: {list(signed_raw_order.keys())}")

    except Exception as e:
        print(f"❌ Error signing raw order: {e}")

    # Example: Test enum validation
    try:
        # This should fail - GTD is not supported by Hyperliquid
        order_signer.sign_order_direct(
            asset_id=0,
            order_side=OrderSide.BUY,
            price="30000.0",
            size="0.001",
            time_in_force=TimeInForce.GTD  # Not supported
        )
    except ValueError as e:
        print(f"\n⚠️  Expected validation error: {e}")
