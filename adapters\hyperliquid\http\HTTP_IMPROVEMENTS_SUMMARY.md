# Hyperliquid HTTP Implementation Improvements

## 问题分析与解决方案

### 1. HyperliquidSigner参数问题

**问题**: HTTP client中HyperliquidSigner只使用了两个参数(`private_key_hex`和`clock`)，但Signer实际支持更多重要参数。

**解决方案**: 
```python
# 修改前
self._signer = HyperliquidSigner(private_key_hex=private_key, clock=self._clock)

# 修改后  
self._signer = HyperliquidSigner(
    clock=self._clock,
    private_key_hex=private_key,
    is_mainnet=self._is_mainnet,
    vault_address=self._vault_address,
)
```

**改进效果**:
- 正确传递网络类型(mainnet/testnet)，确保签名使用正确的链ID
- 支持vault地址，允许代表子账户或保险库进行交易
- 利用Signer的完整功能，避免重复配置

### 2. 签名类型问题

**问题**: 所有HTTP请求都统一使用L1签名，但Hyperliquid有两种不同的签名类型：
- L1 actions: 大部分交易操作(下单、取消、修改等)
- User-signed actions: 转账、提现等操作

**解决方案**: 
```python
# 新增签名类型参数
async def send_request(
    self,
    http_method: HttpMethod,
    endpoint: str,
    payload: dict[str, Any] | None = None,
    signed: bool = False,
    sign_type: str = "l1_action",  # 新增参数
    ...
):
```

**专用签名方法**:
```python
# L1 actions (交易操作)
async def post_l1_action(self, action: dict[str, Any], ...):
    signature = self._signer.sign_l1_action(...)
    
# User-signed actions (转账操作)  
async def post_user_signed_action(self, action: dict[str, Any], payload_types: list, ...):
    signature = self._signer.sign_user_signed_action(...)
```

**改进效果**:
- 正确区分不同类型的操作，使用相应的签名方法
- 充分利用Signer中已有的专用函数(`sign_usd_transfer_action`, `sign_withdraw_action`等)
- 提高签名的准确性和安全性

### 3. 数据解析问题

**问题**: 当前所有API返回都是原始dict格式，缺乏进一步的结构化解析。

**当前状态**:
```python
# 返回原始dict
async def get_all_mids(self) -> dict[str, str]:
    payload = {"type": "allMids", "dex": dex}
    return await self._client.post("/info", payload)  # 原始dict
```

**改进建议**:

#### 选项1: 保持简单(推荐用于MVP)
```python
# 保持dict格式，但添加类型注解和文档
async def get_all_mids(self, dex: str = "") -> dict[str, str]:
    """
    Returns:
        dict[str, str]: Mapping of coin names to mid prices
        Example: {"BTC": "30000.0", "ETH": "2000.0"}
    """
```

#### 选项2: 结构化数据类(适合生产环境)
```python
from dataclasses import dataclass
from typing import List, Optional

@dataclass
class MidPrice:
    symbol: str
    price: str
    
@dataclass  
class OrderBookLevel:
    price: str
    size: str
    count: int

class HyperliquidMarketHttpApi:
    async def get_all_mids_structured(self) -> List[MidPrice]:
        raw_data = await self.get_all_mids()
        return [MidPrice(symbol=k, price=v) for k, v in raw_data.items()]
```

#### 选项3: 混合方式(灵活性最高)
```python
# 提供两种接口
async def get_all_mids(self) -> dict[str, str]:
    """返回原始dict格式"""
    
async def get_all_mids_parsed(self) -> List[MidPrice]:  
    """返回结构化数据"""
    raw_data = await self.get_all_mids()
    return self._parse_mid_prices(raw_data)
```

## 实现状态

### ✅ 已完成
1. **Signer参数修复**: 正确传递所有必要参数
2. **签名类型支持**: 实现L1和user-signed两种签名方式
3. **专用签名方法**: 添加`post_l1_action`和`post_user_signed_action`
4. **Exchange API更新**: 部分方法已更新使用正确签名

### 🔄 进行中
1. **Exchange API完整更新**: 需要完成所有方法的签名类型修正
2. **User-signed actions**: 需要实现转账和提现的正确签名

### 📋 待完成
1. **数据解析层**: 根据需求选择解析策略
2. **错误处理增强**: 针对不同签名类型的错误处理
3. **测试覆盖**: 验证不同签名类型的正确性

## 使用示例

### 正确的L1 Action调用
```python
# 下单 - 使用L1签名
await exchange_api.place_order(
    asset=0,
    is_buy=True, 
    limit_px="30000.0",
    sz="0.001"
)
```

### 正确的User-signed Action调用  
```python
# 转账 - 使用user-signed签名
await exchange_api.usd_transfer(
    destination="0x...",
    amount="100.0"
)
```

## 建议

1. **优先级**: 先完成签名类型修正，再考虑数据解析
2. **测试**: 在testnet上验证不同签名类型的正确性
3. **文档**: 为每个API方法明确标注签名类型和返回格式
4. **渐进式**: 可以先保持dict格式，后续根据需要添加结构化解析
