# Instruments

```{eval-rst}
.. automodule:: nautilus_trader.model.instruments
```

```{eval-rst}
.. automodule:: nautilus_trader.model.instruments.betting
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

```{eval-rst}
.. automodule:: nautilus_trader.model.instruments.crypto_perpetual
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

```{eval-rst}
.. automodule:: nautilus_trader.model.instruments.crypto_future
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

```{eval-rst}
.. automodule:: nautilus_trader.model.instruments.currency_pair
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

```{eval-rst}
.. automodule:: nautilus_trader.model.instruments.equity
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

```{eval-rst}
.. automodule:: nautilus_trader.model.instruments.futures_contract
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

```{eval-rst}
.. automodule:: nautilus_trader.model.instruments.option_contract
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

```{eval-rst}
.. automodule:: nautilus_trader.model.instruments.synthetic
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

```{eval-rst}
.. automodule:: nautilus_trader.model.instruments.base
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```
