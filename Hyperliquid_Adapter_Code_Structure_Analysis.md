# Hyperliquid Adapter Code Structure Analysis

## Overview

This document analyzes the Hyperliquid adapter code structure following the same format as the general Exchange Adapter analysis, mapping each file to its corresponding API documentation and ensuring complete coverage of the Hyperliquid API functionality.

## High-Level Architecture

The Hyperliquid adapter follows the same architectural pattern as other exchange adapters with these core components:

### 1. **Main Client Classes**
- **Data Client**: Handles market data feeds (quotes, trades, orderbooks, bars, funding rates)
- **Execution Client**: Manages account operations and trade execution
- **HTTP Client**: Low-level HTTP API connectivity with signing
- **WebSocket Client**: Real-time data streaming
- **Instrument Provider**: Instrument metadata and symbol management

### 2. **Factory Pattern**
- **HyperliquidLiveDataClientFactory**: Creates and configures data clients
- **HyperliquidLiveExecClientFactory**: Creates and configures execution clients

## Directory Structure Analysis

### Current Structure Pattern
```
adapters/hyperliquid/
├── __init__.py
├── common/                    # Shared utilities and constants
│   ├── __init__.py
│   ├── constants.py          # Exchange-specific constants
│   ├── core.py               # Core functionality
│   ├── enums.py              # Exchange-specific enumerations
│   └── types.py              # Custom type definitions
├── config.py                 # Configuration classes (EMPTY - needs implementation)
├── data.py                   # Data client implementation (EMPTY - needs implementation)
├── execution.py              # Execution client implementation (EMPTY - needs implementation)
├── factories.py              # Client factory implementations (MISSING - needs creation)
├── providers.py              # Instrument provider (EMPTY - needs implementation)
├── http/                     # HTTP client implementations
│   ├── __init__.py
│   ├── account.py            # Account HTTP API
│   ├── client.py             # Main HTTP client
│   ├── error.py              # HTTP error handling
│   ├── exchange.py           # Exchange HTTP API
│   └── market.py             # Market HTTP API
├── schemas/                  # Data structure definitions (MISSING - needs creation)
│   ├── __init__.py
│   ├── common.py             # Common schemas
│   ├── account/              # Account-related schemas
│   ├── market/               # Market data schemas
│   ├── order.py              # Order schemas
│   ├── position.py           # Position schemas
│   └── ws.py                 # WebSocket message schemas
├── util/                     # Utility functions
│   ├── __init__.py
│   └── signing.py            # Signing utilities and authentication
└── websocket/                # WebSocket client
    ├── __init__.py
    └── client.py             # WebSocket implementation
```

### Hyperliquid-Specific Variations

**Unique Features**:
- **Dual signing schemes**: L1 actions vs user-signed actions
- **Vault support**: Sub-accounts and vault trading
- **Asset ID system**: Perpetuals (0-999) vs Spot (10000+)
- **Nonce management**: Timestamp-based nonces for replay protection
- **EIP-712 signing**: Ethereum-style message signing

## Core Component Analysis

### 1. Configuration Classes (`config.py`)

**Purpose**: Define configuration parameters for data and execution clients

**Documentation Reference**: General configuration patterns + Hyperliquid-specific settings

**Key Patterns**:
```python
class HyperliquidDataClientConfig(LiveDataClientConfig, frozen=True):
    # Hyperliquid-specific data client configuration

class HyperliquidExecClientConfig(LiveExecClientConfig, frozen=True):
    # Hyperliquid-specific execution client configuration
```

**Hyperliquid-Specific Configuration Elements**:
- Private key for signing (instead of API key/secret)
- Vault address for sub-account trading
- Testnet vs mainnet selection
- WebSocket subscription preferences
- Asset ID mappings

### 2. Data Client (`data.py`)

**Purpose**: Handle market data subscriptions and historical data requests

**Documentation Reference**:
- `info_endpoint.md` (Lines 1-313)
- `websocket.md` (Lines 1-246)
- `info_endpoint_perpetuals.md` (Lines 1-323)

**Key Responsibilities**:
- WebSocket connection management for real-time data
- Historical data requests via HTTP Info endpoint
- Data parsing and normalization
- Subscription management (quotes, trades, orderbooks, bars, funding)

**Core Structure**:
```python
class HyperliquidDataClient(LiveMarketDataClient):
    def __init__(self, loop, client, msgbus, cache, clock, instrument_provider, ...):
        # Initialize WebSocket clients
        # Set up data decoders
        # Configure subscriptions

    async def _connect(self):
        # Establish connections

    async def _subscribe_**(self, ...):
        # Handle various subscription types

    def _handle_ws_message(self, message):
        # Process incoming WebSocket messages
```

### 3. Execution Client (`execution.py`)

**Purpose**: Handle order management and account operations

**Documentation Reference**: `exchange_endpoint.md` (Lines 1-277)

**Key Responsibilities**:
- Order submission, modification, cancellation
- Account balance and position monitoring
- Trade execution reporting
- Leverage and margin management

**Core Structure**:
```python
class HyperliquidExecutionClient(LiveExecutionClient):
    def __init__(self, loop, client, msgbus, cache, clock, instrument_provider, ...):
        # Initialize HTTP client with signing
        # Set up account management
        # Configure order handling

    async def _submit_order(self, order):
        # Handle order submission with signing

    async def _modify_order(self, command):
        # Handle order modifications

    async def _cancel_order(self, command):
        # Handle order cancellations
```

### 4. Factory Classes (`factories.py`)

**Purpose**: Create and configure client instances with proper dependencies

**Documentation Reference**: Standard factory pattern + Hyperliquid-specific setup

**Key Patterns**:
```python
class HyperliquidLiveDataClientFactory(LiveDataClientFactory):
    @staticmethod
    def create(loop, name, config, msgbus, cache, clock):
        # Create HTTP client with signing
        # Create instrument provider
        # Create and return data client

class HyperliquidLiveExecClientFactory(LiveExecClientFactory):
    @staticmethod
    def create(loop, name, config, msgbus, cache, clock):
        # Create HTTP client with signing
        # Create instrument provider
        # Create and return execution client
```

### 5. Common Utilities (`common/`)

**enums.py**: Hyperliquid-specific enumerations and parsing logic
- **Documentation Reference**: `exchange_endpoint.md` (Lines 29-111), `websocket.md` (Lines 36-107)
- Order types, sides, statuses
- Time in force options
- WebSocket subscription types
- Enum parser classes for bidirectional conversion

**types.py**: Type definitions and data structures
- **Documentation Reference**: `asset_ids.md`, `websocket.md` (Lines 114-176)
- Asset ID types (perpetuals vs spot)
- WebSocket message types
- Order and position structures

**constants.py**: API endpoints and constants
- **Documentation Reference**: `info_endpoint.md` (Lines 7-9), `exchange_endpoint.md` (Lines 7-9)
- Base URLs for mainnet/testnet
- WebSocket URLs
- API version information

**core.py**: Core functionality and utilities
- Symbol handling and normalization
- Price/quantity formatting
- Timestamp conversions

### 6. HTTP Layer (`http/`)

**client.py**: Core HTTP client with authentication
- **Documentation Reference**: `signing.md` (Lines 1-141)
- Request signing with EIP-712
- Nonce management
- Error handling and retries
- Response parsing

**market.py**: Market data HTTP API
- **Documentation Reference**: `info_endpoint.md` (Lines 1-313), `info_endpoint_perpetuals.md` (Lines 1-323)
- Info endpoint implementations
- Historical data requests
- Market metadata retrieval

**exchange.py**: Trading HTTP API
- **Documentation Reference**: `exchange_endpoint.md` (Lines 1-277)
- Order management endpoints
- Account operations
- Transfer and withdrawal functions

**account.py**: Account-specific operations
- User account information
- Sub-account management
- Portfolio data

### 7. Schemas (`schemas/`)

**Purpose**: Define data structures for API responses and WebSocket messages

**Documentation Reference**: All API documentation for response formats

**Organization**:
- Grouped by functionality (account, market, trade)
- msgspec.Struct-based definitions for performance
- Validation and type safety

### 8. WebSocket Client (`websocket/`)

**Purpose**: Real-time data streaming and subscriptions

**Documentation Reference**: `websocket.md` (Lines 1-246), `websocket_advanced.md`

**Structure**:
```python
class HyperliquidWebSocketClient:
    def __init__(self, ...):
        # Configure WebSocket connection

    async def subscribe(self, subscription_type, params):
        # Handle subscriptions

    def _handle_message(self, message):
        # Route messages by type
```

## Key Design Patterns

### 1. **Separation of Concerns**
- Clear separation between data and execution responsibilities
- HTTP and WebSocket clients handle different aspects
- Modular endpoint organization by functionality

### 2. **Factory Pattern**
- Centralized client creation and configuration
- Dependency injection for testability
- Environment-specific configuration (testnet/mainnet)

### 3. **Adapter Pattern**
- Hyperliquid-specific implementations behind common interfaces
- Consistent API across different exchanges
- Nautilus framework integration

### 4. **Observer Pattern**
- WebSocket message handling through callbacks
- Event-driven architecture for real-time data
- Message bus integration for system-wide communication

### 5. **Strategy Pattern**
- Configurable behavior through configuration classes
- Different handling for perpetuals vs spot
- Environment-specific adaptations (mainnet/testnet)

### 6. **Signing Pattern (Hyperliquid-Specific)**
- EIP-712 structured data signing
- Dual signing schemes (L1 actions vs user-signed actions)
- Nonce management for replay protection

## Implementation Guidelines

### What Goes Where

**`common/`**: Shared utilities, constants, and Hyperliquid-specific logic
- Enumerations and parsing logic
- Type definitions for API structures
- Constants for URLs and endpoints
- Core utilities for data transformation

**`config.py`**: Configuration classes with validation
- Private key configuration
- Vault address settings
- Network selection (testnet/mainnet)
- WebSocket preferences

**`data.py`**: Market data client implementation
- WebSocket subscriptions for real-time data
- Historical data requests via Info endpoint
- Data normalization and parsing
- Real-time feed management

**`execution.py`**: Trading client implementation
- Order management with signing
- Account monitoring and operations
- Position tracking and leverage management
- Transfer and withdrawal operations

**`factories.py`**: Client creation and dependency injection
- HTTP client setup with signing
- Instrument provider creation
- Client configuration and initialization

**`http/`**: HTTP API layer
- Authentication and EIP-712 signing
- Request/response handling
- Nonce management
- Error management and retries

**`schemas/`**: Data structure definitions
- API response models
- WebSocket message formats
- Validation schemas using msgspec

**`websocket/`**: Real-time communication
- Connection management
- Subscription handling
- Message routing and parsing
- Reconnection logic

**`util/`**: Utility functions
- Signing utilities (already implemented)
- Helper functions for data conversion
- Validation utilities

## Detailed Implementation Examples

### 1. Enum Parser Implementation Pattern

Hyperliquid adapter implements comprehensive enum parsers for bidirectional conversion:

```python
class HyperliquidEnumParser:
    def __init__(self):
        # Bidirectional mappings
        self.hyperliquid_to_nautilus_order_side = {
            "B": OrderSide.BUY,
            "A": OrderSide.SELL,
        }
        self.nautilus_to_hyperliquid_order_side = {
            v: k for k, v in self.hyperliquid_to_nautilus_order_side.items()
        }

    def parse_nautilus_order_side(self, side: OrderSide) -> str:
        return self.nautilus_to_hyperliquid_order_side[side]

    def parse_hyperliquid_order_side(self, side: str) -> OrderSide:
        return self.hyperliquid_to_nautilus_order_side[side]
```

### 2. HTTP Client Authentication Pattern

```python
class HyperliquidHttpClient:
    def __init__(self, clock, private_key, testnet=False, vault_address=None):
        self._clock = clock
        self._signer = HyperliquidSigner(private_key, clock)
        self._testnet = testnet
        self._vault_address = vault_address

    async def sign_request(self, action, vault_address=None):
        nonce = self._clock.timestamp_ms()
        signature = self._signer.sign_l1_action(
            action=action,
            vault_address=vault_address or self._vault_address,
            nonce=nonce,
            is_mainnet=not self._testnet,
        )

        return {
            "action": action,
            "nonce": nonce,
            "signature": signature,
            "vaultAddress": vault_address,
        }
```

### 3. WebSocket Message Handling Pattern

```python
class HyperliquidDataClient(LiveMarketDataClient):
    def _handle_ws_message(self, raw: bytes) -> None:
        try:
            # Decode message
            message = json.loads(raw)

            # Route by channel type
            channel = message.get("channel")
            if channel == "trades":
                self._handle_trade_update(message["data"])
            elif channel == "l2Book":
                self._handle_orderbook_update(message["data"])
            elif channel == "candle":
                self._handle_candle_update(message["data"])
            elif channel == "userFills":
                self._handle_user_fills_update(message["data"])

        except Exception as e:
            self._log.error(f"Error handling WebSocket message: {e}")
```

### 4. Configuration Validation Pattern

```python
class HyperliquidDataClientConfig(LiveDataClientConfig, frozen=True):
    private_key: str | None = None
    vault_address: str | None = None
    testnet: bool = False
    subscriptions: list[str] | None = None

    def __post_init__(self):
        # Validation logic
        if not self.private_key:
            raise ValueError("private_key is required for Hyperliquid")
        if self.private_key and not self.private_key.startswith("0x"):
            raise ValueError("private_key must be a hex string starting with 0x")
```

## Best Practices and Patterns

### 1. **Error Handling Strategy**

- **HTTP Errors**: Custom exception classes with Hyperliquid error codes
- **WebSocket Errors**: Graceful reconnection with exponential backoff
- **Signing Errors**: Detailed validation and debugging information
- **Rate Limiting**: Respect API limits with proper queuing

### 2. **Performance Optimization**

- **msgspec**: Use for fast JSON encoding/decoding
- **Connection Pooling**: Reuse HTTP connections
- **Signature Caching**: Cache signatures when appropriate
- **Asset ID Mapping**: Cache instrument metadata

### 3. **Testing Strategy**

- **Unit Tests**: Test individual components in isolation
- **Integration Tests**: Test with Hyperliquid testnet
- **Signing Tests**: Validate against known signatures
- **WebSocket Tests**: Test real-time data handling

### 4. **Documentation Requirements**

- **API Documentation**: Document all public methods and classes
- **Configuration Guide**: Explain Hyperliquid-specific settings
- **Integration Examples**: Provide working code examples
- **Troubleshooting Guide**: Common signing and connection issues

### 5. **Security Considerations**

- **Private Key Management**: Never log or expose private keys
- **Nonce Management**: Ensure uniqueness to prevent replay attacks
- **Signature Validation**: Validate signatures before sending
- **Connection Security**: Use secure WebSocket connections

## Hyperliquid-Specific Considerations

### Decentralized Exchange Features
- **EIP-712 Signing**: Ethereum-style message signing for all operations
- **Vault Support**: Sub-account and vault trading capabilities
- **Asset ID System**: Unique numbering for perpetuals (0-999) and spot (10000+)
- **Dual Endpoints**: Separate Info and Exchange endpoints for different operations

### Network Handling
- **Mainnet vs Testnet**: Different URLs and chain IDs
- **Nonce Management**: Timestamp-based nonces for replay protection
- **Gas-Free Trading**: No gas fees for trading operations
- **Real-time Settlement**: Immediate settlement on L1

## Maintenance and Evolution

### 1. **API Version Management**
- Monitor Hyperliquid API updates and changes
- Maintain backward compatibility when possible
- Update documentation references as needed

### 2. **Feature Flags**
- Enable/disable features through configuration
- Support for new asset types and trading features
- Gradual rollout of new functionality

### 3. **Monitoring and Observability**
- Comprehensive logging at appropriate levels
- Metrics collection for performance monitoring
- Health checks for WebSocket connections
- Error tracking and alerting

### 4. **Scalability Considerations**
- Efficient WebSocket connection management
- Optimized signing and nonce generation
- Memory usage optimization for real-time data
- Connection pooling for HTTP requests

## Complete API Documentation Mapping

### Core Documentation Files:
1. **`info_endpoint.md`** (Lines 1-313) → `http/market.py`
2. **`exchange_endpoint.md`** (Lines 1-277) → `http/exchange.py`
3. **`websocket.md`** (Lines 1-246) → `websocket/client.py`
4. **`info_endpoint_perpetuals.md`** (Lines 1-323) → `http/market.py`
5. **`signing.md`** (Lines 1-141) → `util/signing.py` ✅

### Supporting Documentation Files:
6. **`info_endpoint_spot.md`** → `http/market.py` (spot section)
7. **`asset_ids.md`** → `common/types.py`, `providers.py`
8. **`error_responses.md`** → `http/error.py`
9. **`nonces_and_api_wallets.md`** → `util/signing.py`
10. **`tick_and_lot_size.md`** → `providers.py`, `common/types.py`
11. **`websocket_advanced.md`** → `websocket/client.py`
12. **`HyperLiquid_API_符号表示法.md`** → `common/types.py`

This comprehensive structure ensures robust, maintainable, and scalable Hyperliquid adapter implementation that fully covers the API while following established Nautilus patterns.
