# HyperLiquid API Exchange端点

Exchange端点是用于与HyperLiquid链进行交互和交易的主要接口。本文档详细介绍了Exchange端点的各种功能和使用方法。

## 基本信息

- **端点URL**: `https://api.hyperliquid.xyz/exchange`
- **请求方法**: `POST`
- **内容类型**: `application/json`

## 资产ID

许多请求都需要资产ID作为输入：
- **永续合约**：使用`meta`响应中`universe`字段的索引
- **现货资产**：使用`10000 + index`，其中`index`是`spotMeta.universe`中的相应索引
  
例如，当提交`PURR/USDC`的订单时，应使用的资产ID是`10000`，因为它在现货元数据中的资产索引是`0`。

## 子账户和保险库

子账户和保险库没有私钥。要代表子账户或保险库执行操作，签名应由主账户完成，并且`vaultAddress`字段应设置为子账户或保险库的地址。

## 过期时间

某些操作支持可选字段`expiresAfter`，这是一个毫秒时间戳，超过该时间后操作将被拒绝。用户签名的操作（如Core USDC转账）不支持`expiresAfter`字段。

## 主要功能

### 1. 下单

```json
// 请求
{
  "action": {
    "type": "order",
    "orders": [{
      "a": 0, // 资产ID
      "b": true, // 是否买入
      "p": "30000", // 价格
      "s": "0.1", // 数量
      "r": false, // 是否只减仓
      "t": {
        "limit": {
          "tif": "Gtc" // 有效期类型：Alo(只挂单), Ioc(立即成交或取消), Gtc(一直有效)
        }
      },
      "c": "0x1234567890abcdef1234567890abcdef" // 客户端订单ID（可选）
    }],
    "grouping": "na", // 订单分组：na, normalTpsl, positionTpsl
    "builder": { // 可选的构建者参数
      "b": "0x0000000000000000000000000000000000000000", // 接收额外费用的地址
      "f": 10 // 费用大小（十分之一个基点）
    }
  },
  "nonce": 1681247412573, // 建议使用当前时间戳（毫秒）
  "signature": {...}, // 签名对象
  "vaultAddress": "0x0000000000000000000000000000000000000000", // 可选，代表保险库或子账户交易时使用
  "expiresAfter": 1681247512573 // 可选，过期时间戳（毫秒）
}

// 成功响应（挂单）
{
  "status": "ok",
  "response": {
    "type": "order",
    "data": {
      "statuses": [
        {
          "resting": {
            "oid": 77738308
          }
        }
      ]
    }
  }
}

// 成功响应（成交）
{
  "status": "ok",
  "response": {
    "type": "order",
    "data": {
      "statuses": [
        {
          "filled": {
            "totalSz": "0.02",
            "avgPx": "1891.4",
            "oid": 77747314
          }
        }
      ]
    }
  }
}

// 错误响应
{
  "status": "ok",
  "response": {
    "type": "order",
    "data": {
      "statuses": [
        {
          "error": "Order must have minimum value of $10."
        }
      ]
    }
  }
}
```

### 2. 取消订单

```json
// 请求
{
  "action": {
    "type": "cancel",
    "cancels": [
      {
        "a": 0, // 资产ID
        "o": 77738308 // 订单ID
      }
    ]
  },
  "nonce": 1681247412573,
  "signature": {...},
  "vaultAddress": "0x0000000000000000000000000000000000000000", // 可选
  "expiresAfter": 1681247512573 // 可选
}

// 成功响应
{
  "status": "ok",
  "response": {
    "type": "cancel",
    "data": {
      "statuses": [
        "success"
      ]
    }
  }
}
```

### 3. 通过客户端订单ID取消订单

```json
// 请求
{
  "action": {
    "type": "cancelByCloid",
    "cancels": [
      {
        "asset": 0,
        "cloid": "0x1234567890abcdef1234567890abcdef"
      }
    ]
  },
  "nonce": 1681247412573,
  "signature": {...},
  "vaultAddress": "0x0000000000000000000000000000000000000000", // 可选
  "expiresAfter": 1681247512573 // 可选
}
```

### 4. 修改订单

```json
// 请求
{
  "action": {
    "type": "modify",
    "oid": 77738308, // 订单ID或客户端订单ID
    "order": {
      "a": 0,
      "b": true,
      "p": "30100",
      "s": "0.1",
      "r": false,
      "t": {
        "limit": {
          "tif": "Gtc"
        }
      },
      "c": "0x1234567890abcdef1234567890abcdef" // 可选
    }
  },
  "nonce": 1681247412573,
  "signature": {...},
  "vaultAddress": "0x0000000000000000000000000000000000000000", // 可选
  "expiresAfter": 1681247512573 // 可选
}
```

### 5. 更新杠杆

```json
// 请求
{
  "action": {
    "type": "updateLeverage",
    "asset": 0, // 币种索引
    "isCross": true, // 如果更新全仓杠杆则为true
    "leverage": 10 // 新杠杆，受该币种的杠杆约束
  },
  "nonce": 1681247412573,
  "signature": {...},
  "vaultAddress": "0x0000000000000000000000000000000000000000", // 可选
  "expiresAfter": 1681247512573 // 可选
}
```

### 6. Core USDC转账

```json
// 请求
{
  "action": {
    "type": "usdSend",
    "hyperliquidChain": "Mainnet", // 在测试网上使用"Testnet"
    "signatureChainId": "0xa4b1", // 签名时使用的链ID，十六进制格式
    "destination": "0x0000000000000000000000000000000000000000", // 目标地址
    "amount": "1", // 发送的USD金额，例如"1"表示1美元
    "time": 1681247412573 // 当前时间戳（毫秒），应与nonce匹配
  },
  "nonce": 1681247412573,
  "signature": {...}
}
```

### 7. 发起提款请求

```json
// 请求
{
  "action": {
    "type": "withdraw3",
    "hyperliquidChain": "Mainnet", // 在测试网上使用"Testnet"
    "signatureChainId": "0xa4b1", // 签名时使用的链ID，十六进制格式
    "amount": "1", // 发送的USD金额，例如"1"表示1美元
    "time": 1681247412573, // 当前时间戳（毫秒），应与nonce匹配
    "destination": "0x0000000000000000000000000000000000000000" // 目标地址
  },
  "nonce": 1681247412573,
  "signature": {...}
}
```

## 其他功能

Exchange端点还支持以下功能：

1. **计划取消（死人开关）**：`"type": "scheduleCancel"`
2. **批量修改订单**：`"type": "batchModify"`
3. **更新逐仓保证金**：`"type": "updateIsolatedMargin"`
4. **现货转账**：`"type": "spotSend"`
5. **现货账户与永续账户之间转账**：`"type": "usdClassTransfer"`
6. **存入质押**：`"type": "cDeposit"`
7. **从质押中提取**：`"type": "cWithdraw"`
8. **委托或取消委托质押**：`"type": "tokenDelegate"`
9. **存入或从保险库提取**：`"type": "vaultTransfer"`
10. **批准API钱包**：`"type": "approveAgent"`
11. **批准构建者费用**：`"type": "approveBuilderFee"`
12. **下TWAP订单**：`"type": "twapOrder"`
13. **取消TWAP订单**：`"type": "twapCancel"`
14. **预留额外操作**：`"type": "reserveRequestWeight"`

## 最佳实践

1. **使用SDK**：建议使用现有的SDK而不是手动生成签名，以避免签名错误。
2. **批处理请求**：尽可能批处理订单和取消请求，以减少API调用次数。
3. **处理错误**：实现适当的错误处理机制，以应对API可能返回的各种错误。
4. **使用nonce**：使用当前时间戳作为nonce，确保每个请求都有唯一的nonce。
5. **子账户和保险库**：在代表子账户或保险库交易时，确保正确设置`vaultAddress`字段。
