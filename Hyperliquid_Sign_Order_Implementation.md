# Hyperliquid sign_order 函数实现总结

## 实现概述

基于你的建议，我们实现了 `sign_order` 函数，它接受dict形式的参数，类似于 `sign_cancel_order` 的模式，并参考了官方SDK的结构。

## 核心实现

### **1. sign_order 方法** (`adapters/hyperliquid/util/signing.py`)

```python
def sign_order(self, order_params: Dict[str, Any]) -> Dict[str, Any]:
    """
    Signs an order action with dict parameters (similar to sign_cancel_order pattern).
    
    Based on official SDK's order structure and signing pattern.
    """
```

#### **参数结构** (基于官方SDK):
```python
order_params = {
    # 必需参数
    "asset": 0,                                    # 资产ID
    "is_buy": True,                               # 是否买单
    "sz": 0.001,                                  # 订单大小
    "limit_px": 30000.0,                          # 限价
    "order_type": {"limit": {"tif": "Gtc"}},      # 订单类型
    "reduce_only": False,                         # 是否仅减仓
    
    # 可选参数
    "cloid": "my_order_123",                      # 客户端订单ID
    "grouping": "na",                             # 订单分组
    "builder": {...},                             # 构建者信息
    "vault_address": "0x...",                     # 金库地址
    "nonce": 1234567890,                          # nonce值
    "is_mainnet": True,                           # 是否主网
    "expires_after": 1234567890                   # 过期时间
}
```

#### **订单类型格式** (遵循官方SDK):

**限价单:**
```python
"order_type": {"limit": {"tif": "Gtc"}}  # Gtc, Ioc, Alo
```

**触发单 (止损/止盈):**
```python
"order_type": {
    "trigger": {
        "triggerPx": 29500.0,    # 触发价格
        "isMarket": False,       # 是否市价单
        "tpsl": "sl"            # "sl" (止损) 或 "tp" (止盈)
    }
}
```

### **2. 内部辅助方法**

#### **_order_type_to_wire()** - 订单类型转换
```python
def _order_type_to_wire(self, order_type: Dict[str, Any]) -> Dict[str, Any]:
    """Convert order type to wire format (from official SDK)."""
    if "limit" in order_type:
        return {"limit": order_type["limit"]}
    elif "trigger" in order_type:
        trigger = order_type["trigger"]
        return {
            "trigger": {
                "isMarket": trigger["isMarket"],
                "triggerPx": float_to_wire(trigger["triggerPx"]),
                "tpsl": trigger["tpsl"],
            }
        }
```

## 使用方式

### **方式1: 直接使用 sign_order (推荐)**

```python
# 限价单
order_params = {
    "asset": 0,
    "is_buy": True,
    "sz": 0.001,
    "limit_px": 30000.0,
    "order_type": {"limit": {"tif": "Gtc"}},
    "reduce_only": False,
    "cloid": "limit_order_001"
}

signed_order = signer.sign_order(order_params)
```

### **方式2: 结合枚举解析器使用**

```python
# 在执行客户端中
class HyperliquidExecutionClient:
    def _submit_order(self, order: Order):
        # 1. 转换Nautilus枚举
        is_buy = self._enum_parser.parse_nautilus_order_side(order.side)
        hl_tif = self._enum_parser.parse_nautilus_time_in_force(order.time_in_force)
        
        # 2. 构建订单参数
        order_params = {
            "asset": asset_id,
            "is_buy": is_buy,
            "sz": float(order.quantity),
            "limit_px": float(order.price),
            "order_type": {"limit": {"tif": hl_tif.value}},
            "reduce_only": order.reduce_only,
            "cloid": order.client_order_id.value
        }
        
        # 3. 签名
        return self._signer.sign_order(order_params)
```

### **方式3: 原始dict参数 (最灵活)**

```python
# 完全按照官方SDK格式
raw_params = {
    "asset": 0,
    "is_buy": True,
    "sz": 0.001,
    "limit_px": 30000.0,
    "order_type": {"limit": {"tif": "Gtc"}},
    "reduce_only": False
}

signed = signer.sign_order(raw_params)
```

## 与官方SDK的对比

### **相似性** ✅
1. **参数结构**: 完全遵循官方SDK的OrderRequest和OrderWire格式
2. **订单类型**: 支持limit和trigger两种类型，格式完全一致
3. **签名流程**: 使用相同的L1 action签名模式
4. **wire格式**: 订单转换为wire格式的逻辑与官方SDK一致

### **优势** ✅
1. **更好的集成**: 与Nautilus框架和LiveClock集成
2. **类型安全**: 更好的参数验证和错误处理
3. **灵活性**: 支持多种使用方式
4. **一致性**: 与其他signing方法保持一致的API风格

## 实现细节

### **订单wire格式** (遵循官方SDK):
```python
order_wire = {
    "a": asset,                    # asset ID
    "b": is_buy,                   # boolean: buy/sell
    "p": float_to_wire(limit_px),  # price (string)
    "s": float_to_wire(sz),        # size (string)
    "r": reduce_only,              # boolean: reduce only
    "t": order_type_wire,          # order type object
    "c": cloid                     # optional: client order ID
}
```

### **action格式** (遵循官方SDK):
```python
action = {
    "type": "order",
    "orders": [order_wire],
    "grouping": "na",              # or other grouping
    "builder": builder_info        # optional
}
```

## 测试示例

实现包含了完整的测试示例，展示了三种使用方式：

1. **枚举转换方式**: 适合与Nautilus集成
2. **直接方法**: 简化的API调用
3. **原始dict**: 最大灵活性，完全兼容官方SDK

## 总结

✅ **完成的功能:**
- 实现了完全兼容官方SDK的 `sign_order` 方法
- 支持dict参数，类似 `sign_cancel_order` 模式
- 包含完整的订单类型支持 (limit, trigger)
- 提供了多种使用方式和完整示例

✅ **优势:**
- **官方SDK兼容**: 完全遵循官方SDK的结构和格式
- **灵活易用**: 支持多种调用方式
- **类型安全**: 更好的验证和错误处理
- **框架集成**: 与Nautilus和LiveClock良好集成

这个实现为Hyperliquid适配器提供了强大而灵活的订单签名功能，既保持了与官方SDK的兼容性，又提供了更好的开发体验。
