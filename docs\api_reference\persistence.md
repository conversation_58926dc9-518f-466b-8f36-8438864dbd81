# Persistence

```{eval-rst}
.. automodule:: nautilus_trader.persistence
```

```{eval-rst}
.. automodule:: nautilus_trader.persistence.catalog.base
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

```{eval-rst}
.. automodule:: nautilus_trader.persistence.catalog.parquet
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

```{eval-rst}
.. automodule:: nautilus_trader.persistence.wranglers
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

```{eval-rst}
.. automodule:: nautilus_trader.persistence.writer
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```
