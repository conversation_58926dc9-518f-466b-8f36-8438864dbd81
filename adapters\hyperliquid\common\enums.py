from enum import Enum
from typing import Dict

from nautilus_trader.model.enums import OrderSide
from nautilus_trader.model.enums import OrderType
from nautilus_trader.model.enums import TimeInForce


class HyperliquidTimeInForce(str, Enum):
    """Hyperliquid-specific time in force options (wire format)."""
    ALO = "Alo"  # Add Liquidity Only (Post Only)
    IOC = "Ioc"  # Immediate or cancel
    GTC = "Gtc"  # Good till cancelled


class HyperliquidTriggerType(str, Enum):
    """Hyperliquid trigger types for stop/take profit orders."""
    TP = "tp"  # Take Profit
    SL = "sl"  # Stop Loss


class HyperliquidEnumParser:
    """
    Parser for converting between Nautilus and Hyperliquid enums.

    Based on official SDK structure where orders use TypedDict format:
    - Limit orders: {"limit": {"tif": "Gtc"}}
    - Trigger orders: {"trigger": {"triggerPx": "30000", "isMarket": True, "tpsl": "tp"}}
    """

    def __init__(self):
        # Time In Force mappings
        self.nautilus_to_hyperliquid_time_in_force = {
            TimeInForce.GTC: HyperliquidTimeInForce.GTC,
            TimeInForce.IOC: HyperliquidTimeInForce.IOC,
            # Note: FOK is not in official SDK, ALO is Post-Only equivalent
        }

        self.hyperliquid_to_nautilus_time_in_force = {
            v: k for k, v in self.nautilus_to_hyperliquid_time_in_force.items()
        }

        self.valid_time_in_force = {
            TimeInForce.GTC,
            TimeInForce.IOC,
        }

        self.valid_order_types = {
            OrderType.LIMIT,
            OrderType.MARKET,
            OrderType.STOP_MARKET,
            OrderType.STOP_LIMIT,
            OrderType.MARKET_IF_TOUCHED,
            OrderType.LIMIT_IF_TOUCHED,
        }

    def create_limit_order_type(self, time_in_force: TimeInForce) -> Dict:
        """Create a limit order type structure."""
        hl_tif = self.parse_nautilus_time_in_force(time_in_force)
        return {"limit": {"tif": hl_tif.value}}

    def create_trigger_order_type(
        self,
        trigger_price: str,
        is_market: bool,
        trigger_type: HyperliquidTriggerType
    ) -> Dict:
        """Create a trigger order type structure."""
        return {
            "trigger": {
                "triggerPx": trigger_price,
                "isMarket": is_market,
                "tpsl": trigger_type.value
            }
        }

    def parse_nautilus_time_in_force(self, time_in_force: TimeInForce) -> HyperliquidTimeInForce:
        """Convert Nautilus TimeInForce to Hyperliquid format."""
        try:
            return self.nautilus_to_hyperliquid_time_in_force[time_in_force]
        except KeyError as e:
            raise RuntimeError(
                f"Unsupported Nautilus time in force for Hyperliquid: {time_in_force}"
            ) from e

    def parse_hyperliquid_time_in_force(self, time_in_force: HyperliquidTimeInForce) -> TimeInForce:
        """Convert Hyperliquid TimeInForce to Nautilus format."""
        try:
            return self.hyperliquid_to_nautilus_time_in_force[time_in_force]
        except KeyError as e:
            raise RuntimeError(
                f"Unrecognized Hyperliquid time in force: {time_in_force}"
            ) from e

    def parse_nautilus_order_side(self, order_side: OrderSide) -> bool:
        """Convert Nautilus OrderSide to Hyperliquid boolean format."""
        if order_side == OrderSide.BUY:
            return True
        elif order_side == OrderSide.SELL:
            return False
        else:
            raise RuntimeError(f"Unsupported order side: {order_side}")

    def parse_hyperliquid_order_side(self, is_buy: bool) -> OrderSide:
        """Convert Hyperliquid boolean format to Nautilus OrderSide."""
        return OrderSide.BUY if is_buy else OrderSide.SELL

    def validate_order_type(self, order_type: OrderType) -> bool:
        """Validate if order type is supported by Hyperliquid."""
        return order_type in self.valid_order_types

    def validate_time_in_force(self, time_in_force: TimeInForce) -> bool:
        """Validate if time in force is supported by Hyperliquid."""
        return time_in_force in self.valid_time_in_force

