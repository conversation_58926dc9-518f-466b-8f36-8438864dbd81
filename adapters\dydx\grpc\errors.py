# -------------------------------------------------------------------------------------------------
#  Copyright (C) 2015-2025 Nautech Systems Pty Ltd. All rights reserved.
#  https://nautechsystems.io
#
#  Licensed under the GNU Lesser General Public License Version 3.0 (the "License");
#  You may not use this file except in compliance with the License.
#  You may obtain a copy of the License at https://www.gnu.org/licenses/lgpl-3.0.en.html
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
# -------------------------------------------------------------------------------------------------
"""
Define a dYdX exception thrown by the GRPC client.
"""


class DYDXGRPCError(Exception):
    """
    Define the class for all dYdX specific errors thrown by the GRPC client.
    """

    def __init__(self, code: int | None, message: str) -> None:
        """
        Define the class for all dYdX specific errors thrown by the GRPC client.
        """
        super().__init__(message)
        self.code = code
        self.message = message
